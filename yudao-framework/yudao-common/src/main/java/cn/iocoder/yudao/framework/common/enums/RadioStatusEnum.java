package cn.iocoder.yudao.framework.common.enums;

import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RadioStatusEnum implements ArrayValuable<Integer> {

    TRUE(1, "是"),
    FALSE(0, "否");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(RadioStatusEnum::getStatus).toArray(Integer[]::new);

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static boolean isTrue(Integer status) {
        return ObjUtil.equal(TRUE.status, status);
    }

    public static boolean isFalse(Integer status) {
        return ObjUtil.equal(FALSE.status, status);
    }

}

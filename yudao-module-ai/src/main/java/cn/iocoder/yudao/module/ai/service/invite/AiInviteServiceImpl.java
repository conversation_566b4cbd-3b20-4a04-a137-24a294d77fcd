package cn.iocoder.yudao.module.ai.service.invite;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteRecordDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteUserDO;
import cn.iocoder.yudao.module.ai.dal.mysql.invite.AiInviteRecordMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.invite.AiInviteUserMapper;
import cn.iocoder.yudao.module.ai.util.InvitationUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * @Description: TODO
 * @Date: 2025/1/20 23:21
 * @Author: zhangq
 * @Version: 1.0
 */
@Service
@Slf4j
public class AiInviteServiceImpl implements AiInviteService{

    @Resource
    private AiInviteUserMapper inviteUserMapper;

    @Resource
    private AiInviteRecordMapper inviteRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void creatInviteByRegister(Long userId) {
        // 查询用户
        AiInviteUserDO inviteUser = inviteUserMapper.selectOne(new LambdaQueryWrapperX<AiInviteUserDO>()
                .eq(AiInviteUserDO::getBindUserId, userId)
        );
        if (inviteUser == null){
            inviteUser = new AiInviteUserDO();
            inviteUser.setBindUserId(userId);
            inviteUser.setBindUserTime(LocalDateTime.now());
            inviteUser.setInviteEnabled(true);
            inviteUser.setInviteCoin(0);
            inviteUser.setInviteCode(InvitationUtil.generateInviteCode());
            inviteUserMapper.insert(inviteUser);
        }
    }

    @Override
    public String getInviteCode(Long userId) {
        AiInviteUserDO inviteUser = inviteUserMapper.selectOne(new LambdaQueryWrapperX<AiInviteUserDO>()
                .eq(AiInviteUserDO::getBindUserId, userId)
        );
        if (inviteUser != null){
            return inviteUser.getInviteCode();
        }else{
            // 创建邀请码
            inviteUser = new AiInviteUserDO();
            inviteUser.setBindUserId(userId);
            inviteUser.setBindUserTime(LocalDateTime.now());
            inviteUser.setInviteEnabled(true);
            inviteUser.setInviteCoin(0);
            inviteUser.setInviteCode(InvitationUtil.generateInviteCode());
            inviteUserMapper.insert(inviteUser);
            return inviteUser.getInviteCode();
        }
    }

    // 用户填写邀请码并领取奖励
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fillInviteCode(Long userId, String inviteCode) {
        // 1. 查找邀请码对应的推广员
        AiInviteUserDO inviter = inviteUserMapper.selectByInviteCode(inviteCode);
        if (inviter == null) {
            throw new RuntimeException("Invalid invite code.");
        }

        // 2. 检查用户是否已被邀请过
        AiInviteRecordDO inviteRecord = inviteRecordMapper.selectBySourceId(userId);
        if (inviteRecord != null) {
            throw new RuntimeException("User has already been invited.");
        }

        // 3.1 防止互相邀请
        if (isMutualInvitation(inviter, userId)) {
            throw new RuntimeException("Mutual invitations are not allowed.");
        }

        //3.2 自己不能邀请自己
        if (inviter.getBindUserId().equals(userId)) {
            throw new RuntimeException("You cannot invite yourself.");
        }

        // 4. 更新推广员的邀请记录
        inviter.setInviteCoin(inviter.getInviteCoin() + 10); // 假设奖励10积分
        inviteUserMapper.updateById(inviter);

        // 5. 更新自己的邀请记录
        AiInviteUserDO inviteUser = inviteUserMapper.selectOne(new LambdaQueryWrapperX<AiInviteUserDO>()
                .eq(AiInviteUserDO::getBindUserId, userId)
        );
        if (inviteUser == null){
            inviteUser = new AiInviteUserDO();
            inviteUser.setBindUserId(userId);
            inviteUser.setBindUserTime(LocalDateTime.now());
            inviteUser.setInviteEnabled(true);
            inviteUser.setInviteCoin(0);
            inviteUser.setInviteCode(InvitationUtil.generateInviteCode());
            inviteUserMapper.insert(inviteUser);
        }
        inviteUser.setInviteCoin(inviteUser.getInviteCoin() + 10);
        inviteUserMapper.updateById(inviteUser);

        // 6. 创建佣金记录
        AiInviteRecordDO brokerageRecord = new AiInviteRecordDO();
        brokerageRecord.setUserId(userId);
        brokerageRecord.setSourceUserId(inviter.getBindUserId());
        brokerageRecord.setSourceCoin(10);
        brokerageRecord.setBizId("invite_" + userId);
        brokerageRecord.setBizType(2); // 假设 2 表示邀请佣金
        brokerageRecord.setTitle("Invitation Reward");
        brokerageRecord.setDescription("Reward for inviting user " + userId);
        brokerageRecord.setCoin(10); // 佣金为10
        brokerageRecord.setStatus(1); // 佣金状态：已发放
        brokerageRecord.setInviteCode(inviteCode);
        inviteRecordMapper.insert(brokerageRecord);
    }

    // 检查是否互相邀请
    private boolean isMutualInvitation(AiInviteUserDO inviter, Long inviteeUserId) {
        AiInviteRecordDO reciprocalInvite = inviteRecordMapper.findByInviteeUserId(inviteeUserId);
        return reciprocalInvite != null && reciprocalInvite.getSourceUserId().equals(inviter.getBindUserId());
    }
}

package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 内容写作 对话 DO新增/修改 Request VO")
@Data
public class ContentWriteConversationSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4717")
    private Long id;

    @Schema(description = "上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "模型标志不能为空")
    private String model;

    @Schema(description = "模型编号", example = "31192")
    private Long modelId;

    @Schema(description = "是否置顶", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否置顶不能为空")
    private Boolean pinned;

    @Schema(description = "置顶时间")
    private LocalDateTime pinnedTime;

    @Schema(description = "角色编号", example = "5227")
    private Long roleId;

    @Schema(description = "角色设定")
    private String systemMessage;

    @Schema(description = "温度参数")
    private Double temperature;

    @Schema(description = "对话标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "对话标题不能为空")
    private String title;

    @Schema(description = "采样方法")
    private Double topP;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16872")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

}
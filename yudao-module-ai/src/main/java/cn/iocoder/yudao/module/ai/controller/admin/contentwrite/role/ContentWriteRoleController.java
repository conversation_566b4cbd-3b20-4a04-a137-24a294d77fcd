package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRoleRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteRoleDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.role.ContentWriteRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 内容创作角色")
@RestController
@RequestMapping("/ai/content-write-role")
@Validated
public class ContentWriteRoleController {

    @Resource
    private ContentWriteRoleService contentWriteRoleService;

    @PostMapping("/create")
    @Operation(summary = "创建内容创作角色")
    @PreAuthorize("@ss.hasPermission('ai:content-write-role:create')")
    public CommonResult<Long> createContentWriteRole(@Valid @RequestBody ContentWriteRoleSaveReqVO createReqVO) {
        return success(contentWriteRoleService.createContentWriteRole(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新内容创作角色")
    @PreAuthorize("@ss.hasPermission('ai:content-write-role:update')")
    public CommonResult<Boolean> updateContentWriteRole(@Valid @RequestBody ContentWriteRoleSaveReqVO updateReqVO) {
        contentWriteRoleService.updateContentWriteRole(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除内容创作角色")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:content-write-role:delete')")
    public CommonResult<Boolean> deleteContentWriteRole(@RequestParam("id") Long id) {
        contentWriteRoleService.deleteContentWriteRole(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得内容创作角色")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:content-write-role:query')")
    public CommonResult<ContentWriteRoleRespVO> getContentWriteRole(@RequestParam("id") Long id) {
        AiContentWriteRoleDO contentWriteRole = contentWriteRoleService.getContentWriteRole(id);
        return success(BeanUtils.toBean(contentWriteRole, ContentWriteRoleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得内容创作角色分页")
    @PreAuthorize("@ss.hasPermission('ai:content-write-role:query')")
    public CommonResult<PageResult<ContentWriteRoleRespVO>> getContentWriteRolePage(@Valid ContentWriteRolePageReqVO pageReqVO) {
        PageResult<AiContentWriteRoleDO> pageResult = contentWriteRoleService.getContentWriteRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContentWriteRoleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出内容创作角色 Excel")
    @PreAuthorize("@ss.hasPermission('ai:content-write-role:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContentWriteRoleExcel(@Valid ContentWriteRolePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiContentWriteRoleDO> list = contentWriteRoleService.getContentWriteRolePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "内容创作角色.xls", "数据", ContentWriteRoleRespVO.class,
                        BeanUtils.toBean(list, ContentWriteRoleRespVO.class));
    }

}
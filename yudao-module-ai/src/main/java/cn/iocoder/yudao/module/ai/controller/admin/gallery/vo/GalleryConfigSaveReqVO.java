package cn.iocoder.yudao.module.ai.controller.admin.gallery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - AI 画廊配置新增/修改 Request VO")
@Data
public class GalleryConfigSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "23321")
    private Long id;

    @Schema(description = "消耗算力/次")
    private Integer useCoin;

}
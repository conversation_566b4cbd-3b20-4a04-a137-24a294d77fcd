package cn.iocoder.yudao.module.ai.service.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AiAssistantConversationUpdateMyReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.dal.mysql.assistant.AiAssistantConversationMapper;
import cn.iocoder.yudao.module.ai.service.model.AiYyModelService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CHAT_CONVERSATION_NOT_EXISTS;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.MODEL_NOT_EXISTS;

/**
 * AI 聊天对话 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AiAssistantConversationServiceImpl implements AiAssistantConversationService {

    @Resource
    private AiAssistantConversationMapper assistantConversationMapper;

    @Resource
    private AiYyModelService modelService;
    @Resource
    private AiAssistantRoleService assistantRoleService;

    @Override
    public Long createAssistantConversationMy(AiAssistantConversationCreateMyReqVO createReqVO, Long userId) {
        // 1.1 获得 AiAssistantRoleDO 聊天角色
        AiAssistantRoleDO role = createReqVO.getRoleId() != null ? assistantRoleService.validateAssistantRole(createReqVO.getRoleId())
                : assistantRoleService.getRequiredDefaultAssistantModel();
        // 1.2 获得 AiChatModelDO 聊天模型
        AiYyModelDO model = role != null && role.getModelId() != null ? modelService.validateModel(role.getModelId())
                : null;
        Assert.notNull(model, MODEL_NOT_EXISTS.getMsg());
        //validateChatModel(model);

        // 2. 创建 AiAssistantConversationDO 聊天对话
        AiAssistantConversationDO conversation = new AiAssistantConversationDO().setUserId(userId).setPinned(false)
                .setModelId(model.getId()).setModel(model.getModel()).setTopP(role.getTopP())
                .setTemperature(role.getTemperature()).setMaxTokens(role.getMaxTokens()).setMaxContexts(role.getMaxContexts());
        if (role != null) {
            conversation.setTitle(null != role.getName()? role.getName():AiAssistantConversationDO.TITLE_DEFAULT).setRoleId(role.getId()).setSystemMessage(role.getSystemMessage());
        } else {
            conversation.setTitle(AiAssistantConversationDO.TITLE_DEFAULT);
        }
        assistantConversationMapper.insert(conversation);
        return conversation.getId();
    }

    @Override
    public void updateChatConversationMy(AiAssistantConversationUpdateMyReqVO updateReqVO, Long userId) {
        // 1.1 校验对话是否存在
        AiAssistantConversationDO conversation = validateAssistantConversationExists(updateReqVO.getId());
        if (ObjUtil.notEqual(conversation.getUserId(), userId)) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        // 1.2 校验模型是否存在（修改模型的情况）
        AiYyModelDO model = null;
        if (updateReqVO.getModelId() != null) {
            model = modelService.validateModel(updateReqVO.getModelId());
        }

        // 1.3 校验知识库是否存在
        /*if (updateReqVO.getKnowledgeId() != null) {
            knowledgeService.validateKnowledgeExists(updateReqVO.getKnowledgeId());
        }*/

        // 2. 更新对话信息
        AiAssistantConversationDO updateObj = BeanUtils.toBean(updateReqVO, AiAssistantConversationDO.class);
        if (Boolean.TRUE.equals(updateReqVO.getPinned())) {
            updateObj.setPinnedTime(LocalDateTime.now());
        }
        if (model != null) {
            updateObj.setModel(model.getModel());
        }
        assistantConversationMapper.updateById(updateObj);
    }

    @Override
    public List<AiAssistantConversationDO> getAssistantConversationListByUserId(Long userId) {
        return assistantConversationMapper.selectListByUserId(userId);
    }

    @Override
    public AiAssistantConversationDO getAssistantConversation(Long id) {
        return assistantConversationMapper.selectById(id);
    }

    @Override
    public void deleteAssistantConversationMy(Long id, Long userId) {
        // 1. 校验对话是否存在
        AiAssistantConversationDO conversation = validateAssistantConversationExists(id);
        if (conversation == null || ObjUtil.notEqual(conversation.getUserId(), userId)) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        // 2. 执行删除
        assistantConversationMapper.deleteById(id);
    }

    @Override
    public void deleteAssistantConversationByAdmin(Long id) {
        // 1. 校验对话是否存在
        AiAssistantConversationDO conversation = validateAssistantConversationExists(id);
        if (conversation == null) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        // 2. 执行删除
        assistantConversationMapper.deleteById(id);
    }

    /*private void validateChatModel(AiYyModelDO model) {
        if (ObjectUtil.isAllNotEmpty(model.getTemperature(), model.getMaxTokens(), model.getMaxContexts())) {
            return;
        }
        throw exception(CHAT_CONVERSATION_MODEL_ERROR);
    }*/

    @Override
    public AiAssistantConversationDO validateAssistantConversationExists(Long id) {
        AiAssistantConversationDO conversation = assistantConversationMapper.selectById(id);
        if (conversation == null) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        return conversation;
    }

    @Override
    public void deleteAssistantConversationMyByUnpinned(Long userId) {
        List<AiAssistantConversationDO> list = assistantConversationMapper.selectListByUserIdAndPinned(userId, false);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        assistantConversationMapper.deleteBatchIds(convertList(list, AiAssistantConversationDO::getId));
    }

    @Override
    public PageResult<AiAssistantConversationDO> getAssistantConversationPage(AiAssistantConversationPageReqVO pageReqVO) {
        return assistantConversationMapper.selectAssistantConversationPage(pageReqVO);
    }

    @Override
    public List<AiAssistantConversationDO> getAssistantConversationAppListByUserId(Long userId) {
        return assistantConversationMapper.selectAppListByUserId(userId);
    }

    @Override
    public AiAssistantConversationDO getAssistantConversationById(Long userId) {
        return assistantConversationMapper.selectByUserIdDesc(userId);
    }

}

package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 内容创作分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContentWriteCategoryRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "29933")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "分类名称", example = "王五")
    @ExcelProperty("分类名称")
    private String name;

    @Schema(description = "排序值")
    @ExcelProperty("排序值")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "是否推荐", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("是否推荐")
    private Integer recommend;

}
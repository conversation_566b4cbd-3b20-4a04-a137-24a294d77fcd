package cn.iocoder.yudao.module.ai.service.promptwords;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.promptwords.PromptWordsDO;
import jakarta.validation.Valid;

/**
 * 提示词 Service 接口
 *
 * <AUTHOR>
 */
public interface PromptWordsService {

    /**
     * 创建提示词
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPromptWords(@Valid PromptWordsSaveReqVO createReqVO);

    /**
     * 更新提示词
     *
     * @param updateReqVO 更新信息
     */
    void updatePromptWords(@Valid PromptWordsSaveReqVO updateReqVO);

    /**
     * 删除提示词
     *
     * @param id 编号
     */
    void deletePromptWords(Long id);

    /**
     * 获得提示词
     *
     * @param id 编号
     * @return 提示词
     */
    PromptWordsDO getPromptWords(Long id);

    /**
     * 获得提示词分页
     *
     * @param pageReqVO 分页查询
     * @return 提示词分页
     */
    PageResult<PromptWordsDO> getPromptWordsPage(PromptWordsPageReqVO pageReqVO);

}
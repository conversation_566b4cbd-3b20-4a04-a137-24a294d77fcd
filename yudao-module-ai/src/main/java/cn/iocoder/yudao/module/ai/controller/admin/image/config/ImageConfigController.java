package cn.iocoder.yudao.module.ai.controller.admin.image.config;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.image.config.vo.ImageConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.image.config.vo.ImageConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import cn.iocoder.yudao.module.ai.service.image.config.AiImageConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * @Description: TODO
 * @Date: 2024/12/19 21:34
 * @Author: zhangq
 * @Version: 1.0
 */
@Tag(name = "管理后台 - 绘画基础配置")
@RestController
@RequestMapping("/ai/image-config")
@Validated
public class ImageConfigController {

    @Resource
    private AiImageConfigService imageConfigService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "创建/修改基础配置")
    //@PreAuthorize("@ss.hasPermission('ai:base-info-config:create')")
    public CommonResult<Long> saveOrUpdateImageConfig(@Valid @RequestBody ImageConfigSaveReqVO createReqVO) {
        return success(imageConfigService.saveOrUpdateImageConfig(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得基础配置")
    //@PreAuthorize("@ss.hasPermission('ai:base-info-config:query')")
    public CommonResult<ImageConfigRespVO> getBaseInfoConfig() {
        AiImageConfigDO configDO = imageConfigService.getImageConfig();
        return success(BeanUtils.toBean(configDO, ImageConfigRespVO.class));
    }

}

package cn.iocoder.yudao.module.ai.service.contentwrite.config;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConfigDO;
import jakarta.validation.Valid;

/**
 * AI 内容创作配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ContentWriteConfigService {

    /**
     * 创建AI 内容创作配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContentWriteConfig(@Valid ContentWriteConfigSaveReqVO createReqVO);

    /**
     * 更新AI 内容创作配置
     *
     * @param updateReqVO 更新信息
     */
    void updateContentWriteConfig(@Valid ContentWriteConfigSaveReqVO updateReqVO);

    /**
     * 删除AI 内容创作配置
     *
     * @param id 编号
     */
    void deleteContentWriteConfig(Long id);

    /**
     * 获得AI 内容创作配置
     *
     * @param id 编号
     * @return AI 内容创作配置
     */
    AiContentWriteConfigDO getContentWriteConfig(Long id);

    /**
     * 获得AI 内容创作配置分页
     *
     * @param pageReqVO 分页查询
     * @return AI 内容创作配置分页
     */
    PageResult<AiContentWriteConfigDO> getContentWriteConfigPage(ContentWriteConfigPageReqVO pageReqVO);

    Long saveOrUpdateContentWriteConfig(@Valid ContentWriteConfigSaveReqVO createReqVO);

    AiContentWriteConfigDO getLastOneContentWriteConfig();
}
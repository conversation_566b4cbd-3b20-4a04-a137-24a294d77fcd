package cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

/**
 * AI 音色模型 DO
 *
 */
@Table(name = "ai_audio_model")
@Comment(value = "音色模型")
@Entity
@TableName(value = "ai_audio_model", autoResultMap = true)
@KeySequence("ai_audio_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiAudioModelDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 音色名称
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '音色名称'")
    private String voiceName;
    /**
     * 音色ID
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '音色ID'")
    private String voiceId;

    /**
     * 描述
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '描述'")
    private String description;


    /**
     * 语音模型
     * speech-01-turbo
     * 最新的模型，拥有出色的效果与时延表现。此模型将不定期迭代优化，以提供更优的体验。
     * speech-01-240228
     * 稳定版本的模型，效果出色。稳定版本适合对可能出现的细微音色变化敏感的场景。
     * speech-01-turbo-240228
     * 稳定版本的模型，时延更低。稳定版本适合对可能出现的细微音色变化敏感的场景。
     */
    @Type(JsonType.class)
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '语音模型'")
    private String voiceModel;

    /**
     * 排序值
     */
    @Column(columnDefinition = "int COMMENT '排序值'")
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "int COMMENT '状态'")
    private Integer status;

}

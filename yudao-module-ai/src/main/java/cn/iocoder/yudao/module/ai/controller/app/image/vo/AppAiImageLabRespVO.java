package cn.iocoder.yudao.module.ai.controller.app.image.vo;

/**
 * @Description: TODO
 * @Date: 2025/1/13 15:50
 * @Author: zhangq
 * @Version: 1.0
 */
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 创建图片的响应 VO
 */
@Data
public class AppAiImageLabRespVO {

    @Schema(description = "封面")
    @ExcelProperty("封面")
    private String coverImage;

    /**
     * 名称
     */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyim")
    @ExcelProperty("名称")
    private String name;

    /**
     *  描述
     */
    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是描述")
    @ExcelProperty("描述")
    private String description;
}

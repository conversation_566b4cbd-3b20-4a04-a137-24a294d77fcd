package cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户APP - 虚拟陪伴角色 Response VO")
@Data
public class AppVirtualPartnerRoleRespPageVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "24161")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "点击量", example = "13786")
    private Long clickCount;

    @Schema(description = "封面描述")
    private String coverDesc;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    private String coverUrl;

    @Schema(description = "角色名称", example = "赵六")
    private String name;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;
}
package cn.iocoder.yudao.module.ai.controller.app.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationRespVO;
import cn.iocoder.yudao.module.ai.controller.app.assistant.vo.message.AppAiAssistantMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AiAssistantConversationUpdateMyReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.service.assistant.AiAssistantConversationService;
import cn.iocoder.yudao.module.ai.service.assistant.AiAssistantMessageService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - AI 智能助手对话")
@RestController
@RequestMapping("/ai/assistant/conversation")
@Validated
public class AppAssistantConversationController {

    @Resource
    private AiAssistantConversationService assistantConversationService;
    @Resource
    private AiAssistantMessageService assistantMessageService;

    @PostMapping("/create-my")
    @Operation(summary = "创建【我的】聊天对话")
    public CommonResult<Long> createAssistantConversationMy(@RequestBody @Valid AiAssistantConversationCreateMyReqVO createReqVO) {
        return success(assistantConversationService.createAssistantConversationMy(createReqVO, getLoginUserId()));
    }

    @Hidden
    @PutMapping("/update-my")
    @Operation(summary = "更新【我的】聊天对话")
    public CommonResult<Boolean> updateChatConversationMy(@RequestBody @Valid AiAssistantConversationUpdateMyReqVO updateReqVO) {
        assistantConversationService.updateChatConversationMy(updateReqVO, getLoginUserId());
        return success(true);
    }

    @Hidden
    @GetMapping("/my-list-page")
    @Operation(summary = "获得对话分页", description = "用于【对话管理】菜单-目前没用到")
    public CommonResult<PageResult<AiAssistantConversationRespVO>> getAssistantConversationPage(AiAssistantConversationPageReqVO pageReqVO) {
        Long userId = getLoginUserId();
        if (userId == null) {
            return success(PageResult.empty());
        }
        pageReqVO.setUserId(userId);
        PageResult<AiAssistantConversationDO> pageResult = assistantConversationService.getAssistantConversationPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 拼接关联数据
        Map<Long, Integer> messageCountMap = assistantMessageService.getAssistantMessageCountMap(
                convertList(pageResult.getList(), AiAssistantConversationDO::getId));
        return success(BeanUtils.toBean(pageResult, AiAssistantConversationRespVO.class,
                conversation -> conversation.setMessageCount(messageCountMap.getOrDefault(conversation.getId(), 0))));
    }

    @GetMapping("/my-list")
    @Operation(summary = "获得【我的】聊天对话列表-按时间倒序排-取最新的前30条保证速度")
    public CommonResult<List<AppAiAssistantMessageRespVO>> getAssistantConversationMyList() {
        List<AiAssistantConversationDO> list = assistantConversationService.getAssistantConversationAppListByUserId(getLoginUserId());
        return success(BeanUtils.toBean(list, AppAiAssistantMessageRespVO.class));
    }

    @GetMapping("/get-my")
    @Operation(summary = "获得【我的】聊天对话,最新一条")
    public CommonResult<AppAiAssistantMessageRespVO> getAssistantConversationMy() {
        AiAssistantConversationDO conversation = assistantConversationService.getAssistantConversationById(getLoginUserId());
        if (conversation != null && ObjUtil.notEqual(conversation.getUserId(), getLoginUserId())) {
            conversation = null;
        }
        AppAiAssistantMessageRespVO message = null;
        if (conversation != null){
            message = BeanUtils.toBean(conversation, AppAiAssistantMessageRespVO.class);
            message.setConversationId(conversation.getId());
        }
        if (message == null){
            message = new AppAiAssistantMessageRespVO();
            message.setConversationId(0L);
        }
        return success(message);
    }

}

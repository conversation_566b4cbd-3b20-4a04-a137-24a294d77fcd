package cn.iocoder.yudao.module.ai.controller.admin.gallery.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - AI 公开画廊分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GalleryCommonPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    private String coverUrl;

    @Schema(description = "同款图")
    private List<String> imgUrls;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    private Integer status;

}
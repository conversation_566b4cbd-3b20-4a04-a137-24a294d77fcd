package cn.iocoder.yudao.module.ai.service.invite.inviteconfig;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteConfigDO;
import jakarta.validation.Valid;

/**
 * 邀请设置 Service 接口
 *
 * <AUTHOR>
 */
public interface InviteConfigService {

    /**
     * 创建邀请设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInviteConfig(@Valid InviteConfigSaveReqVO createReqVO);

    /**
     * 更新邀请设置
     *
     * @param updateReqVO 更新信息
     */
    void updateInviteConfig(@Valid InviteConfigSaveReqVO updateReqVO);

    /**
     * 删除邀请设置
     *
     * @param id 编号
     */
    void deleteInviteConfig(Long id);

    /**
     * 获得邀请设置
     *
     * @param id 编号
     * @return 邀请设置
     */
    AiInviteConfigDO getInviteConfig(Long id);

    /**
     * 获得邀请设置分页
     *
     * @param pageReqVO 分页查询
     * @return 邀请设置分页
     */
    PageResult<AiInviteConfigDO> getInviteConfigPage(InviteConfigPageReqVO pageReqVO);

}
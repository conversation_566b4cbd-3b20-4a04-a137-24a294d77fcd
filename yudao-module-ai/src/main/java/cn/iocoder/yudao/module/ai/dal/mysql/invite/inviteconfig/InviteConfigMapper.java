package cn.iocoder.yudao.module.ai.dal.mysql.invite.inviteconfig;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 邀请设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InviteConfigMapper extends BaseMapperX<AiInviteConfigDO> {

    default PageResult<AiInviteConfigDO> selectPage(InviteConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiInviteConfigDO>()
                .betweenIfPresent(AiInviteConfigDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiInviteConfigDO::getImageUrl, reqVO.getImageUrl())
                .orderByDesc(AiInviteConfigDO::getId));
    }

}
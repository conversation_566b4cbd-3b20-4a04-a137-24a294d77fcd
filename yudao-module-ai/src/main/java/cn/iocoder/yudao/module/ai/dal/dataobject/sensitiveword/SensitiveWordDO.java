package cn.iocoder.yudao.module.ai.dal.dataobject.sensitiveword;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * 敏感词 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_sensitive_word")
@Comment(value = "AI敏感词配置")
@Entity
@TableName(value = "ai_sensitive_word", autoResultMap = true)
@KeySequence("ai_sensitive_word_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SensitiveWordDO extends BaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;
    /**
     * 敏感词 组合词用&分割
     */
    @Column(columnDefinition = "varchar(255) COMMENT '敏感词，组合词用&分割'")
    private String name;
    /**
     * 描述
     */
    @Column(columnDefinition = "varchar(255) COMMENT '描述'")
    private String description;

    /**
     * 标签数组
     *
     * 用于实现不同的业务场景下，需要使用不同标签的敏感词。
     * 例如说，tag 有短信、论坛两种，敏感词 "推广" 在短信下是敏感词，在论坛下不是敏感词。
     * 此时，我们会存储一条敏感词记录，它的 name 为"推广"，tag 为短信。
     */
    /*@Type(JsonType.class)
    @Column(columnDefinition = "varchar(255) COMMENT '标签数组'")
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tags;*/

    /**
     * 类型
     */
    @Column(length = 20, columnDefinition = "varchar(20) COMMENT '类型'")
    private String type;

    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT 0 COMMENT '状态'")
    private Integer status;

}

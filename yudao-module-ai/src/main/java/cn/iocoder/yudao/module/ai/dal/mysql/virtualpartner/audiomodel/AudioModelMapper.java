package cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.audiomodel;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioModelDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 音色模型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AudioModelMapper extends BaseMapperX<AiAudioModelDO> {

    default PageResult<AiAudioModelDO> selectPage(AudioModelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiAudioModelDO>()
                .betweenIfPresent(AiAudioModelDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiAudioModelDO::getVoiceId, reqVO.getVoiceId())
                .likeIfPresent(AiAudioModelDO::getVoiceName, reqVO.getVoiceName())
                .orderByDesc(AiAudioModelDO::getId));
    }

    default List<AiAudioModelDO> selectList(Integer status) {
        return selectList(new LambdaQueryWrapperX<AiAudioModelDO>()
                .eq(AiAudioModelDO::getStatus, status)
                .orderByAsc(AiAudioModelDO::getSort));
    }
}
package cn.iocoder.yudao.module.ai.dal.dataobject.model;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.enums.model.AiModelTypeEnum;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * AI 聊天模型 DO
 *
 * 默认聊天模型：{@link #status} 为开启，并且 {@link #sort} 排序第一
 *
 * <AUTHOR>
 * @since 2024/4/24 19:39
 */
@Table(name = "ai_yy_model")
@Comment(value = "AI 咿呀聊天模型")
@Entity
@TableName("ai_yy_model")
@KeySequence("ai_yy_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiYyModelDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId
    private Long id;
    /**
     * API 秘钥编号
     *
     * 关联 {@link AiYyApiKeyDO#getId()}
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT 'API 秘钥编号'")
    private Long keyId;
    /**
     * 模型名称
     */
    @Column(length = 64, columnDefinition = "varchar(64) NOT NULL COMMENT '模型名称'")
    private String name;
    /**
     * 模型标志
     */
    @Column(length = 64, columnDefinition = "varchar(64) NOT NULL COMMENT '模型标志'")
    private String model;
    /**
     * 平台
     *
     * 枚举 {@link AiPlatformEnum}
     */
    @Column(length = 32, columnDefinition = "varchar(32) NOT NULL COMMENT '平台'")
    private String platform;

    /**
     * 排序值
     */
    @Column(columnDefinition = "int NOT NULL COMMENT '排序值'")
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "int NOT NULL COMMENT '状态'")
    private Integer status;

    /**
     * 类型
     *
     * 枚举 {@link AiModelTypeEnum}
     */
    @Column(columnDefinition = "int COMMENT '类型'")
    private Integer type;
}

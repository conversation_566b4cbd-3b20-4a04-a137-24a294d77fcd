package cn.iocoder.yudao.module.ai.service.invite.inviteconfig;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.invite.inviteconfig.InviteConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.INVITE_CONFIG_NOT_EXISTS;

/**
 * 邀请设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InviteConfigServiceImpl implements InviteConfigService {

    @Resource
    private InviteConfigMapper inviteConfigMapper;

    @Override
    public Long createInviteConfig(InviteConfigSaveReqVO createReqVO) {
        // 插入
        AiInviteConfigDO inviteConfig = BeanUtils.toBean(createReqVO, AiInviteConfigDO.class);
        inviteConfigMapper.insert(inviteConfig);
        // 返回
        return inviteConfig.getId();
    }

    @Override
    public void updateInviteConfig(InviteConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateInviteConfigExists(updateReqVO.getId());
        // 更新
        AiInviteConfigDO updateObj = BeanUtils.toBean(updateReqVO, AiInviteConfigDO.class);
        inviteConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteInviteConfig(Long id) {
        // 校验存在
        validateInviteConfigExists(id);
        // 删除
        inviteConfigMapper.deleteById(id);
    }

    private void validateInviteConfigExists(Long id) {
        if (inviteConfigMapper.selectById(id) == null) {
            throw exception(INVITE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public AiInviteConfigDO getInviteConfig(Long id) {
        return inviteConfigMapper.selectById(id);
    }

    @Override
    public PageResult<AiInviteConfigDO> getInviteConfigPage(InviteConfigPageReqVO pageReqVO) {
        return inviteConfigMapper.selectPage(pageReqVO);
    }

}
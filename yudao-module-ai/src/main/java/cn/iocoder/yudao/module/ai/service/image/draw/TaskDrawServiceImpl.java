package cn.iocoder.yudao.module.ai.service.image.draw;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppChuZhanTxtImgReqVO;
import cn.iocoder.yudao.module.ai.controller.app.myapi.vo.AppDrawImageWorksPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyApiKeyDO;
import cn.iocoder.yudao.module.ai.dal.mysql.image.config.AiImageConfigMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.image.draw.TaskDrawMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.image.modelstyle.AiModelStyleMapper;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import cn.iocoder.yudao.module.ai.enums.image.ResolutionPresetEnum;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.iocoder.yudao.module.ai.service.model.AiYyApiKeyService;
import cn.iocoder.yudao.module.ai.service.sensitiveword.SensitiveWordService;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.pay.api.coin.MemberCoinApi;
import cn.iocoder.yudao.module.pay.api.coin.dto.MemberCoinRespDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huashi6.ai.chuzhan.client.ChuZhanApiClientService;
import com.huashi6.ai.chuzhan.client.ChuZhanClientFactory;
import com.huashi6.ai.chuzhan.config.ChuZhanApiProperties;
import com.huashi6.ai.chuzhan.dto.request.ChuZhanTxtImgRequest;
import com.huashi6.ai.chuzhan.dto.response.ChuZhanApiResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.ai.service.CommonService.payMentReduceCoin;
import static cn.iocoder.yudao.module.ai.util.CompressImage.compressImageFromUrl;
import static cn.iocoder.yudao.module.pay.enums.ErrorCodeConstants.MEMBER_CALCULATE_NOT_ENOUGH;

/**
 * @Description: TODO
 * @Date: 2024/5/6 15:12
 * @Author: zhangq
 * @Version: 1.0
 */
@Slf4j
@Service
public class TaskDrawServiceImpl extends ServiceImpl<TaskDrawMapper, AiImageDO> implements TaskDrawService {

    @Resource
    TaskDrawMapper taskDrawMapper;

    @Resource
    private SensitiveWordService sensitiveWordService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private AiYyApiKeyService apiKeyService;

    @Resource
    private MemberCoinApi memberCoinApi;

    @Resource
    private AiModelStyleMapper aiModelStyleMapper;

    @Resource
    private AiImageConfigMapper imageConfigMapper;

    @Resource
    private FileApi fileApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTaskAndBack(Long userId, AppChuZhanTxtImgReqVO params) {
        if (userId == null){
            throw exception(USER_NOT_LOGIN);
        }

        //敏感词审核
        boolean isValid = sensitiveWordService.isTextValid(JSONUtil.toJsonStr(params));
        if (!isValid){
            throw exception(SENSITIVE_WORD_IS_EXISTS);
        }

        //1. 检测是否有正在等待执行和执行中的任务 最大任务数3
        Assert.isFalse(
                super.lambdaQuery().eq(AiImageDO::getUserId, userId)
                        .eq(AiImageDO::getPlatform, params.getPlatform())
                        .in(AiImageDO::getStatus, CollUtil.newArrayList(AiImageStatusEnum.WAITING.getStatus(), AiImageStatusEnum.IN_PROGRESS.getStatus()))
                        .count() >= 3,
                () -> new  ServiceException(AI_DRAW_TASK_IS_QUEUE_FULL)
        );
        //1.1 根据绘画清晰模式查询需要消耗积分数
        String resolutionPreset = params.getResolutionPreset();
        Boolean faceFix = params.getFaceFix();
        Integer batchSize = params.getBatchSize();
        AiImageConfigDO configDO =  imageConfigMapper.selectLast();
        if (ObjUtil.isNull(configDO)){
            throw new ServiceException(AI_IMAGE_CONFIG_NOT_EXISTS);
        }
        if (ObjUtil.isNull(resolutionPreset)){
            throw new ServiceException(AI_IMAGE_RESOLUTION_PRESET_NOT_EXISTS);
        }

        if (ObjUtil.isNull(faceFix)){
            throw new ServiceException(AI_FACE_FIX_NOT_EXISTS);
        }
        if (ObjUtil.isNull(batchSize)){
            throw new ServiceException(AI_IMAGE_BATCH_SIZE_NOT_EXISTS);
        }
        Integer aiCoin = params.getAiCoin();
        if (ObjUtil.isNull(aiCoin) && !params.getPredictConsume()){
            throw new ServiceException(500, "发生错误，积分有误请刷新页面！");
        }

        Integer sumAiCoin = 0;
        //1.1 根据绘画清晰模式查询需要消耗积分数
        if(resolutionPreset.equals(ResolutionPresetEnum.DEFAULT.getCode())){
            // 默认消耗积分 * batchSize
            sumAiCoin = configDO.getDrawBaseConsume() * batchSize;
        }

        if(resolutionPreset.equals(ResolutionPresetEnum.NORMAL.getCode())){
            sumAiCoin = configDO.getHighFixConsume() * batchSize;
        }

        if(resolutionPreset.equals(ResolutionPresetEnum.HIGH.getCode())){
            sumAiCoin = configDO.getHighFixSuperConsume() * batchSize;
        }
        //1.1.1 校验用户是否开启脸部修复
        if (faceFix){
            // 默认消耗积分 * batchSize
            sumAiCoin += configDO.getFaceFixConsume() * batchSize;
        }

        ChuZhanTxtImgRequest apiRequestParam = JSON.parseObject(JSON.toJSONString(params), ChuZhanTxtImgRequest.class);
        if (ResolutionPresetEnum.DEFAULT.getCode().equals(params.getResolutionPreset())){
            Map<String, Integer> imageConfig = getImageConfigByType(params.getImgSize());
            if (ObjUtil.isNull(imageConfig)){
                throw new ServiceException(AI_IMAGE_CONFIG_NOT_EXISTS);
            }
            apiRequestParam.setHeight(imageConfig.get("height"));
            apiRequestParam.setWidth(imageConfig.get("width"));
        }

        //1.2 校验用户算力余额
        MemberCoinRespDTO payCoin =  memberCoinApi.getMemberBlanceCoin(userId);
        if (payCoin.getBalanceCoin() == null || payCoin.getBalanceCoin() <= 0 || payCoin.getBalanceCoin() < sumAiCoin) {
            throw exception(MEMBER_CALCULATE_NOT_ENOUGH,sumAiCoin, payCoin.getBalanceCoin());
        }

        //1.3 检验模型是否存在可用
        AiModelStyleDO aiModelStyleDO = aiModelStyleMapper.selectOne(new LambdaQueryWrapper<AiModelStyleDO>().eq(AiModelStyleDO::getModelStyleId,params.getModelStyleId()));
        if (ObjectUtils.isEmpty(aiModelStyleDO)){
            throw new ServiceException(MODEL_STYLE_NOT_EXISTS);
        }

        // 2. 保存数据库
        AiImageDO image = BeanUtils.toBean(params, AiImageDO.class).setUserId(userId).setPublicStatus(false)
                .setModel("ChuZhan")
                .setStatus(AiImageStatusEnum.WAITING.getStatus()).setModelStyleId(String.valueOf(params.getModelStyleId()))
                .setWidth(apiRequestParam.getWidth())
                .setHeight(apiRequestParam.getHeight())
                .setModelName(aiModelStyleDO.getModelStyleName()).setAiCoin(sumAiCoin);
        taskDrawMapper.insert(image);

        params.setNonce(image.getId().toString());

        Long id = image.getId();

        //获取url
        AiYyApiKeyDO aiApiKeyDO = apiKeyService.getApiKeyDOImageModel(AiPlatformEnum.validatePlatform(params.getPlatform()));
        ChuZhanApiClientService clientService =  ChuZhanClientFactory.createService(aiApiKeyDO.getUrl(), aiApiKeyDO.getApiKey());

        log.info("[绘图 - 触站] 文生图 - api请求 任务ID:{}", id);

        ChuZhanApiResponse response;
        try {
            ChuZhanApiProperties chuZhanApiProperties = ChuZhanClientFactory.getChuZhanApiProperties();
            if (chuZhanApiProperties.getNotifyBack()){
                apiRequestParam.setCallback(chuZhanApiProperties.getNotifyUrl());
            }
            response = clientService.txt2img(apiRequestParam);
            log.info("[绘图 - 触站] 文生图 - api响应:{}", JSON.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[绘图 - 触站] 响应失败： {}", e.getMessage());
            throw e;
        }
        if (!response.isSuccess()){
             //修改任务为失败
             taskDrawMapper.updateById(new AiImageDO().setId(id).setStatus(AiImageStatusEnum.FAIL.getStatus()));
             if (response.getCode() == 1 || response.getCode() == 1001 || response.getCode() == 3 || response.getCode() == 1002){
                 throw new ServiceException(SERVER_ERROR);
             }
            if (response.getCode() == 1003) {
                throw new ServiceException(PARAM_ERROR);
            }
            if (response.getCode() == 1004) {
                throw new ServiceException(SENSITIVE_WORD_ERROR);
            }
            if (response.getCode() == 1005) {
                throw new ServiceException(TASK_NUM_LIMIT_ERROR);
            }
        }

        //请求api
        if (Objects.nonNull(response.getData())){
            if (!params.getPredictConsume()){
                String paintingSign = response.getData().getPaintingSign();
                taskDrawMapper.updateById(
                        new AiImageDO().setId(id)
                                .setTaskId(paintingSign)
                                .setUsed(response.getData().getUsed())
                                .setStatus(AiImageStatusEnum.IN_PROGRESS.getStatus())
                );

                //创建消息
                //MultiDelayMessage<String> msg = MultiDelayMessage.of(String.valueOf(id), ChuZhanClientFactory.getDelayMillis()); //这里的消息体是订单ID，后面是延迟消息时间集合
                //String body = JSONObject.toJSONString(msg);

                //log.info("发送消息：" + JSONObject.toJSONString(msg));
                /*rabbitTemplate.convertAndSend(
                        RabbitMQConfig.DELAYED_EXCHANGE_NAME, RabbitMQConfig.ROUTING_KEY,body,
                        new DelayMessageProcessor(msg.removeNextDelay())
                );*/
            }
        }
        log.info("[绘图 - 触站] 文生图 - api请求 任务ID:{}", id);

        //扣减算力
        //3 扣减用户算力余额 包含更新用户总算力余额
        //PayCoinBizTypeEnum.setDescription(PayCoinBizTypeEnum.PAYMENT.getType(), "AI-实验室-AI绘画");
        //memberCoinApi.reduceCoin(userId, sumAiCoin, PayCoinBizTypeEnum.PAYMENT, id);
        String url = "http://yyim.cc:8090/admin-api/infra/file/4/get/ae2377ba9091f0c9a0bb5e39a6eb3af0bd8e4754e1d283902235bf7daeddc85a.png";
        payMentReduceCoin(userId,sumAiCoin, "AI-实验室-AI绘画", id);
        if (params.getPredictConsume()){
            /*String picUrl = fileApi.createFile(HttpUtil.downloadBytes(url));
            List<String> urls = new ArrayList<>();
            urls.add(picUrl);
            byte[] compressbyte = compressImageFromUrl(url,0.5);
            String compressPicUrl = fileApi.createFile(compressbyte);
            taskDrawMapper.updateById(
                    new AiImageDO().setId(id)
                            .setCompressPicUrl(compressPicUrl)
                            .setStatus(AiImageStatusEnum.SUCCESS.getStatus())
                            .setPicUrl(urls).setErrorMessage("测试")
                            .setFinishTime(LocalDateTime.now())
                            .setUsed(sumAiCoin)
            );*/
            getSelf().testImageProcess(id,sumAiCoin);
        }
        return id;
    }

    /**
     * 测试图片处理
     * @param taskId
     * @param sumAiCoin
     * @return
     */
    @Async
    public void testImageProcess(Long taskId, Integer sumAiCoin) {
        String url = "http://yyim.cc:8090/admin-api/infra/file/4/get/ae2377ba9091f0c9a0bb5e39a6eb3af0bd8e4754e1d283902235bf7daeddc85a.png";
        String picUrl = fileApi.createFile(HttpUtil.downloadBytes(url));
        List<String> urls = new ArrayList<>();
        urls.add(picUrl);
        byte[] compressbyte = compressImageFromUrl(url,0.8);
        String compressPicUrl = fileApi.createFile(compressbyte);
        taskDrawMapper.updateById(
                new AiImageDO().setId(taskId)
                        .setCompressPicUrl(compressPicUrl)
                        .setStatus(AiImageStatusEnum.SUCCESS.getStatus())
                        .setPicUrl(urls).setErrorMessage("测试")
                        .setFinishTime(LocalDateTime.now())
                        .setUsed(sumAiCoin)
        );
    }

    /**
     * 获取图片配置
     * @param imgSize
     * @return
     */
    private Map<String, Integer> getImageConfigByType(String imgSize) {
        AiImageConfigDO aiImageConfigDO = imageConfigMapper.selectLast();
        if (aiImageConfigDO == null) {
            throw new ServiceException(AI_IMAGE_RESOLUTION_PRESET_NOT_EXISTS);
        }

        Map<String, Integer> response = new HashMap<>();
        switch (imgSize) {
            case "portrait":
                response.put("width", aiImageConfigDO.getPortraitWidth());
                response.put("height", aiImageConfigDO.getPortraitHeight());
                break;
            case "square":
                response.put("width", aiImageConfigDO.getSquareWidth());
                response.put("height", aiImageConfigDO.getSquareHeight());
                break;
            case "landscape":
                response.put("width", aiImageConfigDO.getLandscapeWidth());
                response.put("height", aiImageConfigDO.getLandscapeHeight());
                break;
            default:
                return null;
        }
        return response;
    }

    @Override
    public void testMQ(Long taskId) {
        // 1.创建消息
        try{
            //MultiDelayMessage<String> msg = MultiDelayMessage.of(String.valueOf(taskId), 3000L, 7000L); //这里的消息体是订单ID，后面是延迟消息时间集合
            //String body = JSON.toJSONString(msg);

            //log.info("发送消息：" + JSONObject.toJSONString(msg));
            /*rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DELAYED_EXCHANGE_NAME, RabbitMQConfig.ROUTING_KEY,body,
                    new DelayMessageProcessor(msg.removeNextDelay().intValue())
            );*/
        }catch (AmqpException e){
            log.error("延迟消息发送失败！");
        }
    }

    @Override
    public void deleteDrawImage(Long id, Long userId) {
        // 1. 校验绘画存在
        AiImageDO imageDO = taskDrawMapper.selectById(id);
        if (imageDO == null || ObjUtil.notEqual(imageDO.getUserId(), userId)) {
            throw exception(IMAGE_NOT_EXISTS);
        }
        // 2. 执行删除
        taskDrawMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revoke(Long id, Long userId) {
        // 1. 校验绘画存在
        AiImageDO imageDO = taskDrawMapper.selectById(id);
        if (imageDO == null || ObjUtil.notEqual(imageDO.getUserId(), userId)) {
            throw exception(AI_DRAW_TASK_NOT_EXISTS);
        }
        //2. 状态是等待
        if (!imageDO.getStatus().equals(AiImageStatusEnum.WAITING.getStatus())){
            throw exception(IMAGE_REVOKE_ERROR, "任务状态不为待处理");
        }
        taskDrawMapper.updateById(new AiImageDO().setId(id).setStatus(AiImageStatusEnum.REVOKE.getStatus()));
        //memberCoinApi.addCoin(userId, imageDO.getAiCoin(), MemberCoinBusTypeEnum.DRAW.getType(), MemberCoinBizTypeEnum.ORDER_USE_CANCEL.getType(), String.valueOf(id));
    }

    @Override
    public PageResult<AiImageDO> getAppDrawImageWorksPage(AppDrawImageWorksPageReqVO pageReqVO,Long userId) {
        PageResult<AiImageDO> pageResult = taskDrawMapper.selectImagePage(pageReqVO, userId);
        return pageResult;
    }

    @Override
    public int executeDrawTask() {
        // 获得需要通知的任务
        /*List<AiImageDO> tasks = notifyTaskMapper.selectListByNotify();
        if (CollUtil.isEmpty(tasks)) {
            return 0;
        }

        // 遍历，逐个通知
        CountDownLatch latch = new CountDownLatch(tasks.size());
        tasks.forEach(task -> threadPoolTaskExecutor.execute(() -> {
            try {
                executeNotify(task);
            } finally {
                latch.countDown();
            }
        }));
        // 等待完成
        awaitExecuteNotify(latch);
        // 返回执行完成的任务数（成功 + 失败)
        return tasks.size();
        String taskId = msg.getData();
        //1、查询绘画状态
        AiImageDO aiImageDO = taskDrawService.getById(Long.valueOf(taskId));
        if (aiImageDO == null){
            log.error("[MQ] 绘画任务不存在: {} 时间{}",message, LocalDateTime.now());
            return;
        }
        Boolean isFinish = false;
        //1.1 查询AISDK
        AiYyApiKeyDO aiApiKeyDO = apiKeyService.getApiKeyDOImageModel(AiPlatformEnum.validatePlatform(aiImageDO.getPlatform()));
        if ((aiImageDO.getStatus() == AiImageStatusEnum.WAITING.getStatus() || aiImageDO.getStatus() == AiImageStatusEnum.IN_PROGRESS.getStatus()) && StrUtil.isNotBlank(aiImageDO.getTaskId())){

            ChuZhanApiClientService clientService =  ChuZhanClientFactory.createService(aiApiKeyDO.getUrl(), aiApiKeyDO.getApiKey());

            ChuZhanTaskDetailResponse response = null;
            try {
                ChuZhanTaskDetailRequest taskDetailReq =  new ChuZhanTaskDetailRequest();
                taskDetailReq.setTaskId(aiImageDO.getTaskId());
                taskDetailReq.setWaitUtilEnd(true);
                taskDetailReq.setPreview(true);
                response = clientService.getTaskDetail(taskDetailReq);
                log.info("[获取绘画任务详情 - 触站] - api响应:{}", JSON.toJSONString(response));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("[获取绘画任务详情 - 触站] 响应失败： {}", e.getMessage());
                //修改任务为失败
                taskDrawService.updateById(new AiImageDO().setId(aiImageDO.getId()).setStatus(AiImageStatusEnum.FAIL.getStatus())
                        .setErrorMessage(e.getMessage())
                );
            }
            if (null != response && !response.isSuccess()){
                //修改任务为失败
                taskDrawService.updateById(new AiImageDO().setId(aiImageDO.getId()).setStatus(AiImageStatusEnum.FAIL.getStatus())
                        .setErrorMessage(response.getMsg())
                );
            }

            //响应处理
            if (Objects.nonNull(response.getData())){
                //获取绘画结果
                DetailData detailData = response.getData();
                // 1. 转换状态
                Integer status = null;
                String taskMsg = null;
                LocalDateTime finishTime = null;
                if (StrUtil.isNotBlank(detailData.getState())) {
                    ChuZhanTask.TaskStatusEnum taskStatusEnum = null;
                    try {
                        taskStatusEnum = ChuZhanTask.TaskStatusEnum.forValue(detailData.getState());
                    } catch (IOException e) {
                        status = AiImageStatusEnum.FAIL.getStatus();
                        taskMsg = e.getMessage();
                    }
                    if (ChuZhanTask.TaskStatusEnum.SUCCESS == taskStatusEnum) {
                        status = AiImageStatusEnum.SUCCESS.getStatus();
                        finishTime = LocalDateTime.now();
                    } else if (ChuZhanTask.TaskStatusEnum.FAILURE == taskStatusEnum) {
                        status = AiImageStatusEnum.FAIL.getStatus();
                        taskMsg = response.getMsg();
                        finishTime = LocalDateTime.now();
                    } else if (ChuZhanTask.TaskStatusEnum.IN_RUNNING == taskStatusEnum) {
                        status = AiImageStatusEnum.IN_PROGRESS.getStatus();
                    } else if (ChuZhanTask.TaskStatusEnum.IN_QUEUE == taskStatusEnum) {
                        status = AiImageStatusEnum.WAITING.getStatus();
                    }
                }

                // 2. 上传图片
                List<String> picUrls = null;
                if (CollectionUtils.isNotEmpty(detailData.getImages())) {
                    picUrls = new ArrayList<>();
                    String picUrl = null;
                    if (StrUtil.isNotBlank(detailData.getImgUrl())) {
                        try {
                            picUrl = fileApi.createFile(HttpUtil.downloadBytes(detailData.getImgUrl()));
                            picUrls.add(picUrl);
                        } catch (Exception e) {
                            picUrl = detailData.getImgUrl();
                            picUrls.add(picUrl);
                            taskMsg = String.format("图片({}) 地址({}) 上传失败", aiImageDO.getId(), detailData.getImgUrl());
                            log.warn("[listenOderDelayMessage][图片({}) 地址({}) 上传失败]", aiImageDO.getId(), detailData.getImgUrl(), e);
                        }
                    }
                }


                // 3. 更新 image 状态
                AiImageDO update = new AiImageDO().setId(aiImageDO.getId()).setStatus(status)
                        .setPicUrl(picUrls).setErrorMessage(taskMsg)
                        .setFinishTime(finishTime);
                if (CollectionUtils.isNotEmpty(detailData.getImages())){
                    update.setAudit(detailData.getImages().get(0).getAudit());
                }
                taskDrawService.updateById(update);
                isFinish = true;
            }
        }

        //4、判断是否存在延迟时间
        if (msg.hasNextDelay() && !isFinish && StrUtil.isNotBlank(aiImageDO.getTaskId())){
            //4.1、存在,则重发延迟消息
            Long nestDelay = msg.removeNextDelay();
            String body = JSON.toJSONString(msg);
            log.info("[MQ] 重发延迟消息：{} 时间{}", message, LocalDateTime.now());
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DELAYED_EXCHANGE_NAME, RabbitMQConfig.ROUTING_KEY,
                    body, new DelayMessageProcessor(nestDelay)
            );
            isFinish = true;
        }

        //6、恢复用户算力
        if (!isFinish){
            //memberCoinApi.addCoin(aiImageDO.getUserId(), aiImageDO.getAiCoin(), MemberCoinBusTypeEnum.DRAW.getType(), MemberCoinBizTypeEnum.ORDER_USE_CANCEL_ITEM.getType(), aiImageDO.getId().toString());
            //修改任务为失败
            taskDrawService.updateById(new AiImageDO().setId(aiImageDO.getId()).setStatus(AiImageStatusEnum.FAIL.getStatus()));
        }*/
        return 0;
    }


    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private TaskDrawServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}

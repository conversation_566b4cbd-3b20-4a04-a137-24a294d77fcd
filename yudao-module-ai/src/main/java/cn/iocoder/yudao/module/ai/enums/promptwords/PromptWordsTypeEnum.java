package cn.iocoder.yudao.module.ai.enums.promptwords;

import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: TODO
 * @Date: 2024/8/5 21:11
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum PromptWordsTypeEnum implements ArrayValuable<Integer> {

    //绘画提示词
    IMAGE_PROMPT_WORDS(1, "绘画提示词"),
    //绘画质量提示词
    IMAGE_QUALITY_PROMPT_WORDS(2, "绘画质量提示词"),
    //智能助手打招呼
    ASSISTANT_GREETING(3, "智能助手打招呼"),
    //智能助手提示词
    ASSISTANT_PROMPT_WORDS(4, "智能助手提示词"),
    ;

    /**
     * 状态
     */
    private final Integer type;

    /**
     * 状态名
     */
    private final String name;

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(PromptWordsTypeEnum::getType).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}

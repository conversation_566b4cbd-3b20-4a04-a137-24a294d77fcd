package cn.iocoder.yudao.module.ai.service.contentwrite.role;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteRoleDO;
import cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.role.ContentWriteRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CHAT_ROLE_DISABLE;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CONTENT_WRITE_ROLE_NOT_EXISTS;

/**
 * 内容创作角色 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContentWriteRoleServiceImpl implements ContentWriteRoleService {

    @Resource
    private ContentWriteRoleMapper contentWriteRoleMapper;

    @Override
    public Long createContentWriteRole(ContentWriteRoleSaveReqVO createReqVO) {
        // 插入
        AiContentWriteRoleDO contentWriteRole = BeanUtils.toBean(createReqVO, AiContentWriteRoleDO.class);
        contentWriteRoleMapper.insert(contentWriteRole);
        // 返回
        return contentWriteRole.getId();
    }

    @Override
    public void updateContentWriteRole(ContentWriteRoleSaveReqVO updateReqVO) {
        // 校验存在
        validateContentWriteRoleExists(updateReqVO.getId());
        // 更新
        AiContentWriteRoleDO updateObj = BeanUtils.toBean(updateReqVO, AiContentWriteRoleDO.class);
        contentWriteRoleMapper.updateById(updateObj);
    }

    @Override
    public void deleteContentWriteRole(Long id) {
        // 校验存在
        validateContentWriteRoleExists(id);
        // 删除
        contentWriteRoleMapper.deleteById(id);
    }

    private AiContentWriteRoleDO validateContentWriteRoleExists(Long id) {
        AiContentWriteRoleDO contentWriteRole = contentWriteRoleMapper.selectById(id);
        if (contentWriteRoleMapper.selectById(id) == null) {
            throw exception(CONTENT_WRITE_ROLE_NOT_EXISTS);
        }
        return contentWriteRole;
    }

    @Override
    public AiContentWriteRoleDO getContentWriteRole(Long id) {
        return contentWriteRoleMapper.selectById(id);
    }

    @Override
    public PageResult<AiContentWriteRoleDO> getContentWriteRolePage(ContentWriteRolePageReqVO pageReqVO) {
        return contentWriteRoleMapper.selectPage(pageReqVO);
    }

    @Override
    public AiContentWriteRoleDO validateContentWriteRole(Long roleId) {
        AiContentWriteRoleDO role = validateContentWriteRoleExists(roleId);
        if (CommonStatusEnum.isDisable(role.getStatus())) {
            throw exception(CHAT_ROLE_DISABLE, role.getName());
        }
        return role;
    }

}
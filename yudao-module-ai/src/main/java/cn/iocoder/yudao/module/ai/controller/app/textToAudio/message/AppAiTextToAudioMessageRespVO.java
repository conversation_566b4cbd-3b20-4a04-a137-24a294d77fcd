package cn.iocoder.yudao.module.ai.controller.app.textToAudio.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户APP - AI 聊天信息转语音信息 Response VO")
@Data
public class AppAiTextToAudioMessageRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "音频地址",example = "https://www.iocoder.cn/xxxx.mp3")
    private String audioUrl;

    @Schema(description = "对话Id", example = "1")
    private Long messageId;

    @Schema(description = "内容", example = "你好，你好啊")
    private String content;

    @Schema(description = "音频时长,秒。", example = "1.5")
    private Double audioLength;

    @Schema(description = "音频大小", example = "2000")
    private Long audioSize;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-05-12 12:51")
    private LocalDateTime createTime;

    @Schema(description = "非法字符占比", example = "0.1")
    private Double invisibleCharacterRatio;
}

package cn.iocoder.yudao.module.ai.service.image.aimodelstyle;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStylePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStyleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import jakarta.validation.Valid;

/**
 * 风格模型 Service 接口
 *
 * <AUTHOR>
 */
public interface AiModelStyleService {

    /**
     * 创建风格模型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createModelStyle(@Valid AiModelStyleSaveReqVO createReqVO);

    /**
     * 更新风格模型
     *
     * @param updateReqVO 更新信息
     */
    void updateModelStyle(@Valid AiModelStyleSaveReqVO updateReqVO);

    /**
     * 删除风格模型
     *
     * @param id 编号
     */
    void deleteModelStyle(Long id);

    /**
     * 获得风格模型
     *
     * @param id 编号
     * @return 风格模型
     */
    AiModelStyleDO getModelStyle(Long id);

    /**
     * 获得风格模型分页
     *
     * @param pageReqVO 分页查询
     * @return 风格模型分页
     */
    PageResult<AiModelStyleDO> getModelStylePage(AiModelStylePageReqVO pageReqVO);

}
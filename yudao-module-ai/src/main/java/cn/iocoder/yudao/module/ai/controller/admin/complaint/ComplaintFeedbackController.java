package cn.iocoder.yudao.module.ai.controller.admin.complaint;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.complaint.ComplaintFeedbackDO;
import cn.iocoder.yudao.module.ai.service.complaint.ComplaintFeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 投诉/反馈")
@RestController
@RequestMapping("/ai/complaint-feedback")
@Validated
public class ComplaintFeedbackController {

    @Resource
    private ComplaintFeedbackService complaintFeedbackService;

    @PostMapping("/create")
    @Operation(summary = "创建投诉/反馈")
    @PreAuthorize("@ss.hasPermission('ai:complaint-feedback:create')")
    public CommonResult<String> createComplaintFeedback(@Valid @RequestBody ComplaintFeedbackSaveReqVO createReqVO) {
        return success(complaintFeedbackService.createComplaintFeedback(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新投诉/反馈")
    @PreAuthorize("@ss.hasPermission('ai:complaint-feedback:update')")
    public CommonResult<Boolean> updateComplaintFeedback(@Valid @RequestBody ComplaintFeedbackSaveReqVO updateReqVO) {
        complaintFeedbackService.updateComplaintFeedback(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除投诉/反馈")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:complaint-feedback:delete')")
    public CommonResult<Boolean> deleteComplaintFeedback(@RequestParam("id") Long id) {
        complaintFeedbackService.deleteComplaintFeedback(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得投诉/反馈")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:complaint-feedback:query')")
    public CommonResult<ComplaintFeedbackRespVO> getComplaintFeedback(@RequestParam("id") Long id) {
        ComplaintFeedbackDO complaintFeedback = complaintFeedbackService.getComplaintFeedback(id);
        return success(BeanUtils.toBean(complaintFeedback, ComplaintFeedbackRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得投诉/反馈分页")
    @PreAuthorize("@ss.hasPermission('ai:complaint-feedback:query')")
    public CommonResult<PageResult<ComplaintFeedbackRespVO>> getComplaintFeedbackPage(@Valid ComplaintFeedbackPageReqVO pageReqVO) {
        PageResult<ComplaintFeedbackDO> pageResult = complaintFeedbackService.getComplaintFeedbackPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ComplaintFeedbackRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出投诉/反馈 Excel")
    @PreAuthorize("@ss.hasPermission('ai:complaint-feedback:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportComplaintFeedbackExcel(@Valid ComplaintFeedbackPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ComplaintFeedbackDO> list = complaintFeedbackService.getComplaintFeedbackPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "投诉/反馈.xls", "数据", ComplaintFeedbackRespVO.class,
                        BeanUtils.toBean(list, ComplaintFeedbackRespVO.class));
    }

}
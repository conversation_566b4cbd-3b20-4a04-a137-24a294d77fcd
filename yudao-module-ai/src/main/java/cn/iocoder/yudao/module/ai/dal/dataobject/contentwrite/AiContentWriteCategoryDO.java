package cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * @Description: 内容创作分类
 * @Date: 2025/3/2 00:51
 * @Author: zhangq
 * @Version: 1.0
 */
@Table(name = "ai_content_write_category")
@Comment(value = "AI 内容创作分类")
@Entity
@TableName(value = "ai_content_write_category", autoResultMap = true)
@KeySequence("content_write_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiContentWriteCategoryDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分类名称
     */
    @Column(columnDefinition = "varchar(64) DEFAULT '' COMMENT '分类名称'")
    private String name;

    /**
     * 排序值
     */
    @Column(columnDefinition = "int COMMENT '排序值'")
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '状态'")
    private Integer status;

    /**
     * 是否推荐
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '是否推荐'")
    private Integer recommend;
}

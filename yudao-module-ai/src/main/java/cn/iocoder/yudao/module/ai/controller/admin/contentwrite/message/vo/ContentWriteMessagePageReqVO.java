package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - AI 内容创作 消息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContentWriteMessagePageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "算力")
    private Integer aiCoin;

    @Schema(description = "聊天内容")
    private String content;

    @Schema(description = "对话编号", example = "3386")
    private Long conversationId;

    @Schema(description = "输入命中敏感词")
    private Boolean inputSensitive;

    @Schema(description = "输入敏感词类型", example = "2")
    private Integer inputSensitiveType;

    @Schema(description = "模型标志")
    private String model;

    @Schema(description = "模型编号", example = "12500")
    private Long modelId;

    @Schema(description = "输出命中敏感词")
    private Boolean outputSensitive;

    @Schema(description = "输出敏感词类型", example = "2")
    private Integer outputSensitiveType;

    @Schema(description = "点赞类型 点赞1 点踩2", example = "2")
    private Integer praiseType;

    @Schema(description = "回复消息编号", example = "26860")
    private Long replyId;

    @Schema(description = "请求完整参数")
    private String requestParams;

    @Schema(description = "响应完整参数")
    private String responseParams;

    @Schema(description = "消息类型", example = "2")
    private String type;

    @Schema(description = "是否携带上下文")
    private Boolean useContext;

    @Schema(description = "用户编号", example = "18130")
    private Long userId;

}
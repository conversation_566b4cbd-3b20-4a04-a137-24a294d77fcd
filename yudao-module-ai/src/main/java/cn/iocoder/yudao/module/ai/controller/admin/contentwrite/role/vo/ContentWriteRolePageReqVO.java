package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 内容创作角色分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContentWriteRolePageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "内容创作分类id", example = "1")
    private Long categoryId;

    //@Schema(description = "聊天背景图", example = "https://www.iocoder.cn")
    //private String backgroundUrl;

    @Schema(description = "点击量", example = "15571")
    private Long clickCount;

    @Schema(description = "开场白")
    private String cover;

    @Schema(description = "描述")
    private String descContent;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    private String coverUrl;

    //@Schema(description = "开场白语音文件")
    //private String coverVoiceFile;

    @Schema(description = "输入框预置词")
    private String inputPresetWord;

    @Schema(description = "上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "模型版本")
    private String model;

    @Schema(description = "模型编号", example = "21907")
    private Long modelId;

    @Schema(description = "角色名称", example = "王五")
    private String name;

    @Schema(description = "模型平台")
    private String platform;

    @Schema(description = "预设示例")
    private List<String> presetExample;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "角色设定")
    private String systemMessage;

    @Schema(description = "温度参数")
    private Double temperature;

    @Schema(description = "采样方法")
    private Double topP;

}
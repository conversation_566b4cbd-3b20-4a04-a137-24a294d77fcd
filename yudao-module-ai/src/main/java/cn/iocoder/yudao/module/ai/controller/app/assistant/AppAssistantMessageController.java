package cn.iocoder.yudao.module.ai.controller.app.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageSendReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageSendRespVO;
import cn.iocoder.yudao.module.ai.controller.app.assistant.vo.message.AppAssistantMessagePraiseReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantMessageDO;
import cn.iocoder.yudao.module.ai.service.assistant.AiAssistantConversationService;
import cn.iocoder.yudao.module.ai.service.assistant.AiAssistantMessageService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户APP - 智能助手消息")
@RestController
@RequestMapping("/ai/assistant/message")
@Slf4j
public class AppAssistantMessageController {

    @Resource
    private AiAssistantMessageService assistantMessageService;
    @Resource
    private AiAssistantConversationService assistantConversationService;

    @Operation(summary = "发送消息（段式）", description = "一次性返回，响应较慢")
    @PostMapping("/send")
    public CommonResult<AiAssistantMessageSendRespVO> appSendAssistantMessage(@Valid @RequestBody AiAssistantMessageSendReqVO sendReqVO) {
        return success(assistantMessageService.sendMessage(sendReqVO, getLoginUserId()));
    }

    @Operation(summary = "发送消息（流式）", description = "流式返回，响应较快")
    @PostMapping(value = "/send-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PermitAll // 解决 SSE 最终响应的时候，会被 Access Denied 拦截的问题
    public Flux<CommonResult<AiAssistantMessageSendRespVO>> appSendAssistantMessageStream(@Valid @RequestBody AiAssistantMessageSendReqVO sendReqVO) {
        return assistantMessageService.sendAssistantMessageStream(sendReqVO, getLoginUserId());
    }

    @Operation(summary = "获得指定对话的消息列表-分页")
    @GetMapping("/list-by-conversation-id")
    @Parameter(name = "conversationId", required = true, description = "对话编号", example = "1024")
    public CommonResult<PageResult<AiAssistantMessageRespVO>> getAssistantMessageListByConversationId(AiAssistantMessagePageReqVO pageReqVO) {
        AiAssistantConversationDO conversation = assistantConversationService.getAssistantConversation(pageReqVO.getConversationId());
        if (conversation == null || ObjUtil.notEqual(conversation.getUserId(), getLoginUserId())) {
            return success(PageResult.empty());
        }
        PageResult<AiAssistantMessageDO> messagePage = assistantMessageService.getAssistantMessagePageByConversationId(pageReqVO);
        if (CollUtil.isEmpty(messagePage.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(messagePage, AiAssistantMessageRespVO.class));
    }

    @Hidden
    @Operation(summary = "删除消息")
    @DeleteMapping("/delete")
    @Parameter(name = "id", required = true, description = "消息编号", example = "1024")
    public CommonResult<Boolean> deleteChatMessage(@RequestParam("id") Long id) {
        assistantMessageService.deleteAssistantMessage(id, getLoginUserId());
        return success(true);
    }

    @Hidden
    @Operation(summary = "删除指定对话的消息")
    @DeleteMapping("/delete-by-conversation-id")
    @Parameter(name = "conversationId", required = true, description = "对话编号", example = "1024")
    public CommonResult<Boolean> deleteAssistantMessageByConversationId(@RequestParam("conversationId") Long conversationId) {
        assistantMessageService.deleteAssistantMessageByConversationId(conversationId, getLoginUserId());
        return success(true);
    }

    /**
     * 消息点赞/点踩
     */
    @Operation(summary = "点赞/点踩")
    @PostMapping("/praise")
    public CommonResult<Boolean> praise(@Valid @RequestBody AppAssistantMessagePraiseReqVO reqVO) {
        assistantMessageService.praiseAssistantMessage(reqVO);
        return success(true);
    }
}

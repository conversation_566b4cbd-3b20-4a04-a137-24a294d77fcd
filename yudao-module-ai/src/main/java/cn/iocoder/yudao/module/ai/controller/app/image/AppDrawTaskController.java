package cn.iocoder.yudao.module.ai.controller.app.image;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppChuZhanTxtImgReqVO;
import cn.iocoder.yudao.module.ai.service.image.draw.TaskDrawService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping( "/ai/image")
@Tag(name = "用户 APP - 绘画控制器")
public class AppDrawTaskController {

    private final TaskDrawService taskDrawService;

    @Operation(summary = "创建绘图任务-直接返回绘图ID")
    @PostMapping("/createTaskAndBack")
    public CommonResult<Long> createTaskAndBack(@RequestBody AppChuZhanTxtImgReqVO params) {
        Long taskId = taskDrawService.createTaskAndBack(getLoginUserId(),params);
        return success(taskId);
    }


    /**
     * mq测试
     */
    @Hidden
    @Operation(summary = "mq测试")
    @PostMapping("/testmq")
    public CommonResult<Boolean> testMQ(@RequestParam Long taskId ) {
        taskDrawService.testMQ(taskId);
        return success(true);
    }

    @Hidden
    @Operation(summary = "删除任务")
    @DeleteMapping("/delete")
    @Parameter(name = "id", required = true, description = "任务编号", example = "1024")
    public CommonResult<Boolean> baseDeleteByIds(@RequestParam("id") Long id) {
        taskDrawService.deleteDrawImage(id, getLoginUserId());
        return success(true);
    }

    /**
     * 更具id撤回任务
     */
    @Hidden
    @Operation(summary = "撤回任务")
    @PostMapping("/revoke")
    @Parameter(name = "id", required = true,description = "任务编号", example = "1024")
    public CommonResult<Boolean> revoke(@RequestParam("id") Long id) {
        taskDrawService.revoke(id, getLoginUserId());
        return success(true);
    }


    /**
     * 绘图任务分页查询
     * @param param
     * @return
     */
    /*@Operation(summary = "绘图任务分页查询")
    @PostMapping("/page")
    public CommonResult<PageResult<AiImageDO>> getDrawImagePage(@RequestBody AiImageDO param) {
        PageResult<AiImageDO> pageResult = null;//taskDrawService.getDrawImagePage(PageUtil.pageBean(param), param);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(pageResult);
    }*/

}
package cn.iocoder.yudao.module.ai.service.image.config;

import cn.iocoder.yudao.module.ai.controller.admin.image.config.vo.ImageConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import jakarta.validation.Valid;

/**
 * @Description: AI绘画配置 Service 接口
 * @Date: 2024/12/18 23:06
 * @Author: zhangq
 * @Version: 1.0
 */
public interface AiImageConfigService{

    /**
     * 创建/修改配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveOrUpdateImageConfig(@Valid ImageConfigSaveReqVO createReqVO);

    /**
     * 获得配置
     * @return
     */
    AiImageConfigDO getImageConfig();
}

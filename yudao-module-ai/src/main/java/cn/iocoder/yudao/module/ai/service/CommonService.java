package cn.iocoder.yudao.module.ai.service;

import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.module.infra.api.websocket.WebSocketSenderApi;
import cn.iocoder.yudao.module.pay.api.coin.MemberCoinApi;
import cn.iocoder.yudao.module.pay.api.coin.dto.MemberCoinRespDTO;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pay.enums.ErrorCodeConstants.MEMBER_CALCULATE_NOT_ENOUGH;

/**
 * @Description: TODO
 * @Date: 2024/12/27 23:28
 * @Author: zhangq
 * @Version: 1.0
 */
@Slf4j
@Component
public class CommonService {

    // 通过 ApplicationContext 动态获取 Bean
    private static WebSocketSenderApi webSocketSenderApi;
    private static MemberCoinApi memberCoinApi;

    // 通过 ApplicationContext 获取这些 Bean
    @Resource
    public void setApplicationContext(ApplicationContext applicationContext) {
        webSocketSenderApi = applicationContext.getBean(WebSocketSenderApi.class);
        memberCoinApi = applicationContext.getBean(MemberCoinApi.class);
    }

    @Async
    public void sendAsyncMessageToMember(Long userId, String messageType, Object content) {
        webSocketSenderApi.sendObject(UserTypeEnum.MEMBER.getValue(), userId, messageType, content);
    }

    //校验用户算力余额
    public static void checkUserCoin(Long userId, Integer useCoin) {
        MemberCoinRespDTO payCoin =  memberCoinApi.getMemberBlanceCoin(userId);
        if (payCoin.getBalanceCoin() == null || payCoin.getBalanceCoin() <= 0 || payCoin.getBalanceCoin() < useCoin) {
            throw exception(MEMBER_CALCULATE_NOT_ENOUGH,useCoin, payCoin.getBalanceCoin());
        }
    }

    //扣减算力
    public static void payMentReduceCoin(Long userId, Integer userCoin, String description, Long bizId) {
        log.info("开始扣减算力，userId:{},userCoin:{},description:{},bizId:{}", userId, userCoin, description, bizId);
        PayCoinBizTypeEnum.setDescription(PayCoinBizTypeEnum.PAYMENT.getType(), description);
        MemberCoinRespDTO memberCoinRespDTO = memberCoinApi.reduceCoin(userId, userCoin, PayCoinBizTypeEnum.PAYMENT, bizId);
        log.info("扣减算力成功，memberCoinRespDTO:{}", memberCoinRespDTO);
        webSocketSenderApi.sendObject(UserTypeEnum.MEMBER.getValue(), userId, "reduce_coin", memberCoinRespDTO.getBalanceCoin());
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private CommonService getSelf() {
        return SpringUtil.getBean(getClass());
    }

}

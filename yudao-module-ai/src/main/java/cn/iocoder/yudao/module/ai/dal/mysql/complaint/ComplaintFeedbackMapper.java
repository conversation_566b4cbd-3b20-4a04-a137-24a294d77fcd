package cn.iocoder.yudao.module.ai.dal.mysql.complaint;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.complaint.ComplaintFeedbackDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ComplaintFeedbackMapper extends BaseMapperX<ComplaintFeedbackDO> {

    default PageResult<ComplaintFeedbackDO> selectPage(ComplaintFeedbackPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ComplaintFeedbackDO>()
                .betweenIfPresent(ComplaintFeedbackDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ComplaintFeedbackDO::getContent, reqVO.getContent())
                .eqIfPresent(ComplaintFeedbackDO::getFeedbackCategory, reqVO.getFeedbackCategory())
                .eqIfPresent(ComplaintFeedbackDO::getImages, reqVO.getImages())
                .eqIfPresent(ComplaintFeedbackDO::getFeedbackType, reqVO.getFeedbackType())
                .eqIfPresent(ComplaintFeedbackDO::getNo, reqVO.getNo())
                .eqIfPresent(ComplaintFeedbackDO::getType, reqVO.getType())
                .eqIfPresent(ComplaintFeedbackDO::getResourceId, reqVO.getResourceId())
                .eqIfPresent(ComplaintFeedbackDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ComplaintFeedbackDO::getResourceType, reqVO.getResourceType())
                .orderByDesc(ComplaintFeedbackDO::getId));
    }
}

package cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.virtualpartner;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 虚拟陪伴角色 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiVirtualPartnerConversationMapper extends BaseMapperX<AiVirtualPartnerConversationDO> {

    default List<AiVirtualPartnerConversationDO> selectAppListByUserId(Long userId){
        return selectList(new LambdaQueryWrapperX<AiVirtualPartnerConversationDO>()
                .eq(AiVirtualPartnerConversationDO::getUserId, userId)
                .orderByDesc(AiVirtualPartnerConversationDO::getCreateTime)
                .last("LIMIT 30")
        );
    }

    default AiVirtualPartnerConversationDO selectByUserIdDesc(Long userId){
        return selectOne(new LambdaQueryWrapperX<AiVirtualPartnerConversationDO>()
                .eq(AiVirtualPartnerConversationDO::getUserId, userId)
                .orderByDesc(AiVirtualPartnerConversationDO::getId)
                .last("LIMIT 1")
        );
    };
}
package cn.iocoder.yudao.module.ai.dal.dataobject.image;

import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiModelDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import cn.iocoder.yudao.module.ai.framework.ai.core.model.midjourney.api.MidjourneyApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.springframework.ai.openai.OpenAiImageOptions;
import org.springframework.ai.stabilityai.api.StabilityAiImageOptions;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AI 绘画 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_image")
@Comment(value = "AI 绘画")
@Entity
@TableName(value = "ai_image", autoResultMap = true)
@KeySequence("ai_image_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiImageDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户编号
     *
     * 关联 {@link AdminUserRespDTO#getId()}
     */
    @Column(columnDefinition = "bigint COMMENT '用户id'")
    private Long userId;

    /**
     * 提示词
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '提示词'")
    private String prompt;

    /**
     * 平台
     *
     * 枚举 {@link cn.iocoder.yudao.framework.ai.core.enums.AiPlatformEnum}
     */
    @Column(columnDefinition = "varchar(255) COMMENT '平台'")
    private String platform;

    /**
     * 模型
     *
     * 冗余 {@link AiYyModelDO#getModel()}
     */
    @Column(columnDefinition = "varchar(255) COMMENT '平台'")
    private Long modelId;

    /**
     * 模型标识
     *
     * 冗余 {@link AiModelDO#getModel()}
     */
    @Column(columnDefinition = "varchar(255) COMMENT '平台'")
    private String model;

    /**
     * 风格模型
     *
     * 冗余 {@link AiModelStyleDO#getModelStyleId()}
     */
    @Column(columnDefinition = "varchar(255) COMMENT '风格模型'")
    private String modelStyleId;

    /**
     * 模型名称
     *
     * 枚举 {@link AiModelStyleDO#getModelStyleName()}
     */
    @Column(columnDefinition = "varchar(255) COMMENT '绘画类型'")
    private String modelName;

    /**
     * 图片宽度
     */
    @Column(columnDefinition = "int COMMENT '图片宽度'")
    private Integer width;
    /**
     * 图片高度
     */
    @Column(columnDefinition = "int COMMENT '图片高度'")
    private Integer height;

    /**
     * 生成状态
     *
     * 枚举 {@link AiImageStatusEnum}
     */
    @Column(columnDefinition = "int COMMENT '生成状态'")
    private Integer status;

    /**
     * 完成时间
     */
    @Column(columnDefinition = "datetime COMMENT '完成时间'")
    private LocalDateTime finishTime;

    /**
     * 绘画错误信息
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '绘画错误信息'")
    private String errorMessage;

    /**
     * 压缩图片地址
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '压缩图片地址'")
    private String compressPicUrl;

    /**
     * 原图片地址
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "text COMMENT '图片地址'")
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> picUrl;
    /**
     * 是否公开
     */
    @Column(columnDefinition = "bit(1) COMMENT '是否公开'")
    private Boolean publicStatus;

    /**
     * 绘制参数，不同 platform 的不同参数
     *
     * 1. {@link OpenAiImageOptions}
     * 2. {@link StabilityAiImageOptions}
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "json COMMENT '绘制参数'")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> options;

    /**
     * mj buttons 按钮
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "json COMMENT 'mj buttons 按钮'")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<MidjourneyApi.Button> buttons;

    /**
     * 任务编号
     *
     * 1. midjourney proxy：关联的 task id
     * 2. 触站：关联的 task id  paintingSign
     */
    @Column(columnDefinition = "varchar(255) COMMENT '任务编号'")
    private String taskId;

    /**
     * 请求完整参数
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "json COMMENT '请求完整参数'")
    private Object requestParams;

    //本次扣减积分
    @Column(columnDefinition = "int COMMENT '本次扣减积分'")
    private Integer used;

    /**
     * 图片审核结果
     * 0:违规图片（此类型图片不会返回，仅会返回一个表示违规的图片url）
     *
     * 1:图片无风险(可能会包含轻微性感图片)
     *
     * 2:图片最好是人工再进行确认是否需要展示，
     *
     * 3:高风险图片，建议仅私密性展示，不要对外公开展示
     */
    @Column(columnDefinition = "int COMMENT '图片审核结果'")
    private Integer audit;

    /**
     * 算力
     */
    @Column(columnDefinition = "int COMMENT '算力'")
    private Integer aiCoin;

}
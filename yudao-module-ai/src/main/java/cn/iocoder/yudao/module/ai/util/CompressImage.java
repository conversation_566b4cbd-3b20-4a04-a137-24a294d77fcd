package cn.iocoder.yudao.module.ai.util;

import com.alibaba.fastjson.JSON;
import com.huashi6.ai.chuzhan.dto.request.taskDetail.ImgFormatEnum;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.Base64;

/**
 * @Description: TODO
 * @Date: 2025/1/13 23:37
 * @Author: zhangq
 * @Version: 1.0
 */
@Slf4j
public class CompressImage {

    /**
     * 压缩图片
     */
    /**
     * 按比例压缩网络图片并返回字节数组
     *
     * @param imageUrl 网络图片的URL
     * @param scale    压缩比例（例如 0.5 表示压缩到原来的一半）
     * @return 压缩后的图片字节数组
     * @throws IOException 如果读取图片或写入字节流时失败
     */
    public static byte[] compressImageFromUrl(String imageUrl, double scale){
        try {
            // 从网络加载图片
            BufferedImage originalImage = ImageIO.read(new URL(imageUrl));

            // 按比例压缩图片
            BufferedImage resizedImage = resizeImage(originalImage, scale);
            // 将压缩后的图片转换为字节数组
            return convertImageToByteArray(resizedImage);
        }catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] compressImage(String imageUrl, double scale){
        try {
            // 从网络加载图片
            BufferedImage originalImage = ImageIO.read(new URL(imageUrl));

            // 按比例压缩图片
            BufferedImage resizedImage = resizeImage(originalImage, scale);
            // 将压缩后的图片转换为字节数组
            return convertImageToByteArray(resizedImage);
        }catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }



    /**
     * 按比例压缩图片
     *
     * @param originalImage 原始图片
     * @param scale         压缩比例
     * @return 压缩后的图片
     */
    private static BufferedImage resizeImage(BufferedImage originalImage, double scale) {
        int newWidth = (int) (originalImage.getWidth() * scale);
        int newHeight = (int) (originalImage.getHeight() * scale);

        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * 将 BufferedImage 转换为字节数组
     *
     * @param image 压缩后的图片
     * @return 图片的字节数组
     * @throws IOException 如果写入字节流失败
     */
    private static byte[] convertImageToByteArray(BufferedImage image) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 判断图片是否包含透明度
        String format;
        log.info("图片是否包含透明度：{}", JSON.toJSONString(image.getAlphaRaster()));
        log.info("图片是否包含透明度：{}", image.getAlphaRaster());
        if (image.getAlphaRaster() != null) {
            // 如果包含 alpha 通道，使用 PNG 格式
            format = ImgFormatEnum.PNG.toValue();
        } else {
            // 否则，使用 JPG 格式
            format = ImgFormatEnum.JPEG.toValue();
        }
        log.info("图片格式：{}", format);
        ImageIO.write(image, format, byteArrayOutputStream);  // 使用 jpg 格式，可以根据需要修改为 png
        return byteArrayOutputStream.toByteArray();
    }

    public static String encodeImageToBase64(String imagePath) throws IOException {
        // 读取图片文件
        File file = new File(imagePath);
        FileInputStream fileInputStream = new FileInputStream(file);

        // 将文件内容读取到字节数组
        byte[] fileBytes = new byte[(int) file.length()];
        fileInputStream.read(fileBytes);
        fileInputStream.close();

        // 将字节数组编码为 Base64 字符串
        return Base64.getEncoder().encodeToString(fileBytes);
    }

    /**
     * 图片无损压缩
     * @param image
     * @return
     */
    public String compressImage(String image) {
        // 实现图片无损压缩的逻辑

        //1.读取文件
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = convert(image);
        } catch (IOException e) {
            log.info("base64转换BufferedImage失败");
        }
        //压缩文件
        if (bufferedImage != null) {
            //模拟压缩 压缩质量（0.0f 最差质量，1.0f 最佳质量） 0.8
            bufferedImage = compressImage(bufferedImage, 0.8);
        }
        //保存文件
        if (bufferedImage != null) {
//            try {
//                saveImage(bufferedImage,downPath);
//            } catch (IOException e) {
//                log.info("图片压缩后保存失败");
//            }

            try {
                image = convertImageToBase64(bufferedImage);
            } catch (Exception e) {
                log.info("BufferedImage转base64失败");
            }
        }

        return image;
    }


    //base64转BufferedImage
    public static BufferedImage convert(String base64Image) throws IOException {
        // 解码Base64字符串
        byte[] imageBytes = Base64.getDecoder().decode(base64Image);
        // 创建字节数组输入流
        ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
        // 通过ImageIO读取图片
        BufferedImage image = ImageIO.read(bis);
        // 关闭输入流
        bis.close();
        return image;
    }

    // 进行无损压缩
    public BufferedImage compressImage(BufferedImage originalImage, double scale) {
        int newWidth = (int) (originalImage.getWidth() * scale);
        int newHeight = (int) (originalImage.getHeight() * scale);

        // 创建压缩后的图片
        BufferedImage compressedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = compressedImage.createGraphics();

        // 绘制原始图片到压缩后的图片上
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose(); // 释放资源

        return compressedImage;
    }


    //BufferedImage转base64
    public String convertImageToBase64(BufferedImage image) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", outputStream); // 假设我们使用PNG格式
        byte[] imageBytes = outputStream.toByteArray();
        String base64String = Base64.getEncoder().encodeToString(imageBytes);
        outputStream.close();
        return base64String;
    }

    //===============
    /**
     * 通过BufferedImage图片流调整图片大小
     */
    public static BufferedImage resizeImageOne(BufferedImage originalImage, int targetWidth, int targetHeight) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Thumbnails.of(originalImage)
                .size(targetWidth, targetHeight)
                .outputFormat("JPEG")
                .outputQuality(1)
                .toOutputStream(outputStream);
        byte[] data = outputStream.toByteArray();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
        return ImageIO.read(inputStream);
    }

    /**
     * BufferedImage图片流转byte[]数组
     */
    public static byte[] imageToBytes(BufferedImage bImage) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            ImageIO.write(bImage, "jpg", out);
        } catch (IOException e) {
            log.error("错误信息: ", e);
        }
        return out.toByteArray();
    }


    /**
     * byte[]数组转BufferedImage图片流
     */
    private static BufferedImage bytesToBufferedImage(byte[] ImageByte) {
        ByteArrayInputStream in = new ByteArrayInputStream(ImageByte);
        BufferedImage image = null;
        try {
            image = ImageIO.read(in);
        } catch (IOException e) {
            log.error("错误信息: ", e);
        }
        return image;
    }

    public String resizeImage(String image, int width, int height) {
        // 实现图片大小调整的逻辑

        //1.读取文件
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = convert(image);
        } catch (IOException e) {
            log.info("base64转换BufferedImage失败");
        }

        try {
            bufferedImage = resizeImageOne(bufferedImage, width, height);
        } catch (Exception e) {
            log.info("图片大小调整失败");
        }

        //保存文件
        if (bufferedImage != null) {
//            try {
//                saveImage(bufferedImage,downPath);
//            } catch (IOException e) {
//                log.info("图片压缩后保存失败");
//            }

            try {
                image = convertImageToBase64(bufferedImage);
            } catch (Exception e) {
                log.info("BufferedImage转base64失败");
            }
        }

        return image;
    }

    /**
     * 验证文件是否为PDF或图片（JPG/PNG）格式。
     *
     * @param filePath 文件的路径。
     * @return 如果文件为PDF或图片格式则返回true，否则返回false。
     */
    public static boolean isValidFileType(String filePath) {
        // 创建File对象
        File file = new File(filePath);

        // 获取文件扩展名
        String extension = getExtension(file);

        // 验证扩展名
        return "pdf".equalsIgnoreCase(extension) ||
                "jpg".equalsIgnoreCase(extension) ||
                "jpeg".equalsIgnoreCase(extension) ||
                "png".equalsIgnoreCase(extension);
    }

    /**
     * 获取文件的扩展名。
     *
     * @param file 文件对象。
     * @return 文件的扩展名。
     */
    private static String getExtension(File file) {
        String fileName = file.getName();
        int lastIndexOfDot = fileName.lastIndexOf('.');
        if (lastIndexOfDot == -1) {
            return ""; // 没有扩展名
        }
        return fileName.substring(lastIndexOfDot + 1);
    }

}

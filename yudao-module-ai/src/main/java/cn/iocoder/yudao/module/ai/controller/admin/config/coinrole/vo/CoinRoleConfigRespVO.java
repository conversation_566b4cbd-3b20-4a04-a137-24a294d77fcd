package cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI算力规则配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CoinRoleConfigRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "6148")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 算力细则
     */
    @Schema(description = "算力细则")
    @ExcelProperty("算力细则")
    private String coinRole;

    /**
     * 算力说明
     */
    @Schema(description = "算力说明")
    @ExcelProperty("算力说明")
    private String coinRoleDesc;
}
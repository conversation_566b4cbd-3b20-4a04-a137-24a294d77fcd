package cn.iocoder.yudao.module.ai.controller.app.complaint;

import cn.iocoder.yudao.framework.common.biz.system.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackSaveReqVO;
import cn.iocoder.yudao.module.ai.service.complaint.ComplaintFeedbackService;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户APP - 投诉/反馈")
@RestController
@RequestMapping("/ai/complaint-feedback")
@Validated
public class AppComplaintFeedbackController {

    @Resource
    private ComplaintFeedbackService complaintFeedbackService;

    @Resource
    private DictDataApi dataApi;

    /**
     * 获取反馈类型列表
     * @return
     */
    @PostMapping("/getFeedbackTypeList")
    @Operation(summary = "获取反馈类型列表")
    public CommonResult<List<DictDataRespDTO>> getFeedbackTypeList() {
        return success(dataApi.getDictDataList("feedback_type"));
    }

    /**
     * 获取反馈类目列表 feedback_category
     * @return
     */
    @PostMapping("/getFeedbackCategoryList")
    @Operation(summary = "获取反馈类目列表")
    public CommonResult<List<DictDataRespDTO>> getFeedbackCategoryList() {
        return success(dataApi.getDictDataList("feedback_category"));
    }

    /**
     * 创建投诉/反馈
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "创建投诉/反馈")
    public CommonResult<String> saveComplaintFeedback(@Valid @RequestBody ComplaintFeedbackSaveReqVO createReqVO) {
        return success(complaintFeedbackService.createAppComplaintFeedback(getLoginUserId(),createReqVO));
    }

}
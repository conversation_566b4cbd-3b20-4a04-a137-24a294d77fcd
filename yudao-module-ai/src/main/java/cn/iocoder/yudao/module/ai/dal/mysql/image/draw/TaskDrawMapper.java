package cn.iocoder.yudao.module.ai.dal.mysql.image.draw;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.app.myapi.vo.AppDrawImageWorksPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

@Mapper
public interface TaskDrawMapper extends BaseMapperX<AiImageDO> {

    default PageResult<AiImageDO> selectImagePage(AppDrawImageWorksPageReqVO reqVO, Long userId) {
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        // 当前时间往前推7天的时间
        LocalDateTime sevenDaysAgo = now.minusDays(7);
        // 定义时间范围数组
        LocalDateTime[] createTime = {sevenDaysAgo, now};
        return selectPage(reqVO, new LambdaQueryWrapperX<AiImageDO>()
                .betweenIfPresent(AiImageDO::getCreateTime, createTime)
                .eq(AiImageDO::getUserId, userId)
                .orderByDesc(AiImageDO::getFinishTime));
    }
}

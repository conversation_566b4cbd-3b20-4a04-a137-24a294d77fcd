package cn.iocoder.yudao.module.ai.enums.banner;

import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * Banner Position 枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BannerPositionEnum implements ArrayValuable<Integer> {

    HOME_POSITION(1, "首页"),
    ;

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(BannerPositionEnum::getPosition).toArray(Integer[]::new);

    /**
     * 值
     */
    private final Integer position;
    /**
     * 名字
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}

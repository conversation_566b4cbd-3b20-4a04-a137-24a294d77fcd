package cn.iocoder.yudao.module.ai.dal.dataobject.invite;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 邀请码邀请好友得奖励 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_invite_user")
@Comment(value = "Ai邀请码用户")
@Entity
@TableName("ai_invite_user")
@KeySequence("ai_invite_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiInviteUserDO extends TenantBaseDO {

    /**
     * 用户编号
     * <p>
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 用户编号
     * <p>
     * 关联 MemberUserDO 的 id 字段
     */
    @Column(columnDefinition = "bigint COMMENT '用户编号'")
    private Long bindUserId;
    /**
     * 绑定时间
     */
    @Column(columnDefinition="datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间'")
    private LocalDateTime bindUserTime;

    /**
     * 是否有分销资格
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'1' COMMENT '是否有分销资格'")
    private Boolean inviteEnabled;

    /**
     * 邀请总佣金
     */
    @Column(columnDefinition = "int COMMENT '邀请总佣金'")
    private Integer inviteCoin;

    /**
     * 邀请码
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '邀请码'")
    private String inviteCode;
}

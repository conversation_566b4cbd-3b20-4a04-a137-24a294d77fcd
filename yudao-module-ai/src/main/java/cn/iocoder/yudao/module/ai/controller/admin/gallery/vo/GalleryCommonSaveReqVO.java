package cn.iocoder.yudao.module.ai.controller.admin.gallery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - AI 公开画廊新增/修改 Request VO")
@Data
public class GalleryCommonSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "11971")
    private Long id;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    private String coverUrl;

    @Schema(description = "同款图")
    private List<String> imgUrls;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    private Integer status;

}
package cn.iocoder.yudao.module.ai.controller.app.textToAudio;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.ai.controller.app.textToAudio.message.AppAiTextToAudioMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.app.textToAudio.message.AppAiTextToAudioReqVO;
import cn.iocoder.yudao.module.ai.service.textToAudio.AiChatToAudioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @Description: 用户聊天信息转语音信息
 * @Date: 2024/7/27 11:14
 * @Author: zhangq
 * @Version: 1.0
 */
@Tag(name = "用户 APP  - 聊天信息转语音信息")
@RestController
@RequestMapping("/ai/chat/t2a")
@Slf4j
public class AppAiChatToAudioController {

    @Resource
    private AiChatToAudioService chatToAudioService;

    /**
     * 文字转语音
     */
    @Operation(summary = "文字转语音")
    @PostMapping("/text-to-voice")
    public CommonResult<AppAiTextToAudioMessageRespVO> textToVoice(@Valid @RequestBody AppAiTextToAudioReqVO reqVO) {
        return success(chatToAudioService.textToVoice(reqVO, getLoginUserId()));
    }
}

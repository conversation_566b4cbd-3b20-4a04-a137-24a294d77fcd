package cn.iocoder.yudao.module.ai.service.assistant;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageSendReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageSendRespVO;
import cn.iocoder.yudao.module.ai.controller.app.assistant.vo.message.AppAssistantMessagePraiseReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantMessageDO;
import jakarta.validation.Valid;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * AI 聊天消息 Service 接口
 *
 * <AUTHOR>
 */
public interface AiAssistantMessageService {

    /**
     * 发送消息
     *
     * @param sendReqVO 发送信息
     * @param userId 用户编号
     * @return 发送结果
     */
    AiAssistantMessageSendRespVO sendMessage(AiAssistantMessageSendReqVO sendReqVO, Long userId);

    /**
     * 发送消息
     *
     * @param sendReqVO 发送信息
     * @param userId 用户编号
     * @return 发送结果
     */
    Flux<CommonResult<AiAssistantMessageSendRespVO>> sendAssistantMessageStream(AiAssistantMessageSendReqVO sendReqVO, Long userId);

    /**
     * 获得指定对话的消息列表
     *
     * @param conversationId 对话编号
     * @return 消息列表
     */
    List<AiAssistantMessageDO> getAssistantMessageListByConversationId(Long conversationId);

    /**
     * 删除消息
     *
     * @param id 消息编号
     * @param userId 用户编号
     */
    void deleteAssistantMessage(Long id, Long userId);

    /**
     * 删除指定对话的消息
     *
     * @param conversationId 对话编号
     * @param userId 用户编号
     */
    void deleteAssistantMessageByConversationId(Long conversationId, Long userId);

    /**
     * 【管理员】删除消息
     *
     * @param id 消息编号
     */
    void deleteAssistantMessageByAdmin(Long id);

    /**
     * 获得聊天对话的消息数量 Map
     *
     * @param conversationIds 对话编号数组
     * @return 消息数量 Map
     */
    Map<Long, Integer> getAssistantMessageCountMap(Collection<Long> conversationIds);

    /**
     * 获得聊天消息的分页
     *
     * @param pageReqVO 分页查询
     * @return 聊天消息的分页
     */
    PageResult<AiAssistantMessageDO> getAssistantMessagePage(AiAssistantMessagePageReqVO pageReqVO);

    /**
     * 获得指定对话的聊天消分页
     * @param conversationId 对话编号
     * @return 聊天消息的分页
     */
    PageResult<AiAssistantMessageDO> getAssistantMessagePageByConversationId(AiAssistantMessagePageReqVO conversationId);

    /**
     * 点赞消息
     * @param reqVO
     */
    void praiseAssistantMessage(@Valid AppAssistantMessagePraiseReqVO reqVO);
}

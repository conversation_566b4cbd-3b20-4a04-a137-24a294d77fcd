package cn.iocoder.yudao.module.ai.controller.app.invite;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.ai.service.invite.AiInviteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * @Description: TODO
 * @Module: 我的邀请码接口
 * @Date: 2025/1/13 22:25
 * @Author: zhangq
 * @Version: 1.0
 */
@Tag(name = "用户APP - 邀请码 接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/invite")
@Validated
public class AppInviteController {


    private final AiInviteService inviteService;

    /**
     * 获取我的邀请码
     */
    @Operation(summary = "获取我的邀请码")
    @PostMapping("/getInviteCode")
    public CommonResult<String> getInviteCode() {
        return success(inviteService.getInviteCode(getLoginUserId()));
    }

    /**
     * 获取我的作品
     *
     * @return
     */
    @Operation(summary = "填邀请码")
    @PostMapping("/fillInviteCode")
    public CommonResult<String> fillInviteCode(String inviteCode) {
        inviteService.fillInviteCode(getLoginUserId(), inviteCode);
        return success(null);
    }
}

package cn.iocoder.yudao.module.ai.dal.mysql.image.config;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description: AI绘画基础配置
 * @Date: 2024/12/18 23:06
 * @Author: zhangq
 * @Version: 1.0
 */
@Mapper
public interface AiImageConfigMapper extends BaseMapperX<AiImageConfigDO> {

   default AiImageConfigDO selectLast(){
       // 构建查询条件，按id降序排列并限制只查询一条记录
       LambdaQueryWrapper<AiImageConfigDO> lambdaQuery =  new LambdaQueryWrapper<>();
       lambdaQuery.orderByDesc(AiImageConfigDO::getId).last("LIMIT 1");
       // 执行查询
       return selectOne(lambdaQuery);
   }
}

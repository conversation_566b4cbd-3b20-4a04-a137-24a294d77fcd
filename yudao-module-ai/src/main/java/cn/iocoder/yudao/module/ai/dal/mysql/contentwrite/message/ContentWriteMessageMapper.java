package cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.message;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo.ContentWriteMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo.AppContentWriteMessagePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteMessageDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI 内容创作 消息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentWriteMessageMapper extends BaseMapperX<AiContentWriteMessageDO> {

    default PageResult<AiContentWriteMessageDO> selectPage(ContentWriteMessagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiContentWriteMessageDO>()
                .betweenIfPresent(AiContentWriteMessageDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiContentWriteMessageDO::getAiCoin, reqVO.getAiCoin())
                .eqIfPresent(AiContentWriteMessageDO::getContent, reqVO.getContent())
                .eqIfPresent(AiContentWriteMessageDO::getConversationId, reqVO.getConversationId())
                .eqIfPresent(AiContentWriteMessageDO::getInputSensitive, reqVO.getInputSensitive())
                .eqIfPresent(AiContentWriteMessageDO::getInputSensitiveType, reqVO.getInputSensitiveType())
                .eqIfPresent(AiContentWriteMessageDO::getModel, reqVO.getModel())
                .eqIfPresent(AiContentWriteMessageDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AiContentWriteMessageDO::getOutputSensitive, reqVO.getOutputSensitive())
                .eqIfPresent(AiContentWriteMessageDO::getOutputSensitiveType, reqVO.getOutputSensitiveType())
                .eqIfPresent(AiContentWriteMessageDO::getPraiseType, reqVO.getPraiseType())
                .eqIfPresent(AiContentWriteMessageDO::getReplyId, reqVO.getReplyId())
                .eqIfPresent(AiContentWriteMessageDO::getRequestParams, reqVO.getRequestParams())
                .eqIfPresent(AiContentWriteMessageDO::getResponseParams, reqVO.getResponseParams())
                .eqIfPresent(AiContentWriteMessageDO::getType, reqVO.getType())
                .eqIfPresent(AiContentWriteMessageDO::getUseContext, reqVO.getUseContext())
                .eqIfPresent(AiContentWriteMessageDO::getUserId, reqVO.getUserId())
                .orderByDesc(AiContentWriteMessageDO::getId));
    }

    default List<AiContentWriteMessageDO> selectListByConversationId(Long id){
        return selectList(new LambdaQueryWrapperX<AiContentWriteMessageDO>()
                .eq(AiContentWriteMessageDO::getConversationId, id)
                .orderByAsc(AiContentWriteMessageDO::getId));
    }

    default PageResult<AiContentWriteMessageDO> getContentWriteMessageListByConversationId(AppContentWriteMessagePageReqVO pageReqVO){
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiContentWriteMessageDO>()
                .eq(AiContentWriteMessageDO::getConversationId, pageReqVO.getConversationId())
                .eqIfPresent(AiContentWriteMessageDO::getUserId, pageReqVO.getUserId())
                .orderByDesc(AiContentWriteMessageDO::getId));
    }
}
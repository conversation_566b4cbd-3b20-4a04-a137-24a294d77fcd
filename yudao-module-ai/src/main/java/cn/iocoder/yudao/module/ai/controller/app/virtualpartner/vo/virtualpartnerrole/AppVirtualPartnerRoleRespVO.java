package cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "用户APP - 虚拟陪伴角色 Response VO")
@Data
public class AppVirtualPartnerRoleRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "24161")
    private Long id;

    @Schema(description = "音色id", example = "24348")
    private Long audioId;

    @Schema(description = "聊天背景图", example = "https://www.iocoder.cn")
    private String backgroundUrl;

    @Schema(description = "点击量", example = "13786")
    private Long clickCount;

    @Schema(description = "开场白")
    private String cover;

    @Schema(description = "封面描述")
    private String coverDesc;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    private String coverUrl;

    @Schema(description = "开场白语音文件")
    private String coverVoiceFile;

    @Schema(description = "输入框预置词")
    private String inputPresetWord;

    @Schema(description = "模型版本")
    private String model;

    @Schema(description = "模型编号", example = "29131")
    private Long modelId;

    @Schema(description = "角色名称", example = "赵六")
    private String name;

    @Schema(description = "模型平台")
    private String platform;

    @Schema(description = "预设示例")
    private List<String> presetExample;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "故事简介")
    private String storyDesc;

    @Schema(description = "角色设定")
    private String systemMessage;

    @Schema(description = "温度参数")
    private Double temperature;

    @Schema(description = "采样方法")
    private Double topP;

}
package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConversationDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.conversation.ContentWriteConversationService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Hidden
@Tag(name = "管理后台 - AI 内容写作 对话 DO")
@RestController
@RequestMapping("/ai/content-write-conversation")
@Validated
public class ContentWriteConversationController {

    @Resource
    private ContentWriteConversationService contentWriteConversationService;

    @PostMapping("/create")
    @Operation(summary = "创建AI 内容写作 对话 DO")
    @PreAuthorize("@ss.hasPermission('ai:content-write-conversation:create')")
    public CommonResult<Long> createContentWriteConversation(@Valid @RequestBody ContentWriteConversationSaveReqVO createReqVO) {
        return success(contentWriteConversationService.createContentWriteConversation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI 内容写作 对话 DO")
    @PreAuthorize("@ss.hasPermission('ai:content-write-conversation:update')")
    public CommonResult<Boolean> updateContentWriteConversation(@Valid @RequestBody ContentWriteConversationSaveReqVO updateReqVO) {
        contentWriteConversationService.updateContentWriteConversation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 内容写作 对话 DO")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:content-write-conversation:delete')")
    public CommonResult<Boolean> deleteContentWriteConversation(@RequestParam("id") Long id) {
        contentWriteConversationService.deleteContentWriteConversation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI 内容写作 对话 DO")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:content-write-conversation:query')")
    public CommonResult<ContentWriteConversationRespVO> getContentWriteConversation(@RequestParam("id") Long id) {
        AiContentWriteConversationDO contentWriteConversation = contentWriteConversationService.getContentWriteConversation(id);
        return success(BeanUtils.toBean(contentWriteConversation, ContentWriteConversationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI 内容写作 对话 DO分页")
    @PreAuthorize("@ss.hasPermission('ai:content-write-conversation:query')")
    public CommonResult<PageResult<ContentWriteConversationRespVO>> getContentWriteConversationPage(@Valid ContentWriteConversationPageReqVO pageReqVO) {
        PageResult<AiContentWriteConversationDO> pageResult = contentWriteConversationService.getContentWriteConversationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContentWriteConversationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出AI 内容写作 对话 DO Excel")
    @PreAuthorize("@ss.hasPermission('ai:content-write-conversation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContentWriteConversationExcel(@Valid ContentWriteConversationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiContentWriteConversationDO> list = contentWriteConversationService.getContentWriteConversationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI 内容写作 对话 DO.xls", "数据", ContentWriteConversationRespVO.class,
                        BeanUtils.toBean(list, ContentWriteConversationRespVO.class));
    }

}
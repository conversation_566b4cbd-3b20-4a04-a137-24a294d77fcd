package cn.iocoder.yudao.module.ai.controller.admin.complaint.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 投诉/反馈新增/修改 Request VO")
@Data
public class ComplaintFeedbackSaveReqVO {

    @Schema(description = "编号，唯一自增", example = "1")
    private Long id;

    @Schema(description = "原因 用户填写")
    private String content;

    @Schema(description = "反馈类目 接口获取")
    private String feedbackCategory;

    @Schema(description = "投诉图片")
    private String images;

    @Schema(description = "反馈类型 接口获取", example = "2")
    private String feedbackType;

    //@Schema(description = "编号")
    //private String no;

    @Schema(description = "资源类型 1-智能助手 2-虚拟陪伴 3-内容创作 以后新加去字典看feedback_resource_type", example = "1")
    private String resourceType;

    @Schema(description = "类型 1举报 2反馈", example = "2")
    private String type;

    @Schema(description = "资源id 当前消息ID 当是来源【我的】可不传", example = "14725")
    private String resourceId;

    @Schema(description = "用户编号", example = "27311")
    private Long userId;

}
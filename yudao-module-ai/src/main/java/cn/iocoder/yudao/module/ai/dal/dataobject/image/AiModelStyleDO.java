package cn.iocoder.yudao.module.ai.dal.dataobject.image;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.glassfish.jaxb.core.v2.TODO;
import org.hibernate.annotations.Comment;

/**
 * 风格模型 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_model_style")
@Comment(value = "风格模型")
@Entity
@TableName("ai_model_style")
@KeySequence("ai_model_style_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiModelStyleDO extends TenantBaseDO {

    /**
     * 编号，唯一自增
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;
    /**
     * 风格描述
     */
    @Column(columnDefinition = "varchar(255) COMMENT '风格描述'")
    private String modelStyleDesc;
    /**
     * 触站Model ID
     */
    @Column(columnDefinition = "int COMMENT '触站Model ID'")
    private Integer modelStyleId;
    /**
     * 效果图
     */
    @Column(columnDefinition = "varchar(255) COMMENT '效果图'")
    private String modelStyleImg;
    /**
     * 风格名称
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '风格名称'")
    private String modelStyleName;
    /**
     * 使用次数
     */
    @Column(columnDefinition = "int COMMENT '使用次数'")
    private Integer modelStyleUseCount;
    /**
     * 排序
     */
    @Column(columnDefinition = "int COMMENT '排序'")
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    @Column(columnDefinition = "int COMMENT '状态'")
    private Integer status;

    /**
     * 备注
     */
    @Column(columnDefinition = "varchar(255) COMMENT '备注'")
    private String remark;
}
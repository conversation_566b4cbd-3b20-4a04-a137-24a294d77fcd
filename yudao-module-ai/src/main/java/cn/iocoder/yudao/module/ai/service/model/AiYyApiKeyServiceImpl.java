package cn.iocoder.yudao.module.ai.service.model;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.apikey.AiYyApiKeyPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.apikey.AiYyApiKeySaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyApiKeyDO;
import cn.iocoder.yudao.module.ai.dal.mysql.model.AiYyApiKeyMapper;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.API_KEY_DISABLE;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.API_KEY_NOT_EXISTS;

/**
 * AI API 密钥 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiYyApiKeyServiceImpl implements AiYyApiKeyService {

    @Resource
    private AiYyApiKeyMapper apiKeyMapper;

    @Override
    public Long createApiKey(AiYyApiKeySaveReqVO createReqVO) {
        // 插入
        AiYyApiKeyDO apiKey = BeanUtils.toBean(createReqVO, AiYyApiKeyDO.class);
        apiKeyMapper.insert(apiKey);
        // 返回
        return apiKey.getId();
    }

    @Override
    public void updateApiKey(AiYyApiKeySaveReqVO updateReqVO) {
        // 校验存在
        validateApiKeyExists(updateReqVO.getId());
        // 更新
        AiYyApiKeyDO updateObj = BeanUtils.toBean(updateReqVO, AiYyApiKeyDO.class);
        apiKeyMapper.updateById(updateObj);
    }

    @Override
    public void deleteApiKey(Long id) {
        // 校验存在
        validateApiKeyExists(id);
        // 删除
        apiKeyMapper.deleteById(id);
    }

    private AiYyApiKeyDO validateApiKeyExists(Long id) {
        AiYyApiKeyDO apiKey = apiKeyMapper.selectById(id);
        if (apiKey == null) {
            throw exception(API_KEY_NOT_EXISTS);
        }
        return apiKey;
    }

    @Override
    public AiYyApiKeyDO getApiKey(Long id) {
        return apiKeyMapper.selectById(id);
    }

    @Override
    public AiYyApiKeyDO validateApiKey(Long id) {
        AiYyApiKeyDO apiKey = validateApiKeyExists(id);
        if (CommonStatusEnum.isDisable(apiKey.getStatus())) {
            throw exception(API_KEY_DISABLE);
        }
        return apiKey;
    }

    @Override
    public PageResult<AiYyApiKeyDO> getApiKeyPage(AiYyApiKeyPageReqVO pageReqVO) {
        return apiKeyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AiYyApiKeyDO> getApiKeyList(Integer status) {
        return apiKeyMapper.selectList(new LambdaQueryWrapperX<AiYyApiKeyDO>()
                .eq(AiYyApiKeyDO::getStatus, status)
        );
    }

    @Override
    public AiYyApiKeyDO getApiKeyDOImageModel(AiPlatformEnum platform) {
        AiYyApiKeyDO apiKey = apiKeyMapper.selectFirstByPlatformAndStatus(platform.getPlatform(), CommonStatusEnum.ENABLE.getStatus());
        if (apiKey == null) {
            throw exception(API_KEY_NOT_EXISTS, platform.getName());
        }
        return apiKey;
    }

    @Override
    public AiYyApiKeyDO getRequiredDefaultApiKey(String platform, Integer status) {
        AiYyApiKeyDO apiKey = apiKeyMapper.selectFirstByPlatformAndStatus(platform, status);
        if (apiKey == null) {
            throw exception(API_KEY_NOT_EXISTS);
        }
        return apiKey;
    }
}
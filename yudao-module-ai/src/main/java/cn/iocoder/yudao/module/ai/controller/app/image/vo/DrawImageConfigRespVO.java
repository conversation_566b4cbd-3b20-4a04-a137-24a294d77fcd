package cn.iocoder.yudao.module.ai.controller.app.image.vo;

import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/8/4 21:27
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class DrawImageConfigRespVO {

    /**
     * 绘画风格
     */
    @Schema(description = "绘画风格")
    private List<AppModelStyleRespVO> modelStyleList;

    /**
     * 绘画尺寸
     */
    //@Schema(description = "绘画尺寸")
    //private AppAspectRatioPresetRespVO aspectRatioPreset;
    //List<Map<String, String>> aspectRatioPreset;

    /**
     * 绘画清晰度
     */
    @Schema(description = "绘画清晰度")
    private List<AppResolutionPresetRespVO> resolutionPresetList;

    @Schema(description = "脸部修复消耗（算力/张）")
    @ExcelProperty("脸部修复消耗（算力/张）")
    private Integer faceFixConsume;

    @Schema(description = "默认绘画数量（1-5）")
    @ExcelProperty("默认绘画数量（1-5）")
    private Integer defaultDrawNum;

    @Schema(description = "基础消耗算力/张")
    @ExcelProperty("基础消耗算力/张")
    private Integer drawBaseConsume;

    @Schema(description = "脸部修复默认状态")
    private Integer faceFixDefaultStatus;
}

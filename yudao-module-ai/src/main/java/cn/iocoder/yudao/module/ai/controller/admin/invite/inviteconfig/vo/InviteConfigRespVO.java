package cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 邀请设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InviteConfigRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "24246")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "邀请图片", example = "https://www.iocoder.cn")
    @ExcelProperty("邀请图片")
    private String imageUrl;

}
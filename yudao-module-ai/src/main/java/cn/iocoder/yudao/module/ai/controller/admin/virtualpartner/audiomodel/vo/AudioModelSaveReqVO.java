package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 音色模型新增/修改 Request VO")
@Data
public class AudioModelSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14088")
    private Long id;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "音色ID", example = "842")
    private String voiceId;

    @Schema(description = "音色名称", example = "张三")
    private String voiceName;

    @Schema(description = "语音类型列表，以逗号分隔", example = "1,2")
    private List<Integer> voiceTypes;

}
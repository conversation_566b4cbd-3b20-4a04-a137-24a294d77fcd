package cn.iocoder.yudao.module.ai.service.image.aimodelstyle;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStylePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStyleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import cn.iocoder.yudao.module.ai.dal.mysql.image.modelstyle.AiModelStyleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.MODEL_STYLE_NOT_EXISTS;

/**
 * 风格模型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiModelStyleServiceImpl implements AiModelStyleService {

    @Resource
    private AiModelStyleMapper modelStyleMapper;

    @Override
    public Long createModelStyle(AiModelStyleSaveReqVO createReqVO) {
        // 插入
        AiModelStyleDO modelStyle = BeanUtils.toBean(createReqVO, AiModelStyleDO.class);
        modelStyleMapper.insert(modelStyle);
        // 返回
        return modelStyle.getId();
    }

    @Override
    public void updateModelStyle(AiModelStyleSaveReqVO updateReqVO) {
        // 校验存在
        validateModelStyleExists(updateReqVO.getId());
        // 更新
        AiModelStyleDO updateObj = BeanUtils.toBean(updateReqVO, AiModelStyleDO.class);
        modelStyleMapper.updateById(updateObj);
    }

    @Override
    public void deleteModelStyle(Long id) {
        // 校验存在
        validateModelStyleExists(id);
        // 删除
        modelStyleMapper.deleteById(id);
    }

    private void validateModelStyleExists(Long id) {
        if (modelStyleMapper.selectById(id) == null) {
            throw exception(MODEL_STYLE_NOT_EXISTS);
        }
    }

    @Override
    public AiModelStyleDO getModelStyle(Long id) {
        return modelStyleMapper.selectById(id);
    }

    @Override
    public PageResult<AiModelStyleDO> getModelStylePage(AiModelStylePageReqVO pageReqVO) {
        return modelStyleMapper.selectPage(pageReqVO);
    }

}
package cn.iocoder.yudao.module.ai.service.gallery;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryConfigDO;
import jakarta.validation.Valid;

/**
 * AI 画廊配置 Service 接口
 *
 * <AUTHOR>
 */
public interface GalleryConfigService {

    /**
     * 创建AI 画廊配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveOrUpdateGalleryConfig(@Valid GalleryConfigSaveReqVO createReqVO);

    /**
     * 删除AI 画廊配置
     *
     * @param id 编号
     */
    void deleteGalleryConfig(Long id);

    /**
     * 获得AI 画廊配置
     * @return AI 画廊配置
     */
    GalleryConfigDO getGalleryConfig();

    /**
     * 获得AI 画廊配置分页
     *
     * @param pageReqVO 分页查询
     * @return AI 画廊配置分页
     */
    PageResult<GalleryConfigDO> getGalleryConfigPage(GalleryConfigPageReqVO pageReqVO);

}
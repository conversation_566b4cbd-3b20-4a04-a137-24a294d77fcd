package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 音色模型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AudioModelPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "音色ID", example = "842")
    private String voiceId;

    @Schema(description = "音色名称", example = "张三")
    private String voiceName;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "语音类型列表，以逗号分隔", example = "1,2")
    @ExcelProperty("语音类型列表，以逗号分隔")
    private List<Integer> voiceTypes;

}
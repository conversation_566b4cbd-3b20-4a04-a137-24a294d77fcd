package cn.iocoder.yudao.module.ai.dal.mysql.promptwords;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.promptwords.PromptWordsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提示词 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PromptWordsMapper extends BaseMapperX<PromptWordsDO> {

    default PageResult<PromptWordsDO> selectPage(PromptWordsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PromptWordsDO>()
                .betweenIfPresent(PromptWordsDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(PromptWordsDO::getPromptWords, reqVO.getPromptWords())
                .eqIfPresent(PromptWordsDO::getPromptWordsType, reqVO.getPromptWordsType())
                .eqIfPresent(PromptWordsDO::getSort, reqVO.getSort())
                .eqIfPresent(PromptWordsDO::getStatus, reqVO.getStatus())
                .orderByDesc(PromptWordsDO::getId));
    }

}
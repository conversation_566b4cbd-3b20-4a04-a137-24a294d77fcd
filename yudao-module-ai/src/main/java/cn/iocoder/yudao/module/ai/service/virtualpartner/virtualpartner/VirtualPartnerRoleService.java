package cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import jakarta.validation.Valid;

/**
 * 虚拟陪伴角色 Service 接口
 *
 * <AUTHOR>
 */
public interface VirtualPartnerRoleService {

    /**
     * 创建虚拟陪伴角色
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVirtualPartnerRole(@Valid VirtualPartnerRoleSaveReqVO createReqVO);

    /**
     * 更新虚拟陪伴角色
     *
     * @param updateReqVO 更新信息
     */
    void updateVirtualPartnerRole(@Valid VirtualPartnerRoleSaveReqVO updateReqVO);

    /**
     * 删除虚拟陪伴角色
     *
     * @param id 编号
     */
    void deleteVirtualPartnerRole(Long id);

    /**
     * 获得虚拟陪伴角色
     *
     * @param id 编号
     * @return 虚拟陪伴角色
     */
    AiVirtualPartnerRoleDO getVirtualPartnerRole(Long id);

    /**
     * 获得虚拟陪伴角色分页
     *
     * @param pageReqVO 分页查询
     * @return 虚拟陪伴角色分页
     */
    PageResult<AiVirtualPartnerRoleDO> getVirtualPartnerRolePage(VirtualPartnerRolePageReqVO pageReqVO);

    /**
     * 获得虚拟陪伴角色分页-App
     * @param pageReqVO
     * @return
     */
    PageResult<AiVirtualPartnerRoleDO> getAppVirtualPartnerRolePage(@Valid AppVirtualPartnerRolePageReqVO pageReqVO);

    /**
     * 验证虚拟陪伴角色是否存在
     * @param roleId
     * @return
     */
    AiVirtualPartnerRoleDO validateVirtualPartnerRole(Long roleId);
}
package cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.ai.enums.promptwords.PromptWordsTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 提示词新增/修改 Request VO")
@Data
public class PromptWordsSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "提示词")
    private String promptWords;

    @Schema(description = "提示词类型 1绘画提示词 2绘画质量提示词 3智能助手打招呼 4智能助手提示词", example = "1")
    @NotNull(message = "提示词类型不能为空")
    @InEnum(PromptWordsTypeEnum.class)
    private Integer promptWordsType;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态 0启用 1禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Integer status;

}
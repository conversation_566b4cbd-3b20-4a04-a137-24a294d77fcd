package cn.iocoder.yudao.module.ai.controller.app.contentwrite.role.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 内容创作角色 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppContentWriteRoleRespVO implements VO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "2274")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "内容创作分类id", example = "1")
    @Trans(type = TransType.SIMPLE, targetClassName = "cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteCategoryDO",
    fields = "name", ref = "categoryName")
    private Long categoryId;

    @Schema(description = "分类名称", example = "写作")
    @ExcelProperty("分类名称")
    private String categoryName;

    //@Schema(description = "聊天背景图", example = "https://www.iocoder.cn")
    //@ExcelProperty("聊天背景图")
    //private String backgroundUrl;

    @Schema(description = "点击量", example = "15571")
    @ExcelProperty("点击量")
    private Long clickCount;

    @Schema(description = "开场白")
    @ExcelProperty("开场白")
    private String cover;

    @Schema(description = "描述")
    @ExcelProperty("描述")
    private String descContent;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    @ExcelProperty("封面")
    private String coverUrl;

    //@Schema(description = "开场白语音文件")
    //@ExcelProperty("开场白语音文件")
    //private String coverVoiceFile;

    @Schema(description = "输入框预置词")
    @ExcelProperty("输入框预置词")
    private String inputPresetWord;

    //@Schema(description = "上下文的最大 Message 数量")
    //@ExcelProperty("上下文的最大 Message 数量")
    //private Integer maxContexts;

    //@Schema(description = "单条回复的最大 Token 数量")
    //@ExcelProperty("单条回复的最大 Token 数量")
    //private Integer maxTokens;

    //@Schema(description = "模型版本")
    //@ExcelProperty("模型版本")
    //private String model;

    //@Schema(description = "模型编号", example = "21907")
    //@ExcelProperty("模型编号")
    //private Long modelId;

    @Schema(description = "角色名称", example = "王五")
    @ExcelProperty("角色名称")
    private String name;

    //@Schema(description = "模型平台")
    //@ExcelProperty("模型平台")
    //private String platform;

    @Schema(description = "预设示例")
    @ExcelProperty("预设示例")
    private List<String> presetExample;

    @Schema(description = "排序值")
    @ExcelProperty("排序值")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Integer status;

    //@Schema(description = "故事简介")
    //@ExcelProperty("故事简介")
    //private String storyDesc;

    //@Schema(description = "角色设定")
    //@ExcelProperty("角色设定")
    //private String systemMessage;

    //@Schema(description = "温度参数")
    //@ExcelProperty("温度参数")
    //private Double temperature;

    //@Schema(description = "采样方法")
    //@ExcelProperty("采样方法")
    //private Double topP;

}
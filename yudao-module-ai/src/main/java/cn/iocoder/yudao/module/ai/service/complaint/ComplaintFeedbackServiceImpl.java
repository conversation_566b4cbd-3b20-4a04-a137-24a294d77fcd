package cn.iocoder.yudao.module.ai.service.complaint;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.complaint.ComplaintFeedbackDO;
import cn.iocoder.yudao.module.ai.dal.mysql.complaint.ComplaintFeedbackMapper;
import cn.iocoder.yudao.module.ai.dal.redis.no.ComplaintFeedbackNoRedisDAO;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.COMPLAINT_FEEDBACK_NOT_EXISTS;

/**
 * 投诉/反馈 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ComplaintFeedbackServiceImpl implements ComplaintFeedbackService {

    /**
     * 流水的 no 前缀
     */
    private static final String COMPLAINT_FEEDBACK_NO_PREFIX = "CF";

    @Resource
    private ComplaintFeedbackMapper complaintFeedbackMapper;

    @Resource
    private ComplaintFeedbackNoRedisDAO complaintFeedbackNoRedisDAO;

    @Resource
    private MemberUserApi memberUserApi;

    @Override
    public String createComplaintFeedback(ComplaintFeedbackSaveReqVO createReqVO) {
        // 插入
        ComplaintFeedbackDO complaintFeedback = BeanUtils.toBean(createReqVO, ComplaintFeedbackDO.class);
        complaintFeedback.setNo(complaintFeedbackNoRedisDAO.generate(COMPLAINT_FEEDBACK_NO_PREFIX));
        MemberUserRespDTO user = memberUserApi.getUser(createReqVO.getUserId());
        log.debug("[createComplaintFeedback][user({})", user);
        if (null != user && null != user.getMobile()) {
            complaintFeedback.setMobile(user.getMobile());
        }
        complaintFeedbackMapper.insert(complaintFeedback);
        // 返回
        return complaintFeedback.getNo();
    }

    @Override
    public void updateComplaintFeedback(ComplaintFeedbackSaveReqVO updateReqVO) {
        // 校验存在
        validateComplaintFeedbackExists(updateReqVO.getId());
        // 更新
        ComplaintFeedbackDO updateObj = BeanUtils.toBean(updateReqVO, ComplaintFeedbackDO.class);
        complaintFeedbackMapper.updateById(updateObj);
    }

    @Override
    public void deleteComplaintFeedback(Long id) {
        // 校验存在
        validateComplaintFeedbackExists(id);
        // 删除
        complaintFeedbackMapper.deleteById(id);
    }

    private void validateComplaintFeedbackExists(Long id) {
        if (complaintFeedbackMapper.selectById(id) == null) {
            throw exception(COMPLAINT_FEEDBACK_NOT_EXISTS);
        }
    }

    @Override
    public ComplaintFeedbackDO getComplaintFeedback(Long id) {
        return complaintFeedbackMapper.selectById(id);
    }

    @Override
    public PageResult<ComplaintFeedbackDO> getComplaintFeedbackPage(ComplaintFeedbackPageReqVO pageReqVO) {
        return complaintFeedbackMapper.selectPage(pageReqVO);
    }

    @Override
    public String createAppComplaintFeedback(Long userId,ComplaintFeedbackSaveReqVO createReqVO) {
        // 插入
        ComplaintFeedbackDO complaintFeedback = BeanUtils.toBean(createReqVO, ComplaintFeedbackDO.class);
        complaintFeedback.setNo(complaintFeedbackNoRedisDAO.generate(COMPLAINT_FEEDBACK_NO_PREFIX));
        MemberUserRespDTO user = memberUserApi.getUser(userId);
        log.debug("[createComplaintFeedback][user({})", user);
        if (null != user && null != user.getMobile()) {
            complaintFeedback.setMobile(user.getMobile());
        }
        complaintFeedbackMapper.insert(complaintFeedback);
        // 返回
        return complaintFeedback.getNo();
    }

}
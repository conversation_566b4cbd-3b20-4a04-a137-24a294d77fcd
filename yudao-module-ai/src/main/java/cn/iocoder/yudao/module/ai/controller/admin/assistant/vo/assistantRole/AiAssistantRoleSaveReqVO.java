package cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantR<PERSON>;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - AI 聊天角色新增/修改 Request VO")
@Data
public class AiAssistantRoleSaveReqVO {

    @Schema(description = "角色编号", example = "32746")
    private Long id;

    @Schema(description = "模型编号", example = "17640")
    private Long modelId;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED, example = "gpt-3.5-turbo")
    private String model;

    @Schema(description = "消耗算力", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "消耗算力不能为空")
    private Integer coin;

    @Schema(description = "角色设定", requiredMode = Schema.RequiredMode.REQUIRED, example = "现在开始你扮演一位程序员，你是一名优秀的程序员，具有很强的逻辑思维能力，总能高效的解决问题")
    @NotEmpty(message = "角色设定不能为空")
    private String systemMessage;

    /**
     * 随机值/温度参数
     */
    @Schema(description = "随机值/温度参数", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.9")
    @NotNull(message = "随机值/温度参数不能为空")
    private Double temperature;
    /**
     * 采样方法 Top_p
     */
    @Schema(description = "采样方法 Top_p", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.9")
    @NotNull(message = "采样方法 Top_p不能为空")
    private Double topP;

    @Schema(description = "是否默认 0默认 1非默认", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    //@NotNull(message = "是否默认不能为空")
    private Integer defaultRole = 0;

    @Schema(description = "角色排序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    //@NotNull(message = "角色排序不能为空")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    //@NotNull(message = "状态不能为空")
    //@InEnum(CommonStatusEnum.class)
    private Integer status;

    @Schema(description = "单条回复的最大 Token 数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "单条回复的最大 Token 数量不能为空")
    private Integer maxTokens;

    @Schema(description = "上下文的最大 Message 数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer maxContexts;

}
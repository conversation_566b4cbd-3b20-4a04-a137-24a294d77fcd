package cn.iocoder.yudao.module.ai.service.contentwrite.role;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteRoleDO;
import jakarta.validation.Valid;

/**
 * 内容创作角色 Service 接口
 *
 * <AUTHOR>
 */
public interface ContentWriteRoleService {

    /**
     * 创建内容创作角色
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContentWriteRole(@Valid ContentWriteRoleSaveReqVO createReqVO);

    /**
     * 更新内容创作角色
     *
     * @param updateReqVO 更新信息
     */
    void updateContentWriteRole(@Valid ContentWriteRoleSaveReqVO updateReqVO);

    /**
     * 删除内容创作角色
     *
     * @param id 编号
     */
    void deleteContentWriteRole(Long id);

    /**
     * 获得内容创作角色
     *
     * @param id 编号
     * @return 内容创作角色
     */
    AiContentWriteRoleDO getContentWriteRole(Long id);

    /**
     * 获得内容创作角色分页
     *
     * @param pageReqVO 分页查询
     * @return 内容创作角色分页
     */
    PageResult<AiContentWriteRoleDO> getContentWriteRolePage(ContentWriteRolePageReqVO pageReqVO);

    /**
     * 校验角色是否存在
     * @param roleId
     * @return
     */
    AiContentWriteRoleDO validateContentWriteRole(Long roleId);
}
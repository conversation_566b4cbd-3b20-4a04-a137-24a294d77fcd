package cn.iocoder.yudao.module.ai.dal.mysql.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessagePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantMessageDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * AI 聊天对话 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiAssistantMessageMapper extends BaseMapperX<AiAssistantMessageDO> {

    default List<AiAssistantMessageDO> selectListByConversationId(Long conversationId) {
        return selectList(new LambdaQueryWrapperX<AiAssistantMessageDO>()
                .eq(AiAssistantMessageDO::getConversationId, conversationId)
                .orderByAsc(AiAssistantMessageDO::getId));
    }

    default Map<Long, Integer> selectCountMapByConversationId(Collection<Long> conversationIds) {
        // SQL count 查询
        List<Map<String, Object>> result = selectMaps(new QueryWrapper<AiAssistantMessageDO>()
                .select("COUNT(id) AS count, conversation_id AS conversationId")
                .in("conversation_id", conversationIds)
                .groupBy("conversation_id"));
        if (CollUtil.isEmpty(result)) {
            return Collections.emptyMap();
        }
        // 转换数据
        return CollectionUtils.convertMap(result,
                record -> MapUtil.getLong(record, "conversationId"),
                record -> MapUtil.getInt(record, "count" ));
    }

    default PageResult<AiAssistantMessageDO> selectPage(AiAssistantMessagePageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiAssistantMessageDO>()
                .eqIfPresent(AiAssistantMessageDO::getConversationId, pageReqVO.getConversationId())
                .eqIfPresent(AiAssistantMessageDO::getUserId, pageReqVO.getUserId())
                .likeIfPresent(AiAssistantMessageDO::getContent, pageReqVO.getContent())
                .betweenIfPresent(AiAssistantMessageDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(AiAssistantMessageDO::getId));
    }

    default PageResult<AiAssistantMessageDO> getAssistantMessagePageByConversationId(AiAssistantMessagePageReqVO pageReqVO){
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiAssistantMessageDO>()
                .eq(AiAssistantMessageDO::getConversationId, pageReqVO.getConversationId())
                .eqIfPresent(AiAssistantMessageDO::getUserId, pageReqVO.getUserId())
                .orderByDesc(AiAssistantMessageDO::getId));
    }
}

package cn.iocoder.yudao.module.ai.controller.admin.config.base.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 基础配置新增/修改 Request VO")
@Data
public class BaseInfoConfigSaveReqVO {

    @Schema(description = "编号，唯一自增，修改时传值", example = "6148")
    private Long id;

    @Schema(description = "被好友成功邀请，赠送算力")
    private Integer inviteFriendGive;

    @Schema(description = "成功邀请好友，赠送算力")
    private Integer inviteGive;

    @Schema(description = "新用户注册赠送算力")
    private Integer registerGive;


    @Schema(description = "苹果应用商店ID")
    private String appStoreId;

    @Schema(description = "联系客服配置")
    private String contactCustomer;
}
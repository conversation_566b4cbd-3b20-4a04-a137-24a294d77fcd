package cn.iocoder.yudao.module.ai.service.config.coinrole;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo.CoinRoleConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.CoinRoleConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.base.CoinRoleConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.COIN_ROLE_CONFIG_NOT_EXISTS;

/**
 * 算力规则配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CoinRoleConfigServiceImpl implements CoinRoleConfigService {

    @Resource
    private CoinRoleConfigMapper coinRoleConfigMapper;

    @Override
    public Long saveOrUpdateCoinRoleConfig(CoinRoleConfigSaveReqVO createReqVO) {
        // 插入
        CoinRoleConfigDO roleConfig =  BeanUtils.toBean(createReqVO, CoinRoleConfigDO.class);
        if (null == roleConfig.getId() || roleConfig.getId() == 0){
            coinRoleConfigMapper.insert(roleConfig);
        }else{
            // 校验存在
            validateCoinRoleConfigExists(roleConfig.getId());
            coinRoleConfigMapper.updateById(roleConfig);
        }
        // 返回
        return roleConfig.getId();
    }

    private void validateCoinRoleConfigExists(Long id) {
        if (coinRoleConfigMapper.selectById(id) == null) {
            throw exception(COIN_ROLE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public CoinRoleConfigDO getSimpleCoinRoleConfig() {
        List<CoinRoleConfigDO> list = coinRoleConfigMapper.selectList();
        return CollectionUtils.getFirst(list);
    }

}
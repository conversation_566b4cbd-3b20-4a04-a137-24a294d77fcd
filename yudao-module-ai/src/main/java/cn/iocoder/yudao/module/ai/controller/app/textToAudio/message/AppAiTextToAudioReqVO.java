package cn.iocoder.yudao.module.ai.controller.app.textToAudio.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户APP - AI 聊天信息转语音信息")
@Data
public class AppAiTextToAudioReqVO {

    @Schema(description = "消息编号", example = "1")
    private Long messageId;

    @Schema(description = "文本", example = "真正的危险不是计算机开始像人一样思考，而是人开始像计算机一样思考。")
    private String text;
}

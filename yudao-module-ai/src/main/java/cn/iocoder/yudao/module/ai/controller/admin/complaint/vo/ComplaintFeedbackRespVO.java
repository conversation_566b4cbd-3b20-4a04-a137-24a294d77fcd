package cn.iocoder.yudao.module.ai.controller.admin.complaint.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.iocoder.yudao.framework.desensitize.core.slider.annotation.MobileDesensitize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 投诉/反馈 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ComplaintFeedbackRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "9253")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "原因")
    @ExcelProperty("原因")
    private String content;

    @Schema(description = "反馈类目")
    @ExcelProperty("反馈类目")
    private String feedbackCategory;

    @Schema(description = "投诉图片")
    @ExcelProperty("投诉图片")
    private String images;

    @Schema(description = "反馈类型", example = "2")
    @ExcelProperty("反馈类型")
    private String feedbackType;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String no;

    @Schema(description = "类型", example = "2")
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "资源id", example = "14725")
    @ExcelProperty("资源id")
    private String resourceId;

    @Schema(description = "资源类型 1-智能助手 2-虚拟陪伴 3-内容创作 以后新加去字典看feedback_resource_type", example = "1")
    private String resourceType;

    @Schema(description = "用户编号", example = "27311")
    @ExcelProperty("用户编号")
    private Long userId;

    @MobileDesensitize
    @Schema(description = "手机号", example = "15899999999")
    @ExcelProperty("手机号")
    private String mobile;

}
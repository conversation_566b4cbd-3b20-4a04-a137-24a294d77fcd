package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AiTextToAudioMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioConfigDO;
import cn.iocoder.yudao.module.ai.service.virtualpartner.audioconfig.AudioConfigService;
import com.minimaxi.platform.t2a.api.MinMaxT2AApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 语音配置")
@RestController
@RequestMapping("/ai/audio-config")
@Validated
public class AudioConfigController {

    @Resource
    private AudioConfigService audioConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建语音配置")
    @PreAuthorize("@ss.hasPermission('ai:audio-config:create')")
    public CommonResult<Long> createAudioConfig(@Valid @RequestBody AudioConfigSaveReqVO createReqVO) {
        return success(audioConfigService.createAudioConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新语音配置")
    @PreAuthorize("@ss.hasPermission('ai:audio-config:update')")
    public CommonResult<Boolean> updateAudioConfig(@Valid @RequestBody AudioConfigSaveReqVO updateReqVO) {
        audioConfigService.updateAudioConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除语音配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:audio-config:delete')")
    public CommonResult<Boolean> deleteAudioConfig(@RequestParam("id") Long id) {
        audioConfigService.deleteAudioConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得语音配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:audio-config:query')")
    public CommonResult<AudioConfigRespVO> getAudioConfig(@RequestParam("id") Long id) {
        AiAudioConfigDO audioConfig = audioConfigService.getAudioConfig(id);
        return success(BeanUtils.toBean(audioConfig, AudioConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得语音配置分页")
    @PreAuthorize("@ss.hasPermission('ai:audio-config:query')")
    public CommonResult<PageResult<AudioConfigRespVO>> getAudioConfigPage(@Valid AudioConfigPageReqVO pageReqVO) {
        PageResult<AiAudioConfigDO> pageResult = audioConfigService.getAudioConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AudioConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出语音配置 Excel")
    @PreAuthorize("@ss.hasPermission('ai:audio-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAudioConfigExcel(@Valid AudioConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiAudioConfigDO> list = audioConfigService.getAudioConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "语音配置.xls", "数据", AudioConfigRespVO.class,
                        BeanUtils.toBean(list, AudioConfigRespVO.class));
    }

    @Operation(summary = "语音生成（流式）", description = "流式返回，响应较快")
    @PostMapping(value = "/audio-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PermitAll // 解决 SSE 最终响应的时候，会被 Access Denied 拦截的问题
    public Flux<CommonResult<MinMaxT2AApi.T2ACompletion>> sendAudioMessageStream(@Valid @RequestBody AudioConfigSaveReqVO sendReqVO) {
        return audioConfigService.sendAudioMessageStream(sendReqVO, getLoginUserId());
    }

    @Operation(summary = "语音生成（非流式）", description = "非流式返回，响应较慢")
    @PostMapping(value = "/audio", produces = MediaType.APPLICATION_JSON_VALUE)
    @PermitAll // 解决 SSE 最终响应的时候，会被 Access Denied 拦截的问题
    public CommonResult<AiTextToAudioMessageRespVO> sendAudioMessage(@Valid @RequestBody AudioConfigSaveReqVO sendReqVO) {
        return CommonResult.success(audioConfigService.sendAudioMessage(sendReqVO, getLoginUserId()));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得语音配置模型列表")
    @Parameter(name = "status", description = "状态", required = true, example = "1")
    public CommonResult<List<AudioConfigRespVO>> getAudioConfigSimpleList(@RequestParam("status") Integer status) {
        List<AiAudioConfigDO> list = audioConfigService.getAudioConfigListByStatus(status);
        return success(convertList(list, audio -> new AudioConfigRespVO().setId(audio.getId())
                .setAudioFile(audio.getAudioFile()).setModel(audio.getModel()).setPlatform(audio.getPlatform())
                .setVoiceName(audio.getVoiceName()).setVoiceId(audio.getVoiceId())
        ));
    }

}
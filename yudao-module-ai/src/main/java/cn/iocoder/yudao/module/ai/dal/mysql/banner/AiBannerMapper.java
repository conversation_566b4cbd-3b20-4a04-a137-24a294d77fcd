package cn.iocoder.yudao.module.ai.dal.mysql.banner;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.banner.AiBannerDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Banner Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiBannerMapper extends BaseMapperX<AiBannerDO> {

    default PageResult<AiBannerDO> selectPage(BannerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiBannerDO>()
                .likeIfPresent(AiBannerDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AiBannerDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AiBannerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AiBannerDO::getSort));
    }

    default void updateBrowseCount(Long id) {
        update(null, new LambdaUpdateWrapper<AiBannerDO>()
                .eq(AiBannerDO::getId, id)
                .setSql("browse_count = browse_count + 1"));
    }

    default List<AiBannerDO> selectBannerListByPosition(Integer position) {
        return selectList(new LambdaQueryWrapperX<AiBannerDO>().eq(AiBannerDO::getPosition, position));
    }

}

package cn.iocoder.yudao.module.ai.service.virtualpartner.audioconfig;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServerException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AiTextToAudioMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyApiKeyDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.audioconfig.AudioConfigMapper;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.iocoder.yudao.module.ai.service.model.AiYyApiKeyService;
import cn.iocoder.yudao.module.ai.util.HEX;
import cn.iocoder.yudao.module.ai.util.MiniMaxT2AUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.minimaxi.platform.t2a.api.MinMaxT2AApi;
import com.minimaxi.platform.t2a.api.MiniMaxT2AModel;
import com.minimaxi.platform.t2a.api.MiniMaxT2AOptions;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;

/**
 * 语音配置 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AudioConfigServiceImpl implements AudioConfigService {

    @Resource
    private AudioConfigMapper audioConfigMapper;

    @Resource
    private AiYyApiKeyService apiKeyService;

    @Resource
    private FileApi fileApi;

    @Override
    public Long createAudioConfig(AudioConfigSaveReqVO createReqVO) {
        // 插入
        AiAudioConfigDO audioConfig = BeanUtils.toBean(createReqVO, AiAudioConfigDO.class);
        audioConfigMapper.insert(audioConfig);
        // 返回
        return audioConfig.getId();
    }

    @Override
    public void updateAudioConfig(AudioConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateAudioConfigExists(updateReqVO.getId());
        // 更新
        AiAudioConfigDO updateObj = BeanUtils.toBean(updateReqVO, AiAudioConfigDO.class);
        audioConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteAudioConfig(Long id) {
        // 校验存在
        validateAudioConfigExists(id);
        // 删除
        audioConfigMapper.deleteById(id);
    }

    private void validateAudioConfigExists(Long id) {
        if (audioConfigMapper.selectById(id) == null) {
            throw exception(AUDIO_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public AiAudioConfigDO getAudioConfig(Long id) {
        return audioConfigMapper.selectById(id);
    }

    @Override
    public PageResult<AiAudioConfigDO> getAudioConfigPage(AudioConfigPageReqVO pageReqVO) {
        return audioConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public Flux<CommonResult<MinMaxT2AApi.T2ACompletion>> sendAudioMessageStream(AudioConfigSaveReqVO sendReqVO, Long loginUserId) {
        // 1.1 校验模型
        //获取APIKEY
        AiYyApiKeyDO aiApiKeyDO = apiKeyService.getApiKeyDOImageModel(AiPlatformEnum.validatePlatform(sendReqVO.getPlatform()));
        if (aiApiKeyDO == null || !aiApiKeyDO.getStatus().equals(CommonStatusEnum.ENABLE.getStatus())){
            throw exception(MODEL_NOT_EXISTS);
        }
        if (StrUtil.isEmpty(sendReqVO.getContent())){
            throw exception(TEXT_NOT_EXISTS);
        }

        MinMaxT2AApi api;
        if (StrUtil.isEmpty(aiApiKeyDO.getUrl())){
            api = new MinMaxT2AApi(aiApiKeyDO.getApiKey());
        }else{
            api = new MinMaxT2AApi( aiApiKeyDO.getUrl() ,aiApiKeyDO.getApiKey());
        }

        MiniMaxT2AOptions option = MiniMaxT2AUtils.buildMiniMaxT2AOptions(sendReqVO,true);

        MiniMaxT2AModel t2AModel = new MiniMaxT2AModel(api, option);

        Flux<MinMaxT2AApi.T2ACompletion> chatResponse = null;
        try {
            chatResponse = t2AModel.stream("1810541336101666871");
        } catch (Exception e) {
            throw new ServerException(500,e.getMessage());
        }
        //chatResponse.window(256*1024);
        //return chatResponse.map((t2ACompletion) -> success(t2ACompletion));
        chatResponse
                .flatMap(completion -> {
                    byte[] data = completion.data().audio().getBytes();
                    List<byte[]> chunks = chunkData(data, 256 * 1024); // 将字节数组分块
                    // 仅处理分块数据，不改变返回的 completion 对象
                    return Flux.fromIterable(chunks)
                            .then(Mono.just(completion)); // 在分块完成后返回原始的 completion 对象
                })
                .map(completion -> CommonResult.success(completion));
        System.out.println(
                chatResponse.map(completion -> CommonResult.success(completion))
        );
        return null;
    }

    @Override
    public AiTextToAudioMessageRespVO sendAudioMessage(AudioConfigSaveReqVO sendReqVO, Long loginUserId) {
        // 1.1 校验模型
        //获取APIKEY
        AiYyApiKeyDO aiApiKeyDO = apiKeyService.getApiKeyDOImageModel(AiPlatformEnum.validatePlatform(sendReqVO.getPlatform()));
        if (aiApiKeyDO == null || !aiApiKeyDO.getStatus().equals(CommonStatusEnum.ENABLE.getStatus())){
            throw exception(MODEL_NOT_EXISTS);
        }
        if (StrUtil.isEmpty(sendReqVO.getContent())){
            throw exception(TEXT_NOT_EXISTS);
        }
        AiAudioConfigDO audioConfig = createAudioConfigDO(sendReqVO);

        MinMaxT2AApi api;
        if (StrUtil.isEmpty(aiApiKeyDO.getUrl())){
            api = new MinMaxT2AApi(aiApiKeyDO.getApiKey());
        }else{
            api = new MinMaxT2AApi( aiApiKeyDO.getUrl() ,aiApiKeyDO.getApiKey());
        }

        MiniMaxT2AOptions option = MiniMaxT2AUtils.buildMiniMaxT2AOptions(sendReqVO, false);

        MiniMaxT2AModel t2AModel = new MiniMaxT2AModel(api, option);

        MinMaxT2AApi.T2ACompletion chatResponse = null;
        try {
            chatResponse = t2AModel.call("1810541336101666871");
        } catch (Exception e) {
            throw new ServerException(500,e.getMessage());
        }

        //响应处理
        LocalDateTime finishTime = LocalDateTime.now();;
        AiTextToAudioMessageRespVO respVO = null;
        if (Objects.nonNull(chatResponse)){
            if (chatResponse.baseResponse() != null && chatResponse.baseResponse().statusCode() != 0) {

                audioConfigMapper.updateById(audioConfig.setStatus(AiImageStatusEnum.FAIL.getStatus()).setFinishTime(finishTime)
                        .setErrorMessage(chatResponse.baseResponse().message())
                );
                log.warn("[chatResponse] {}", chatResponse.baseResponse().message());
            }

            MinMaxT2AApi.T2ACompletion.ExtraInfo extraInfo=  chatResponse.extraInfo();

            // 2. 上传语音文件和字幕文件
            String audioUrl = null;
            String subtitleUrl = null;
            String errorMessage = null;
            if(StrUtil.isNotBlank(chatResponse.data().audio())){
                byte[] data = HEX.decode(chatResponse.data().audio());
                try {
                    audioUrl = fileApi.createFile(data);
                } catch (Exception ex) {
                    errorMessage = ex.getMessage();
                    log.warn("[textToVoice][语音({})) 上传失败]", chatResponse.traceId(), ex);
                }
            }

            // 3. 更新 message 状态
            if (StrUtil.isNotBlank(errorMessage)){
                audioConfig.setStatus(AiImageStatusEnum.FAIL.getStatus()).setErrorMessage(errorMessage);
            }else {
                audioConfig.setStatus(AiImageStatusEnum.SUCCESS.getStatus());
            }
            audioConfig.setAudioFile(audioUrl).setSubtitleFile(subtitleUrl).setErrorMessage(errorMessage)
                    .setTraceId(chatResponse.traceId()).setAudioLength(extraInfo.audioLength())
                    .setAudioSampleRate(extraInfo.audioSampleRate()).setAudioSize(extraInfo.audioSize())
                    .setBitrate(extraInfo.bitrate()).setWordCount(extraInfo.wordCount()).setInvisibleCharacterRatio(extraInfo.invisibleCharacterRatio())
                    .setUsageCharacters(extraInfo.usageCharacters())
                    .setFinishTime(finishTime);
            audioConfigMapper.updateById(audioConfig);

            respVO = new AiTextToAudioMessageRespVO()
                    .setAudioLength(msToSeconds(extraInfo.audioLength()))
                    .setAudioUrl(audioUrl).setAudioSize(extraInfo.audioSize())
                    //.setMessageId(textToAudioMessage.getMessageId())
                    .setInvisibleCharacterRatio(extraInfo.invisibleCharacterRatio())
                    //.setContent(textToAudioMessage.getContent())
                    .setCreateTime(audioConfig.getCreateTime()).setId(audioConfig.getId());
        }

        return respVO;
    }

    @Override
    public List<AiAudioConfigDO> getAudioConfigListByStatus(Integer status) {
        return audioConfigMapper.selectList(status);
    }

    private List<byte[]> chunkData(byte[] data, int chunkSize) {
        int numOfChunks = (int) Math.ceil((double) data.length / chunkSize);
        List<byte[]> chunks = new ArrayList<>(numOfChunks);

        for (int i = 0; i < numOfChunks; i++) {
            int start = i * chunkSize;
            int length = Math.min(data.length - start, chunkSize);

            byte[] temp = new byte[length];
            System.arraycopy(data, start, temp, 0, length);
            chunks.add(temp);
        }

        return chunks;
    }

    private AiAudioConfigDO createAudioConfigDO(AudioConfigSaveReqVO reqVO) {
        AiAudioConfigDO audioConfig = new AiAudioConfigDO();
        if (null != reqVO.getId()){
            audioConfig = audioConfigMapper.selectById(reqVO.getId());
        }else {
            audioConfig = BeanUtils.toBean(reqVO, AiAudioConfigDO.class)
                    .setPlatform(reqVO.getPlatform()).setStatus(AiImageStatusEnum.WAITING.getStatus())
                    ;
            audioConfig.setCreateTime(LocalDateTime.now());
            audioConfigMapper.insert(audioConfig);
        }
        return audioConfig;
    }

    /**
     * 毫秒转化未秒 精确到小数点后四位
     */
    public static Double msToSeconds(Long milliseconds) {
        // 将毫秒转换为秒
        Double seconds = milliseconds / 1000.0;

        // 使用 DecimalFormat 格式化到小数点后两位
        DecimalFormat df = new DecimalFormat("#.0000");
        return Double.parseDouble(df.format(seconds));
    }
}
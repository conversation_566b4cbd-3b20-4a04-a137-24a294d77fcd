package cn.iocoder.yudao.module.ai.service.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import cn.iocoder.yudao.module.ai.dal.mysql.assistant.AiAssistantRoleMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;

/**
 * AI 聊天角色 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiAssistantRoleServiceImpl implements AiAssistantRoleService {

    @Resource
    private AiAssistantRoleMapper assistantRoleMapper;

    @Override
    public Long saveOrUpdateAssistantRole(AiAssistantRoleSaveReqVO createReqVO) {
        AiAssistantRoleDO assistantRole = BeanUtils.toBean(createReqVO, AiAssistantRoleDO.class);
        if (null == assistantRole.getId() || assistantRole.getId() == 0){
            assistantRoleMapper.insert(assistantRole);
        }else{
            // 校验存在
            validateAssistantRoleExists(assistantRole.getId());
            assistantRoleMapper.updateById(assistantRole);
        }
        return assistantRole.getId();
    }

    @Override
    public void updateAssistantRole(AiAssistantRoleSaveReqVO updateReqVO) {
        // 校验存在
        validateAssistantRoleExists(updateReqVO.getId());
        // 更新
        AiAssistantRoleDO updateObj = BeanUtils.toBean(updateReqVO, AiAssistantRoleDO.class);
        assistantRoleMapper.updateById(updateObj);
    }

    @Override
    public void deleteAssistantRole(Long id) {
        // 校验存在
        validateAssistantRoleExists(id);
        // 删除
        assistantRoleMapper.deleteById(id);
    }

    private AiAssistantRoleDO validateAssistantRoleExists(Long id) {
        AiAssistantRoleDO assistantRole = assistantRoleMapper.selectById(id);
        if (assistantRole == null) {
            throw exception(CHAT_ROLE_NOT_EXISTS);
        }
        return assistantRole;
    }

    @Override
    public AiAssistantRoleDO getAssistantRole() {
        return assistantRoleMapper.selectFirstByDefault(CommonStatusEnum.ENABLE.getStatus());
    }

    @Override
    public List<AiAssistantRoleDO> getAssistantRoleList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return assistantRoleMapper.selectBatchIds(ids);
    }

    @Override
    public AiAssistantRoleDO validateAssistantRole(Long id) {
        AiAssistantRoleDO assistantRole = validateAssistantRoleExists(id);
        if (CommonStatusEnum.isDisable(assistantRole.getStatus())) {
            throw exception(CHAT_ROLE_DISABLE, assistantRole.getName());
        }
        return assistantRole;
    }

    @Override
    public PageResult<AiAssistantRoleDO> getAssistantRolePage(AiAssistantRolePageReqVO pageReqVO) {
        return assistantRoleMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AiAssistantRoleDO> getAssistantRoleListByName(String name) {
        return assistantRoleMapper.selectListByName(name);
    }

    @Override
    public AiAssistantRoleDO getRequiredDefaultAssistantModel() {
        AiAssistantRoleDO model = assistantRoleMapper.selectFirstByDefault(CommonStatusEnum.ENABLE.getStatus());
        if (model == null) {
            throw exception(MODEL_DEFAULT_NOT_EXISTS);
        }
        return model;
    }

}


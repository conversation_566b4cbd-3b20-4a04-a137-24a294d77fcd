package cn.iocoder.yudao.module.ai.tool.method;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 来自 Spring AI 官方文档
 *
 * Implementation of the PersonService interface using an in-memory data store.
 * Manages a collection of Person objects loaded from embedded CSV data.
 * This class is thread-safe due to the use of ConcurrentHashMap and AtomicInteger.
 */
@Service
@Slf4j
public class PersonServiceImpl implements PersonService {

    private final Map<Integer, Person> personStore = new ConcurrentHashMap<>();

    private AtomicInteger idGenerator;

    /**
     * Embedded CSV data for initial population
     */
    private static final String CSV_DATA = """
            Id,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Age
            1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<EMAIL>,<PERSON>,55.1 <PERSON><PERSON><PERSON> <PERSON>,<PERSON> Associate,31
            2,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,et<PERSON><PERSON><PERSON>1@networks<PERSON><PERSON>s.com,<PERSON>,18 <PERSON><PERSON><PERSON> <PERSON>,<PERSON> <PERSON>,38
            3,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,s<PERSON><PERSON><PERSON>2@yellow<PERSON><PERSON>.com,<PERSON>,1 <PERSON><PERSON> <PERSON>,<PERSON><PERSON> <PERSON> <PERSON>,30
            4,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,s<PERSON><PERSON>3@map<PERSON>.com,<PERSON>,2 <PERSON><PERSON> <PERSON>,Chief <PERSON> <PERSON>,40
            5,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,h<PERSON><EMAIL>,Female,3 Kitteringham Drive,VP Sales,47
            6,Anallise,Parradine,<EMAIL>,Female,4 Parradine Street,Analog Circuit Design manager,44
            7,Gorden,Kirkbright,<EMAIL>,Male,5 Kirkbright Plaza,Senior Editor,40
            8,Veradis,Ledwitch,<EMAIL>,Female,6 Ledwitch Avenue,Computer Systems Analyst IV,44
            9,Agnesse,Penhalurick,<EMAIL>,Female,7 Penhalurick Terrace,Automation Specialist IV,41
            10,Bibby,Hutable,<EMAIL>,Female,8 Hutable Place,Account Representative I,43
            11,Karoly,Lightoller,<EMAIL>,Female,9 Lightoller Parkway,Senior Developer,46
            12,Cristine,Durrad,<EMAIL>,Female,10 Durrad Center,Senior Developer,48
            13,Aggy,Napier,<EMAIL>,Female,11 Napier Court,VP Product Management,44
            14,Prisca,Caddens,<EMAIL>,Female,12 Caddens Alley,Business Systems Development Analyst,41
            15,Khalil,McKernan,<EMAIL>,Male,13 McKernan Pass,Engineer IV,44
            16,Lorry,MacTrusty,<EMAIL>,Male,14 MacTrusty Junction,Design Engineer,42
            17,Casandra,Worsell,<EMAIL>,Female,15 Worsell Point,Systems Administrator IV,45
            18,Ulrikaumeko,Haveline,<EMAIL>,Female,16 Haveline Trail,Financial Advisor,42
            19,Shurlocke,Albany,<EMAIL>,Male,17 Albany Plaza,Software Test Engineer III,46
            20,Myrilla,Brimilcombe,<EMAIL>,Female,18 Brimilcombe Road,Programmer Analyst I,48
            21,Carlina,Scimonelli,<EMAIL>,Female,19 Scimonelli Pass,Help Desk Technician,45
            22,Tina,Goullee,<EMAIL>,Female,20 Goullee Crossing,Accountant IV,43
            23,Adriaens,Storek,<EMAIL>,Female,21 Storek Avenue,Recruiting Manager,40
            24,Tedra,Giraudot,<EMAIL>,Female,22 Giraudot Terrace,Speech Pathologist,47
            25,Josiah,Soares,<EMAIL>,Male,23 Soares Street,Tax Accountant,45
            26,Kayle,Gaukrodge,<EMAIL>,Female,24 Gaukrodge Parkway,Accountant II,43
            27,Ardys,Chuter,<EMAIL>,Female,25 Chuter Drive,Engineer IV,41
            28,Francyne,Baudinet,<EMAIL>,Female,26 Baudinet Center,VP Accounting,48
            29,Gerick,Bullan,<EMAIL>,Male,27 Bullan Way,Senior Financial Analyst,43
            30,Northrup,Grivori,<EMAIL>,Male,28 Grivori Plaza,Systems Administrator I,45
            31,Town,Duguid,<EMAIL>,Male,29 Duguid Pass,Safety Technician IV,46
            32,Pierette,Kopisch,<EMAIL>,Female,30 Kopisch Lane,Director of Sales,41
            33,Jacquenetta,Le Prevost,<EMAIL>,Female,31 Le Prevost Trail,Senior Developer,47
            34,Garvy,Rusted,<EMAIL>,Male,32 Rusted Junction,Senior Developer,42
            35,Clarice,Aysh,<EMAIL>,Female,33 Aysh Avenue,VP Quality Control,40
            36,Tracie,Fedorski,<EMAIL>,Male,34 Fedorski Terrace,Design Engineer,44
            37,Noelyn,Matushenko,<EMAIL>,Female,35 Matushenko Place,VP Sales,48
            38,Rudiger,Klaesson,<EMAIL>,Male,36 Klaesson Road,Database Administrator IV,43
            39,Mirella,Syddie,<EMAIL>,Female,37 Syddie Circle,Geological Engineer,46
            40,Donalt,O'Lunny,<EMAIL>,Male,38 O'Lunny Center,Analog Circuit Design manager,41
            41,Guntar,Deniskevich,<EMAIL>,Male,39 Deniskevich Way,Structural Engineer,47
            42,Hort,Shufflebotham,<EMAIL>,Male,40 Shufflebotham Court,Structural Analysis Engineer,45
            43,Dominique,Thickett,<EMAIL>,Male,41 Thickett Crossing,Safety Technician I,42
            44,Zebulen,Piscopello,<EMAIL>,Male,42 Piscopello Parkway,Web Developer II,40
            45,Mellicent,Mac Giany,<EMAIL>,Female,43 Mac Giany Pass,Assistant Manager,44
            46,Merle,Bounds,<EMAIL>,Female,44 Bounds Alley,Systems Administrator III,41
            47,Madelle,Farbrace,<EMAIL>,Female,45 Farbrace Terrace,Quality Engineer,48
            48,Galvin,O'Sheeryne,<EMAIL>,Male,46 O'Sheeryne Way,Environmental Specialist,43
            49,Guillemette,Bootherstone,<EMAIL>,Female,47 Bootherstone Plaza,Professor,46
            50,Letti,Aylmore,<EMAIL>,Female,48 Aylmore Circle,Automation Specialist I,40
            51,Nonie,Rivalland,<EMAIL>,Female,49 Rivalland Avenue,Software Test Engineer IV,45
            52,Jacquelynn,Halfacre,<EMAIL>,Female,50 Halfacre Pass,Geologist II,42
            53,Anderea,MacKibbon,<EMAIL>,Female,51 MacKibbon Parkway,Automation Specialist II,47
            54,Wash,Klimko,<EMAIL>,Male,52 Klimko Alley,Database Administrator I,40
            55,Flori,Kynett,<EMAIL>,Female,53 Kynett Trail,Quality Control Specialist,46
            56,Libbey,Penswick,<EMAIL>,Female,54 Penswick Point,VP Accounting,43
            57,Silvanus,Skellorne,<EMAIL>,Male,55 Skellorne Drive,Account Executive,48
            58,Carmine,Mateos,<EMAIL>,Male,56 Mateos Terrace,Systems Administrator I,41
            59,Sheffie,Blazewicz,<EMAIL>,Male,57 Blazewicz Center,VP Sales,44
            60,Leanor,Worsnop,<EMAIL>,Female,58 Worsnop Plaza,Systems Administrator III,45
            61,Caspar,Pamment,<EMAIL>,Male,59 Pamment Court,Senior Financial Analyst,42
            62,Justinian,Pentycost,<EMAIL>,Male,60 Pentycost Way,Senior Quality Engineer,47
            63,Gerianne,Jarnell,<EMAIL>,Female,61 Jarnell Avenue,Help Desk Operator,40
            64,Boycie,Zanetto,<EMAIL>,Male,62 Zanetto Place,Quality Engineer,46
            65,Camilla,Mac Giany,<EMAIL>,Female,63 Mac Giany Parkway,Senior Cost Accountant,43
            66,Hadlee,Piscopiello,<EMAIL>,Male,64 Piscopiello Street,Account Representative III,48
            67,Bobbie,Penvarden,<EMAIL>,Male,65 Penvarden Lane,Help Desk Operator,41
            68,Ali,Gowlett,<EMAIL>,Male,66 Gowlett Pass,VP Marketing,44
            69,Olivette,Acome,<EMAIL>,Female,67 Acome Hill,VP Product Management,45
            70,Jehanna,Brotherheed,<EMAIL>,Female,68 Brotherheed Junction,Database Administrator III,42
            71,Morgan,Berthomieu,<EMAIL>,Male,69 Berthomieu Alley,Systems Administrator II,47
            72,Linzy,Shilladay,<EMAIL>,Female,70 Shilladay Trail,Research Assistant IV,40
            73,Faydra,Brimner,<EMAIL>,Female,71 Brimner Road,Senior Editor,46
            74,Gwenore,Oxlee,<EMAIL>,Female,72 Oxlee Terrace,Systems Administrator II,43
            75,Evangelin,Beinke,<EMAIL>,Female,73 Beinke Circle,Accountant I,48
            76,Missy,Cockling,<EMAIL>,Female,74 Cockling Way,Software Engineer I,41
            77,Suzanne,Klimschak,<EMAIL>,Female,75 Klimschak Plaza,Tax Accountant,44
            78,Candide,Goricke,<EMAIL>,Female,76 Goricke Pass,Sales Associate,45
            79,Gerome,Pinsent,<EMAIL>,Male,77 Pinsent Junction,Software Consultant,42
            80,Lezley,Mac Giany,<EMAIL>,Male,78 Mac Giany Alley,Operator,47
            81,Tobiah,Durn,<EMAIL>,Male,79 Durn Court,VP Sales,40
            82,Sherlocke,Cockshoot,<EMAIL>,Male,80 Cockshoot Street,Senior Financial Analyst,46
            83,Myrle,Speenden,<EMAIL>,Female,81 Speenden Center,Senior Developer,43
            84,Isidore,Gorries,<EMAIL>,Male,82 Gorries Parkway,Sales Representative,48
            85,Isac,Kitchingman,<EMAIL>,Male,83 Kitchingman Drive,VP Accounting,41
            86,Benedetta,Purrier,<EMAIL>,Female,84 Purrier Trail,VP Accounting,44
            87,Tera,Fitchell,<EMAIL>,Female,85 Fitchell Place,Software Engineer IV,45
            88,Abbe,Pamment,<EMAIL>,Male,86 Pamment Avenue,VP Sales,42
            89,Jandy,Gommowe,<EMAIL>,Female,87 Gommowe Road,Financial Analyst,47
            90,Karena,Fussey,<EMAIL>,Female,88 Fussey Point,Assistant Professor,40
            91,Gaspar,Pammenter,<EMAIL>,Male,89 Pammenter Hill,Help Desk Operator,46
            92,Stanwood,Mac Giany,<EMAIL>,Male,90 Mac Giany Terrace,Research Associate,43
            93,Byrom,Beedell,<EMAIL>,Male,91 Beedell Way,VP Sales,48
            94,Annabella,Rowbottom,<EMAIL>,Female,92 Rowbottom Plaza,Help Desk Operator,41
            95,Rodolphe,Debell,<EMAIL>,Male,93 Debell Pass,Design Engineer,44
            96,Tyne,Gommey,<EMAIL>,Female,94 Gommey Junction,VP Marketing,45
            97,Christoper,Pincked,<EMAIL>,Male,95 Pincked Alley,Human Resources Manager,42
            98,Kore,Le Prevost,<EMAIL>,Female,96 Le Prevost Street,VP Quality Control,47
            99,Ceciley,Petrolli,<EMAIL>,Female,97 Petrolli Court,Senior Developer,40
            100,Elspeth,Mac Giany,<EMAIL>,Female,98 Mac Giany Parkway,Internal Auditor,46
            """;

    /**
     * Initializes the service after dependency injection by loading data from the CSV string.
     * Uses @PostConstruct to ensure this runs after the bean is created.
     */
    @PostConstruct
    private void initializeData() {
        log.info("Initializing PersonService data store...");
        int maxId = loadDataFromCsv();
        idGenerator = new AtomicInteger(maxId);
        log.info("PersonService initialized with {} records. Next ID: {}", personStore.size(), idGenerator.get() + 1);
    }

    /**
     * Parses the embedded CSV data and populates the in-memory store.
     * Calculates the maximum ID found in the data to initialize the ID generator.
     *
     * @return The maximum ID found in the loaded CSV data.
     */
    private int loadDataFromCsv() {
        final AtomicInteger currentMaxId = new AtomicInteger(0);
        // Clear existing data before loading (important for tests or re-initialization scenarios)
        personStore.clear();
        try (Stream<String> lines = CSV_DATA.lines().skip(1)) { // Skip header row
            lines.forEach(line -> {
                try {
                    // Split carefully, handling potential commas within quoted fields if necessary (simple split here)
                    String[] fields = line.split(",", 8); // Limit split to handle potential commas in job title
                    if (fields.length == 8) {
                        int id = Integer.parseInt(fields[0].trim());
                        String firstName = fields[1].trim();
                        String lastName = fields[2].trim();
                        String email = fields[3].trim();
                        String sex = fields[4].trim();
                        String ipAddress = fields[5].trim();
                        String jobTitle = fields[6].trim();
                        int age = Integer.parseInt(fields[7].trim());

                        Person person = new Person(id, firstName, lastName, email, sex, ipAddress, jobTitle, age);
                        personStore.put(id, person);
                        currentMaxId.updateAndGet(max -> Math.max(max, id));
                    } else {
                        log.warn("Skipping malformed CSV line (expected 8 fields, found {}): {}", fields.length, line);
                    }
                } catch (NumberFormatException e) {
                    log.warn("Skipping line due to parsing error (ID or Age): {} - Error: {}", line, e.getMessage());
                } catch (Exception e) {
                    log.error("Skipping line due to unexpected error: {} - Error: {}", line, e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("Fatal error reading embedded CSV data: {}", e.getMessage(), e);
            // In a real application, might throw a specific initialization exception
        }
        return currentMaxId.get();
    }

    @Override
    @Tool(
        name = "ps_create_person",
        description = "Create a new person record in the in-memory store."
    )
    public Person createPerson(Person personData) {
        if (personData == null) {
            throw new IllegalArgumentException("Person data cannot be null");
        }
        int newId = idGenerator.incrementAndGet();
        // Create a new Person record using data from the input, but with the generated ID
        Person newPerson = new Person(
                newId,
                personData.firstName(),
                personData.lastName(),
                personData.email(),
                personData.sex(),
                personData.ipAddress(),
                personData.jobTitle(),
                personData.age()
        );
        personStore.put(newId, newPerson);
        log.debug("Created person: {}", newPerson);
        return newPerson;
    }

    @Override
    @Tool(
        name = "ps_get_person_by_id",
        description = "Retrieve a person record by ID from the in-memory store."
    )
    public Optional<Person> getPersonById(int id) {
        Person person = personStore.get(id);
        log.debug("Retrieved person by ID {}: {}", id, person);
        return Optional.ofNullable(person);
    }

    @Override
    @Tool(
        name = "ps_get_all_persons",
        description = "Retrieve all person records from the in-memory store."
    )
    public List<Person> getAllPersons() {
        // Return an unmodifiable view of the values
        List<Person> allPersons = personStore.values().stream().toList();
        log.debug("Retrieved all persons (count: {})", allPersons.size());
        return allPersons;
    }

    @Override
    @Tool(
        name = "ps_update_person",
        description = "Update an existing person record by ID in the in-memory store."
    )
    public boolean updatePerson(int id, Person updatedPersonData) {
         if (updatedPersonData == null) {
            throw new IllegalArgumentException("Updated person data cannot be null");
        }
        // Use computeIfPresent for atomic update if the key exists
        Person result = personStore.computeIfPresent(id, (key, existingPerson) ->
                // Create a new Person record with the original ID but updated data
                new Person(
                        id, // Keep original ID
                        updatedPersonData.firstName(),
                        updatedPersonData.lastName(),
                        updatedPersonData.email(),
                        updatedPersonData.sex(),
                        updatedPersonData.ipAddress(),
                        updatedPersonData.jobTitle(),
                        updatedPersonData.age()
                )
        );
        boolean updated = result != null;
        log.debug("Update attempt for ID {}: {}", id, updated ? "Successful" : "Failed (Not Found)");
        if(updated) log.trace("Updated person data for ID {}: {}", id, result);
        return updated;
    }

    @Override
    @Tool(
        name = "ps_delete_person",
        description = "Delete a person record by ID from the in-memory store."
    )
    public boolean deletePerson(int id) {
        boolean removed = personStore.remove(id) != null;
        log.debug("Delete attempt for ID {}: {}", id, removed ? "Successful" : "Failed (Not Found)");
        return removed;
    }

    @Override
    @Tool(
        name = "ps_search_by_job_title",
        description = "Search for persons by job title in the in-memory store."
    )
    public List<Person> searchByJobTitle(String jobTitleQuery) {
        if (jobTitleQuery == null || jobTitleQuery.isBlank()) {
            log.debug("Search by job title skipped due to blank query.");
            return Collections.emptyList();
        }
        String lowerCaseQuery = jobTitleQuery.toLowerCase();
        List<Person> results = personStore.values().stream()
                .filter(person -> person.jobTitle() != null && person.jobTitle().toLowerCase().contains(lowerCaseQuery))
                .collect(Collectors.toList());
        log.debug("Search by job title '{}' found {} results.", jobTitleQuery, results.size());
        return Collections.unmodifiableList(results);
    }

    @Override
    @Tool(
        name = "ps_filter_by_sex",
        description = "Filters Persons by sex (case-insensitive)."
    )
    public List<Person> filterBySex(String sex) {
        if (sex == null || sex.isBlank()) {
             log.debug("Filter by sex skipped due to blank filter.");
            return Collections.emptyList();
        }
        List<Person> results = personStore.values().stream()
                .filter(person -> person.sex() != null && person.sex().equalsIgnoreCase(sex))
                .collect(Collectors.toList());
        log.debug("Filter by sex '{}' found {} results.", sex, results.size());
        return Collections.unmodifiableList(results);
    }

    @Override
    @Tool(
        name = "ps_filter_by_age",
        description = "Filters Persons by age."
    )
    public List<Person> filterByAge(int age) {
         if (age < 0) {
            log.debug("Filter by age skipped due to negative age: {}", age);
            return Collections.emptyList(); // Or throw IllegalArgumentException based on requirements
        }
        List<Person> results = personStore.values().stream()
                .filter(person -> person.age() == age)
                .collect(Collectors.toList());
        log.debug("Filter by age {} found {} results.", age, results.size());
        return Collections.unmodifiableList(results);
    }

}
package cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.conversation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConversationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 内容写作 对话 DO Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentWriteConversationMapper extends BaseMapperX<AiContentWriteConversationDO> {

    default PageResult<AiContentWriteConversationDO> selectPage(ContentWriteConversationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiContentWriteConversationDO>()
                .betweenIfPresent(AiContentWriteConversationDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiContentWriteConversationDO::getMaxContexts, reqVO.getMaxContexts())
                .eqIfPresent(AiContentWriteConversationDO::getMaxTokens, reqVO.getMaxTokens())
                .eqIfPresent(AiContentWriteConversationDO::getModel, reqVO.getModel())
                .eqIfPresent(AiContentWriteConversationDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AiContentWriteConversationDO::getPinned, reqVO.getPinned())
                .betweenIfPresent(AiContentWriteConversationDO::getPinnedTime, reqVO.getPinnedTime())
                .eqIfPresent(AiContentWriteConversationDO::getRoleId, reqVO.getRoleId())
                .eqIfPresent(AiContentWriteConversationDO::getSystemMessage, reqVO.getSystemMessage())
                .eqIfPresent(AiContentWriteConversationDO::getTemperature, reqVO.getTemperature())
                .eqIfPresent(AiContentWriteConversationDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AiContentWriteConversationDO::getTopP, reqVO.getTopP())
                .eqIfPresent(AiContentWriteConversationDO::getUserId, reqVO.getUserId())
                .orderByDesc(AiContentWriteConversationDO::getId));
    }

    default PageResult<AiContentWriteConversationDO> selectPage(Long userId, AppContentWriteConversationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiContentWriteConversationDO>()
                .betweenIfPresent(AiContentWriteConversationDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiContentWriteConversationDO::getModel, reqVO.getModel())
                .eqIfPresent(AiContentWriteConversationDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AiContentWriteConversationDO::getRoleId, reqVO.getRoleId())
                .eqIfPresent(AiContentWriteConversationDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AiContentWriteConversationDO::getUserId, userId)
                .orderByDesc(AiContentWriteConversationDO::getId));
    }

    default AiContentWriteConversationDO selectByUserIdDesc(Long userId){
        return selectOne(new LambdaQueryWrapperX<AiContentWriteConversationDO>()
                .eq(AiContentWriteConversationDO::getUserId, userId)
                .orderByDesc(AiContentWriteConversationDO::getId)
                .last("LIMIT 1")
        );
    };
}
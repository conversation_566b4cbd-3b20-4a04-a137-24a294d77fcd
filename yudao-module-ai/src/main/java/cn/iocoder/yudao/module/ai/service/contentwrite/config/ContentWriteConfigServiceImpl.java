package cn.iocoder.yudao.module.ai.service.contentwrite.config;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.config.ContentWriteConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CONTENT_WRITE_CONFIG_NOT_EXISTS;

/**
 * AI 内容创作配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContentWriteConfigServiceImpl implements ContentWriteConfigService {

    @Resource
    private ContentWriteConfigMapper contentWriteConfigMapper;

    @Override
    public Long createContentWriteConfig(ContentWriteConfigSaveReqVO createReqVO) {
        // 插入
        AiContentWriteConfigDO contentWriteConfig = BeanUtils.toBean(createReqVO, AiContentWriteConfigDO.class);
        contentWriteConfigMapper.insert(contentWriteConfig);
        // 返回
        return contentWriteConfig.getId();
    }

    @Override
    public void updateContentWriteConfig(ContentWriteConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateContentWriteConfigExists(updateReqVO.getId());
        // 更新
        AiContentWriteConfigDO updateObj = BeanUtils.toBean(updateReqVO, AiContentWriteConfigDO.class);
        contentWriteConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteContentWriteConfig(Long id) {
        // 校验存在
        validateContentWriteConfigExists(id);
        // 删除
        contentWriteConfigMapper.deleteById(id);
    }

    private void validateContentWriteConfigExists(Long id) {
        if (contentWriteConfigMapper.selectById(id) == null) {
            throw exception(CONTENT_WRITE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public AiContentWriteConfigDO getContentWriteConfig(Long id) {
        return contentWriteConfigMapper.selectById(id);
    }

    @Override
    public PageResult<AiContentWriteConfigDO> getContentWriteConfigPage(ContentWriteConfigPageReqVO pageReqVO) {
        return contentWriteConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public Long saveOrUpdateContentWriteConfig(ContentWriteConfigSaveReqVO createReqVO) {
        // 插入
        AiContentWriteConfigDO contentWriteConfig = BeanUtils.toBean(createReqVO, AiContentWriteConfigDO.class);
        if (null == createReqVO.getId() || createReqVO.getId() == 0){
            contentWriteConfigMapper.insert(contentWriteConfig);
        }else{
            // 校验存在
            validateContentWriteConfigExists(createReqVO.getId());
            contentWriteConfigMapper.updateById(contentWriteConfig);
        }
        // 返回
        return contentWriteConfig.getId();
    }

    @Override
    public AiContentWriteConfigDO getLastOneContentWriteConfig() {
        return contentWriteConfigMapper.selectDefaultConfig();
    }

}
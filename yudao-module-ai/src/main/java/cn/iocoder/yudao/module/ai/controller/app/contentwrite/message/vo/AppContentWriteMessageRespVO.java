package cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 内容创作 消息 Response VO")
@Data
public class AppContentWriteMessageRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "30654")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "聊天内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    @Schema(description = "对话编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3386")
    private Long conversationId;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private String model;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12500")
    private Long modelId;

    @Schema(description = "点赞类型 点赞1 点踩2", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer praiseType;

    @Schema(description = "回复消息编号", example = "26860")
    private Long replyId;

    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String type;

    @Schema(description = "是否携带上下文", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean useContext;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18130")
    private Long userId;

    // ========== 仅在【对话管理】时加载 ==========

    @Schema(description = "角色名字", example = "小黄")
    private String roleName;

}
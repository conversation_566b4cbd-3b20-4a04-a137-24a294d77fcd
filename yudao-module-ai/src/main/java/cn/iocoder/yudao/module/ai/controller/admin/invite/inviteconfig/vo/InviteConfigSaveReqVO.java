package cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 邀请设置新增/修改 Request VO")
@Data
public class InviteConfigSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "24246")
    private Long id;

    @Schema(description = "邀请图片", example = "https://www.iocoder.cn")
    private String imageUrl;

}
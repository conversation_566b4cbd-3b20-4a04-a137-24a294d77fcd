package cn.iocoder.yudao.module.ai.dto;

import lombok.Data;
import org.springframework.ai.chat.metadata.ChatResponseMetadata;
import org.springframework.ai.chat.model.Generation;

/**
 * @Description: TODO
 * @Date: 2025/1/19 18:13
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class MinMaxAllResponse {

    private ChatResponseMetadata chatResponseMetadata;
    private Generation generation;
}

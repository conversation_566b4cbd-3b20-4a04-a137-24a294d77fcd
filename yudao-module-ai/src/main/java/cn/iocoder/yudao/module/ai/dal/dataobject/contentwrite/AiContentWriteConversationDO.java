package cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * AI ContentWrite 对话 DO
 *
 * 用户每次发起 assistant 聊天时，会创建一个 {@link AiContentWriteConversationDO} 对象，将它的消息关联在一起
 *
 */
@Table(name = "ai_content_write_conversation")
@Comment(value = "AI 内容写作 对话 DO")
@Entity
@TableName("ai_content_write_conversation")
@KeySequence("ai_content_write_conversation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiContentWriteConversationDO extends TenantBaseDO {

    public static final String TITLE_DEFAULT = "新对话";

    /**
     * ID 编号，自增
     */
    @Id
    @Comment(value = "编号")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 用户编号
     *
     * 关联 AdminUserDO 的 userId 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '用户编号'")
    private Long userId;

    /**
     * 对话标题
     *
     * 默认由系统自动生成，可用户手动修改
     */
    @Column(length = 256, columnDefinition = "varchar(256) NOT NULL COMMENT '对话标题'")
    private String title;
    /**
     * 是否置顶
     */
    @Column(columnDefinition = "bit(1) NOT NULL COMMENT '是否置顶'")
    private Boolean pinned;
    /**
     * 置顶时间
     */
    @Column(columnDefinition = "datetime COMMENT '置顶时间'")
    private LocalDateTime pinnedTime;

    /**
     * 角色编号
     *
     * 关联 {@link AiAssistantRoleDO#getId()}
     */
    @Column(columnDefinition = "bigint COMMENT '角色编号'")
    private Long roleId;

    /**
     * 模型编号
     *
     * 关联 {@link AiYyModelDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint COMMENT '模型编号'")
    private Long modelId;
    /**
     * 模型标志
     */
    @Column(length = 32, columnDefinition = "varchar(32) NOT NUll COMMENT '模型标志'")
    private String model;

    // ========== 对话配置 ==========

    /**
     * 角色设定
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '角色设定'")
    private String systemMessage;
    /**
     * 温度参数
     *
     * 用于调整生成回复的随机性和多样性程度：较低的温度值会使输出更收敛于高频词汇，较高的则增加多样性
     */
    @Column(columnDefinition = "double COMMENT '温度参数'")
    private Double temperature;

    /**
     * 采样方法
     *
     * 用于控制生成回复的多样性，取值范围在 0 到 1 之间，值越小，输出的回复越集中，值越大，输出的回复越分散
     */
    @Column(name = "top_p", columnDefinition = "double COMMENT '采样方法'")
    private Double topP;

    /**
     * 单条回复的最大 Token 数量
     */
    @Column(columnDefinition = "int COMMENT '单条回复的最大 Token 数量'")
    private Integer maxTokens;
    /**
     * 上下文的最大 Message 数量
     */
    @Column(columnDefinition = "int COMMENT '上下文的最大 Message 数量'")
    private Integer maxContexts;

}

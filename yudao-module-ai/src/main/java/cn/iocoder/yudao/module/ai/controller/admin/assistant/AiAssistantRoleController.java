package cn.iocoder.yudao.module.ai.controller.admin.assistant;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRoleRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import cn.iocoder.yudao.module.ai.service.assistant.AiAssistantRoleService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI 智能助手角色-基础配置")
@RestController
@RequestMapping("/ai/assistant-role")
@Validated
public class AiAssistantRoleController {

    @Resource
    private AiAssistantRoleService assistantRoleService;

    // ========== 角色管理 ==========

    @PostMapping("/createOrUpdate")
    @Operation(summary = "创建聊天角色")
    @PreAuthorize("@ss.hasPermission('ai:assistant-role:create')")
    public CommonResult<Long> saveOrUpdateAssistantRole(@Valid @RequestBody AiAssistantRoleSaveReqVO createReqVO) {
        return success(assistantRoleService.saveOrUpdateAssistantRole(createReqVO));
    }

    @Hidden
    @DeleteMapping("/delete")
    @Operation(summary = "删除聊天角色")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:assistant-role:delete')")
    public CommonResult<Boolean> deleteAssistantRole(@RequestParam("id") Long id) {
        assistantRoleService.deleteAssistantRole(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得聊天角色")
    @PreAuthorize("@ss.hasPermission('ai:assistant-role:query')")
    public CommonResult<AiAssistantRoleRespVO> getAssistantRole() {
        AiAssistantRoleDO AssistantRole = assistantRoleService.getAssistantRole();
        return success(BeanUtils.toBean(AssistantRole, AiAssistantRoleRespVO.class));
    }

    @Hidden
    @GetMapping("/page")
    @Operation(summary = "获得聊天角色分页")
    @PreAuthorize("@ss.hasPermission('ai:assistant-role:query')")
    public CommonResult<PageResult<AiAssistantRoleRespVO>> getAssistantRolePage(@Valid AiAssistantRolePageReqVO pageReqVO) {
        PageResult<AiAssistantRoleDO> pageResult = assistantRoleService.getAssistantRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AiAssistantRoleRespVO.class));
    }

}

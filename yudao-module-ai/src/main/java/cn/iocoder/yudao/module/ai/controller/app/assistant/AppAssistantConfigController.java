package cn.iocoder.yudao.module.ai.controller.app.assistant;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.promptwords.AppPromptWordsRespVO;
import cn.iocoder.yudao.module.ai.enums.promptwords.PromptWordsTypeEnum;
import cn.iocoder.yudao.module.ai.service.image.config.DrawImageConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @Description: 获取绘画配置信息
 * @Date: 2024/7/28 20:10
 * @Author: zhangq
 * @Version: 1.0
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping( "/ai/assistant-config")
@Tag(name = "用户 APP - 获取智能助手对话配置信息")
public class AppAssistantConfigController {


    @Resource
    private DrawImageConfigService drawImageConfigService;

    /**
     * 获取系统默认打招呼-提示词
     */
    @GetMapping("/get-inspiration")
    @Operation(summary = "智能助手对话-获取打招呼提示词")
    public CommonResult<AppPromptWordsRespVO>  getAssistantInspiration() {
        AppPromptWordsRespVO respVO = drawImageConfigService.getInspiration(getLoginUserId(), PromptWordsTypeEnum.ASSISTANT_GREETING.getType());
        return success(respVO);
    }

    /**
     * 获取示例-提示词
     */
    @GetMapping("/get-quality")
    @Operation(summary = "智能助手对话-获取示例提示词")
    public CommonResult<List<AppPromptWordsRespVO>> getAssistantQuality() {
        List<AppPromptWordsRespVO> respVOS = drawImageConfigService.getQuality(getLoginUserId(),PromptWordsTypeEnum.ASSISTANT_PROMPT_WORDS.getType());
        return success(respVOS);
    }
}

package cn.iocoder.yudao.module.ai.dal.mysql.model;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.apikey.AiYyApiKeyPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyApiKeyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI API 密钥 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiYyApiKeyMapper extends BaseMapperX<AiYyApiKeyDO> {

    default PageResult<AiYyApiKeyDO> selectPage(AiYyApiKeyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiYyApiKeyDO>()
                .likeIfPresent(AiYyApiKeyDO::getName, reqVO.getName())
                .eqIfPresent(AiYyApiKeyDO::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AiYyApiKeyDO::getStatus, reqVO.getStatus())
                .orderByDesc(AiYyApiKeyDO::getId));
    }

    default AiYyApiKeyDO selectFirstByPlatformAndStatus(String platform, Integer status) {
        return selectOne(new QueryWrapperX<AiYyApiKeyDO>()
                .eq("platform", platform)
                .eq("status", status)
                .limitN(1)
                .orderByAsc("id"));
    }

}
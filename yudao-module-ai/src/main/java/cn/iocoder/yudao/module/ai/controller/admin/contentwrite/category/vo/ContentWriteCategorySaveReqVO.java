package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - AI 内容创作分类新增/修改 Request VO")
@Data
public class ContentWriteCategorySaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "29933")
    private Long id;

    @Schema(description = "分类名称", example = "王五")
    private String name;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "是否推荐", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "是否推荐不能为空")
    private Integer recommend;

}
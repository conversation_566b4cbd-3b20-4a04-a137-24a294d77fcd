package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo.ContentWriteMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo.ContentWriteMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo.ContentWriteMessageSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteMessageDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.message.ContentWriteMessageService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Hidden
@Tag(name = "管理后台 - AI 内容创作 消息")
@RestController
@RequestMapping("/ai/content-write-message")
@Validated
public class ContentWriteMessageController {

    @Resource
    private ContentWriteMessageService contentWriteMessageService;

    @PostMapping("/create")
    @Operation(summary = "创建AI 内容创作 消息")
    @PreAuthorize("@ss.hasPermission('ai:content-write-message:create')")
    public CommonResult<Long> createContentWriteMessage(@Valid @RequestBody ContentWriteMessageSaveReqVO createReqVO) {
        return success(contentWriteMessageService.createContentWriteMessage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI 内容创作 消息")
    @PreAuthorize("@ss.hasPermission('ai:content-write-message:update')")
    public CommonResult<Boolean> updateContentWriteMessage(@Valid @RequestBody ContentWriteMessageSaveReqVO updateReqVO) {
        contentWriteMessageService.updateContentWriteMessage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 内容创作 消息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:content-write-message:delete')")
    public CommonResult<Boolean> deleteContentWriteMessage(@RequestParam("id") Long id) {
        contentWriteMessageService.deleteContentWriteMessage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI 内容创作 消息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:content-write-message:query')")
    public CommonResult<ContentWriteMessageRespVO> getContentWriteMessage(@RequestParam("id") Long id) {
        AiContentWriteMessageDO contentWriteMessage = contentWriteMessageService.getContentWriteMessage(id);
        return success(BeanUtils.toBean(contentWriteMessage, ContentWriteMessageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI 内容创作 消息分页")
    @PreAuthorize("@ss.hasPermission('ai:content-write-message:query')")
    public CommonResult<PageResult<ContentWriteMessageRespVO>> getContentWriteMessagePage(@Valid ContentWriteMessagePageReqVO pageReqVO) {
        PageResult<AiContentWriteMessageDO> pageResult = contentWriteMessageService.getContentWriteMessagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContentWriteMessageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出AI 内容创作 消息 Excel")
    @PreAuthorize("@ss.hasPermission('ai:content-write-message:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContentWriteMessageExcel(@Valid ContentWriteMessagePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiContentWriteMessageDO> list = contentWriteMessageService.getContentWriteMessagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI 内容创作 消息.xls", "数据", ContentWriteMessageRespVO.class,
                        BeanUtils.toBean(list, ContentWriteMessageRespVO.class));
    }

}
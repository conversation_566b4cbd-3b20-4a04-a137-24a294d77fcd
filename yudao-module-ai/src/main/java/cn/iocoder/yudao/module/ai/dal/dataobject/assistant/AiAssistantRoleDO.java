package cn.iocoder.yudao.module.ai.dal.dataobject.assistant;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * @Description: 智能助手
 * @Date: 2024/12/22 17:11
 * @Author: zhangq
 * @Version: 1.0
 */
@Table(name = "ai_assistant")
@Comment(value = "AI 智能助手")
@Entity
@TableName(value = "ai_assistant", autoResultMap = true)
@KeySequence("ai_assistant_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiAssistantRoleDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 名称
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '名称'")
    private String name;

    /**
     * 消耗算力
     */
    @Column(columnDefinition = "int COMMENT '消耗算力'")
    private Integer coin;

    /**
     * 角色设定
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '角色设定'")
    private String systemMessage;

    @Column(columnDefinition = "double COMMENT '随机值/温度参数'")
    private Double temperature;

    /**
     * 采样方法 Top_p
     */
    @Column(name =  "top_p", columnDefinition = "double COMMENT '采样方法'")
    private Double topP;

    /**
     * 模型编号
     *
     * 关联 {@link AiYyModelDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint COMMENT '模型编号'")
    private Long modelId;
    /**
     * 模型标志
     */
    @Column(length = 32, columnDefinition = "varchar(32) NOT NUll COMMENT '模型标志'")
    private String model;

    /**
     * 默认
     */
    @Column(columnDefinition = "bit(1) COMMENT '是否默认'")
    private Integer defaultRole;

    /**
     * 排序值
     */
    @Column(columnDefinition = "int COMMENT '排序值'")
    private Integer sort;

    /**
     * 状态
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '是否启用'")
    private Integer status;

    /**
     * 单条回复的最大 Token 数量
     */
    @Column(columnDefinition = "int COMMENT '单条回复的最大 Token 数量'")
    private Integer maxTokens;
    /**
     * 上下文的最大 Message 数量
     */
    @Column(columnDefinition = "int COMMENT '上下文的最大 Message 数量'")
    private Integer maxContexts;
}

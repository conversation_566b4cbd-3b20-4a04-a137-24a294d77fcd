package cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.virtualpartner;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 虚拟陪伴角色 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VirtualPartnerRoleMapper extends BaseMapperX<AiVirtualPartnerRoleDO> {

    default PageResult<AiVirtualPartnerRoleDO> selectPage(VirtualPartnerRolePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiVirtualPartnerRoleDO>()
                .betweenIfPresent(AiVirtualPartnerRoleDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiVirtualPartnerRoleDO::getAudioId, reqVO.getAudioId())
                .eqIfPresent(AiVirtualPartnerRoleDO::getBackgroundUrl, reqVO.getBackgroundUrl())
                .eqIfPresent(AiVirtualPartnerRoleDO::getClickCount, reqVO.getClickCount())
                .eqIfPresent(AiVirtualPartnerRoleDO::getCover, reqVO.getCover())
                .eqIfPresent(AiVirtualPartnerRoleDO::getCoverDesc, reqVO.getCoverDesc())
                .eqIfPresent(AiVirtualPartnerRoleDO::getCoverUrl, reqVO.getCoverUrl())
                .eqIfPresent(AiVirtualPartnerRoleDO::getCoverVoiceFile, reqVO.getCoverVoiceFile())
                .eqIfPresent(AiVirtualPartnerRoleDO::getInputPresetWord, reqVO.getInputPresetWord())
                .eqIfPresent(AiVirtualPartnerRoleDO::getModel, reqVO.getModel())
                .eqIfPresent(AiVirtualPartnerRoleDO::getModelId, reqVO.getModelId())
                .likeIfPresent(AiVirtualPartnerRoleDO::getName, reqVO.getName())
                .eqIfPresent(AiVirtualPartnerRoleDO::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AiVirtualPartnerRoleDO::getPresetExample, reqVO.getPresetExample())
                .eqIfPresent(AiVirtualPartnerRoleDO::getSort, reqVO.getSort())
                .eqIfPresent(AiVirtualPartnerRoleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AiVirtualPartnerRoleDO::getStoryDesc, reqVO.getStoryDesc())
                .eqIfPresent(AiVirtualPartnerRoleDO::getSystemMessage, reqVO.getSystemMessage())
                .eqIfPresent(AiVirtualPartnerRoleDO::getTemperature, reqVO.getTemperature())
                .eqIfPresent(AiVirtualPartnerRoleDO::getTopP, reqVO.getTopP())
                .orderByDesc(AiVirtualPartnerRoleDO::getId));
    }

    default PageResult<AiVirtualPartnerRoleDO> selectAppPage(AppVirtualPartnerRolePageReqVO pageReqVO){
       return selectPage(pageReqVO, new LambdaQueryWrapperX<AiVirtualPartnerRoleDO>()
                .betweenIfPresent(AiVirtualPartnerRoleDO::getCreateTime, pageReqVO.getCreateTime())
                .eqIfPresent(AiVirtualPartnerRoleDO::getStatus, pageReqVO.getStatus())
                .orderByDesc(AiVirtualPartnerRoleDO::getId));
    }
}
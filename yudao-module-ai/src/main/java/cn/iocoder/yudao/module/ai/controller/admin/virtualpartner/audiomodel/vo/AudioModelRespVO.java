package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 音色模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AudioModelRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14088")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "描述", example = "随便")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "排序值")
    @ExcelProperty("排序值")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "音色ID", example = "842")
    @ExcelProperty("音色ID")
    private String voiceId;

    @Schema(description = "音色名称", example = "张三")
    @ExcelProperty("音色名称")
    private String voiceName;

    @Schema(description = "语音类型列表，以逗号分隔", example = "1,2")
    @ExcelProperty("语音类型列表，以逗号分隔")
    private List<Integer> voiceTypes;

}
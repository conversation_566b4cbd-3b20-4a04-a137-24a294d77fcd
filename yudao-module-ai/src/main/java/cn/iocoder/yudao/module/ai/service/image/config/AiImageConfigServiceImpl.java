package cn.iocoder.yudao.module.ai.service.image.config;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.image.config.vo.ImageConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.image.config.AiImageConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.IMAGE_CONFIG_NOT_EXISTS;

/**
 * @Description: TODO
 * @Date: 2024/12/19 21:29
 * @Author: zhangq
 * @Version: 1.0
 */
@Service
public class AiImageConfigServiceImpl implements AiImageConfigService{

    @Resource
    private AiImageConfigMapper aiImageConfigMapper;

    @Override
    public Long saveOrUpdateImageConfig(ImageConfigSaveReqVO createReqVO) {
        // 插入
        AiImageConfigDO configDO = BeanUtils.toBean(createReqVO, AiImageConfigDO.class);
        if (null == configDO.getId() || configDO.getId() == 0){
            aiImageConfigMapper.insert(configDO);
        }else{
            // 校验存在
            validateImageConfigExists(configDO.getId());
            aiImageConfigMapper.updateById(configDO);
        }
        // 返回
        return configDO.getId();
    }

    private void validateImageConfigExists(Long id) {
        if (aiImageConfigMapper.selectById(id) == null) {
            throw exception(IMAGE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public AiImageConfigDO getImageConfig() {
        return aiImageConfigMapper.selectLast();
    }
}

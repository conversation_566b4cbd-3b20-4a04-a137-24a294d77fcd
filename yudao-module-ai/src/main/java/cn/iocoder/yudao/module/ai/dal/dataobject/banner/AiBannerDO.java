package cn.iocoder.yudao.module.ai.dal.dataobject.banner;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.enums.banner.BannerPositionEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * banner DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_banner")
@Comment(value = "AI banner配置")
@Entity
@TableName("ai_banner")
@KeySequence("ai_banner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiBannerDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;
    /**
     * 标题
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '标题'")
    private String title;
    /**
     * 跳转链接
     */
    @Column(length = 255, columnDefinition = "varchar(255) COMMENT '跳转链接'")
    private String url;
    /**
     * 图片链接
     */
    @Column(columnDefinition = "varchar(255) COMMENT '图片链接'")
    private String picUrl;
    /**
     * 排序
     */
    @Column(columnDefinition = "int COMMENT '排序'")
    private Integer sort;

    /**
     * 状态 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "int COMMENT '状态'")
    private Integer status;

    /**
     * 定位 {@link BannerPositionEnum}
     */
    @Column(columnDefinition = "int COMMENT '定位'")
    private Integer position;

    /**
     * 备注
     */
    @Column(length = 255, columnDefinition = "varchar(255) COMMENT '备注'")
    private String memo;

    /**
     * 点击次数
     */
    @Column(columnDefinition = "int COMMENT '点击次数'")
    private Integer browseCount;

}

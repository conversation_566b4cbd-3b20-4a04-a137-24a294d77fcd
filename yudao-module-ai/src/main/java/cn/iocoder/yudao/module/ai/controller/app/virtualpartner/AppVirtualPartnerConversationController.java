package cn.iocoder.yudao.module.ai.controller.app.virtualpartner;

import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AppAiVirtualPartnerConversationRespVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AppAiVirtualpartnerConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.AiVirtualPartnerConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Slf4j
@Tag(name = "用户 APP - AI 虚拟陪伴角色对话")
@RestController
@RequestMapping("/ai/virtualpartner/conversation")
@Validated
public class AppVirtualPartnerConversationController {

    @Resource
    private AiVirtualPartnerConversationService virtualpartnerConversationService;

    @PostMapping("/create-my")
    @Operation(summary = "创建【我的】聊天对话")
    public CommonResult<Long> createVirtualPartnerConversationMy(@RequestBody @Valid AppAiVirtualpartnerConversationCreateMyReqVO createReqVO) {
        return success(virtualpartnerConversationService.createVirtualpartnerConversationMy(createReqVO, getLoginUserId()));
    }

    @GetMapping("/my-list")
    @Operation(summary = "获得【我的】聊天对话列表-按时间倒序排-取最新的前30条保证速度")
    public CommonResult<List<AppAiVirtualPartnerConversationRespVO>> getVirtualPartnerConversationMyList() {
        List<AiVirtualPartnerConversationDO> list = virtualpartnerConversationService.getVirtualPartnerConversationAppListByUserId(getLoginUserId());
        return success(BeanUtils.toBean(list, AppAiVirtualPartnerConversationRespVO.class));
    }

    @GetMapping("/get-my")
    @Operation(summary = "获得【我的】聊天对话,最新一条")
    public CommonResult<AppAiVirtualPartnerConversationRespVO> getVirtualPartnerConversationMy() {
        AiVirtualPartnerConversationDO conversation = virtualpartnerConversationService.getVirtualPartnerConversationById(getLoginUserId());
        if (conversation != null && ObjUtil.notEqual(conversation.getUserId(), getLoginUserId())) {
            conversation = null;
        }
        return success(BeanUtils.toBean(conversation, AppAiVirtualPartnerConversationRespVO.class));
    }

}

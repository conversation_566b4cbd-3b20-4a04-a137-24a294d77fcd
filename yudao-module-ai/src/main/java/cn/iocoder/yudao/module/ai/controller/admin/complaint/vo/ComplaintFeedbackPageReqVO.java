package cn.iocoder.yudao.module.ai.controller.admin.complaint.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 投诉/反馈分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ComplaintFeedbackPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "原因")
    private String content;

    @Schema(description = "反馈类目")
    private String feedbackCategory;

    @Schema(description = "投诉图片")
    private String images;

    @Schema(description = "反馈类型", example = "2")
    private String feedbackType;

    @Schema(description = "编号")
    private String no;

    @Schema(description = "类型", example = "2")
    private String type;

    @Schema(description = "资源类型 1-智能助手 2-虚拟陪伴 3-内容创作 以后新加去字典看feedback_resource_type", example = "1")
    private String resourceType;

    @Schema(description = "资源id", example = "14725")
    private String resourceId;

    @Schema(description = "用户编号", example = "27311")
    private Long userId;

}
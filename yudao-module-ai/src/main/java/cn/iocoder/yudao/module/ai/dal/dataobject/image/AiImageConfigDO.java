package cn.iocoder.yudao.module.ai.dal.dataobject.image;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * @Description: AI绘画基础配置
 * @Date: 2024/12/18 23:06
 * @Author: zhangq
 * @Version: 1.0
 */
@Table(name = "ai_image_config")
@Comment(value = "AI绘画基础配置")
@Entity
@TableName("ai_image_config")
@KeySequence("ai_image_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiImageConfigDO extends TenantBaseDO {

    /**
     * 编号，唯一自增
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 名称
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '名称'")
    private String name;

    /**
     *  描述
     */
    @Column(length = 255, columnDefinition = "varchar(255) COMMENT '描述'")
    private String description;

    /**
     * 绘画基础消耗（算力/张）
     */
    @Column(columnDefinition = "int COMMENT '绘画基础消耗（算力/张）'")
    private Integer drawBaseConsume;

    /**
     * 脸部修复消耗（算力/张）
     */
    @Column(columnDefinition = "int COMMENT '脸部修复消耗（算力/张）'")
    private Integer faceFixConsume;

    /**
     * 高清修复消耗（算力/张）
     */
    @Column(columnDefinition = "int COMMENT '高清修复消耗（算力/张）'")
    private Integer highFixConsume;
    /**
     * 高清修复-超清消耗（算力/张）
     */
    @Column(columnDefinition = "int COMMENT '高清修复-超清消耗（算力/张）'")
    private Integer highFixSuperConsume;

    /**
     * 默认绘画数量（1-5）
     */
    @Column(columnDefinition = "int COMMENT '默认绘画数量（1-5）'")
    private Integer defaultDrawNum;

    /**
     * 脸部修复默认状态
     */
    @Column(columnDefinition = "int COMMENT '脸部修复默认状态'")
    private Integer faceFixDefaultStatus;

    /**
     * 竖图宽度
     */
    @Column(columnDefinition = "int COMMENT '竖图宽度'")
    private Integer portraitWidth;

    /**
     * 竖图高度
     */
    @Column(columnDefinition = "int COMMENT '竖图高度'")
    private Integer portraitHeight;

    /**
     * 方图宽度
     */
    @Column(columnDefinition = "int COMMENT '方图宽度'")
    private Integer squareWidth;

    /**
     * 方图高度
     */
    @Column(columnDefinition = "int COMMENT '方图高度'")
    private Integer squareHeight;

    /**
     * 横图宽度
     */
    @Column(columnDefinition = "int COMMENT '横图宽度'")
    private Integer landscapeWidth;

    /**
     * 横图高度
     */
    @Column(columnDefinition = "int COMMENT '横图高度'")
    private Integer landscapeHeight;


    /**
     * 封面
     */
    @Column(columnDefinition = "varchar(255) COMMENT '封面'")
    private String coverImage;
}

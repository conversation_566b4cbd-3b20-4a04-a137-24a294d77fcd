package cn.iocoder.yudao.module.ai.controller.app.image.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/8/4 21:32
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class AppModelStyleRespVO {

    @Schema(description = "触站Model ID", example = "17062")
    private Integer modelStyleId;

    @Schema(description = "效果图")
    private String modelStyleImg;

    @Schema(description = "风格名称", example = "二次元")
    private String modelStyleName;

}

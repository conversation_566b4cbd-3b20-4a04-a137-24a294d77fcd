package cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.virtualpartner.VirtualPartnerRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CHAT_ROLE_DISABLE;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.VIRTUAL_PARTNER_ROLE_NOT_EXISTS;

/**
 * 虚拟陪伴角色 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VirtualPartnerRoleServiceImpl implements VirtualPartnerRoleService {

    @Resource
    private VirtualPartnerRoleMapper virtualPartnerRoleMapper;

    @Override
    public Long createVirtualPartnerRole(VirtualPartnerRoleSaveReqVO createReqVO) {
        // 插入
        AiVirtualPartnerRoleDO virtualPartnerRole = BeanUtils.toBean(createReqVO, AiVirtualPartnerRoleDO.class);
        virtualPartnerRoleMapper.insert(virtualPartnerRole);
        // 返回
        return virtualPartnerRole.getId();
    }

    @Override
    public void updateVirtualPartnerRole(VirtualPartnerRoleSaveReqVO updateReqVO) {
        // 校验存在
        validateVirtualPartnerRoleExists(updateReqVO.getId());
        // 更新
        AiVirtualPartnerRoleDO updateObj = BeanUtils.toBean(updateReqVO, AiVirtualPartnerRoleDO.class);
        virtualPartnerRoleMapper.updateById(updateObj);
    }

    @Override
    public void deleteVirtualPartnerRole(Long id) {
        // 校验存在
        validateVirtualPartnerRoleExists(id);
        // 删除
        virtualPartnerRoleMapper.deleteById(id);
    }

    private AiVirtualPartnerRoleDO validateVirtualPartnerRoleExists(Long id) {
        AiVirtualPartnerRoleDO virtualPartnerRole = virtualPartnerRoleMapper.selectById(id);
        if (virtualPartnerRole == null) {
            throw exception(VIRTUAL_PARTNER_ROLE_NOT_EXISTS);
        }
        return virtualPartnerRole;
    }

    @Override
    public AiVirtualPartnerRoleDO getVirtualPartnerRole(Long id) {
        return virtualPartnerRoleMapper.selectById(id);
    }

    @Override
    public PageResult<AiVirtualPartnerRoleDO> getVirtualPartnerRolePage(VirtualPartnerRolePageReqVO pageReqVO) {
        return virtualPartnerRoleMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AiVirtualPartnerRoleDO> getAppVirtualPartnerRolePage(AppVirtualPartnerRolePageReqVO pageReqVO) {
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        return virtualPartnerRoleMapper.selectAppPage(pageReqVO);
    }

    @Override
    public AiVirtualPartnerRoleDO validateVirtualPartnerRole(Long id) {
        AiVirtualPartnerRoleDO virtualPartnerRole = validateVirtualPartnerRoleExists(id);
        if (CommonStatusEnum.isDisable(virtualPartnerRole.getStatus())) {
            throw exception(CHAT_ROLE_DISABLE, virtualPartnerRole.getName());
        }
        return virtualPartnerRole;
    }

}
package cn.iocoder.yudao.module.ai.service.contentwrite.message;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo.ContentWriteMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo.ContentWriteMessageSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo.AppContentWriteMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo.AppContentWriteMessagePraiseReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo.AppContentWriteMessageSendReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo.AppContentWriteMessageSendRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteMessageDO;
import jakarta.validation.Valid;
import reactor.core.publisher.Flux;

/**
 * AI 内容创作 消息 Service 接口
 *
 * <AUTHOR>
 */
public interface ContentWriteMessageService {

    /**
     * 创建AI 内容创作 消息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContentWriteMessage(@Valid ContentWriteMessageSaveReqVO createReqVO);

    /**
     * 更新AI 内容创作 消息
     *
     * @param updateReqVO 更新信息
     */
    void updateContentWriteMessage(@Valid ContentWriteMessageSaveReqVO updateReqVO);

    /**
     * 删除AI 内容创作 消息
     *
     * @param id 编号
     */
    void deleteContentWriteMessage(Long id);

    /**
     * 获得AI 内容创作 消息
     *
     * @param id 编号
     * @return AI 内容创作 消息
     */
    AiContentWriteMessageDO getContentWriteMessage(Long id);

    /**
     * 获得AI 内容创作 消息分页
     *
     * @param pageReqVO 分页查询
     * @return AI 内容创作 消息分页
     */
    PageResult<AiContentWriteMessageDO> getContentWriteMessagePage(ContentWriteMessagePageReqVO pageReqVO);

    /**
     * 发送消息
     * @param sendReqVO
     * @param userId
     * @return
     */
    AppContentWriteMessageSendRespVO sendMessage(@Valid AppContentWriteMessageSendReqVO sendReqVO, Long userId);

    /**
     * 发送消息
     * @param sendReqVO
     * @param userId
     * @return
     */
    Flux<CommonResult<AppContentWriteMessageSendRespVO>> senddContentWriteMessageStream(@Valid AppContentWriteMessageSendReqVO sendReqVO, Long userId);

    /**
     * 获得指定对话的消息列表-分页
     * @param pageReqVO
     * @return
     */
    PageResult<AiContentWriteMessageDO> getContentWriteMessageListByConversationId(AppContentWriteMessagePageReqVO pageReqVO);

    /**
     * 点赞/点踩
     * @param reqVO
     */
    void praiseContentWriteMessage(@Valid AppContentWriteMessagePraiseReqVO reqVO);
}
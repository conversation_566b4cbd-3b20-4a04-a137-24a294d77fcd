package cn.iocoder.yudao.module.ai.service.invite;

/**
 * @Description: TODO
 * @Date: 2025/1/20 23:21
 * @Author: zhangq
 * @Version: 1.0
 */
public interface AiInviteService {

    /**
     * 创建邀请码
     * @param userId
     */
    void creatInviteByRegister(Long userId);

    /**
     * 获取邀请码
     * @param userId
     * @return
     */
    String getInviteCode(Long userId);

    // 用户填写邀请码并领取奖励
    void fillInviteCode(Long userId, String inviteCode);
}

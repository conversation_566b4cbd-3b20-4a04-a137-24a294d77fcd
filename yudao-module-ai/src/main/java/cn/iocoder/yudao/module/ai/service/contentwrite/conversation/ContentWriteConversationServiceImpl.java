package cn.iocoder.yudao.module.ai.service.contentwrite.conversation;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteRoleDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.conversation.ContentWriteConversationMapper;
import cn.iocoder.yudao.module.ai.service.contentwrite.role.ContentWriteRoleService;
import cn.iocoder.yudao.module.ai.service.model.AiYyModelService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;

/**
 * AI 内容写作 对话 DO Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContentWriteConversationServiceImpl implements ContentWriteConversationService {

    @Resource
    private ContentWriteConversationMapper contentWriteConversationMapper;

    @Resource
    private AiYyModelService modelService;
    @Resource
    private ContentWriteRoleService contentWriteRoleService;

    @Override
    public Long createContentWriteConversation(ContentWriteConversationSaveReqVO createReqVO) {
        // 插入
        AiContentWriteConversationDO contentWriteConversation = BeanUtils.toBean(createReqVO, AiContentWriteConversationDO.class);
        contentWriteConversationMapper.insert(contentWriteConversation);
        // 返回
        return contentWriteConversation.getId();
    }

    @Override
    public void updateContentWriteConversation(ContentWriteConversationSaveReqVO updateReqVO) {
        // 校验存在
        validateContentWriteConversationExists(updateReqVO.getId());
        // 更新
        AiContentWriteConversationDO updateObj = BeanUtils.toBean(updateReqVO, AiContentWriteConversationDO.class);
        contentWriteConversationMapper.updateById(updateObj);
    }

    @Override
    public void deleteContentWriteConversation(Long id) {
        // 校验存在
        validateContentWriteConversationExists(id);
        // 删除
        contentWriteConversationMapper.deleteById(id);
    }

    @Override
    public AiContentWriteConversationDO validateContentWriteConversationExists(Long id) {
        AiContentWriteConversationDO conversation = contentWriteConversationMapper.selectById(id);
        if (conversation == null) {
            throw exception(CONTENT_WRITE_CONVERSATION_NOT_EXISTS);
        }
        return conversation;
    }

    @Override
    public AiContentWriteConversationDO getContentWriteConversation(Long id) {
        return contentWriteConversationMapper.selectById(id);
    }

    @Override
    public PageResult<AiContentWriteConversationDO> getContentWriteConversationPage(ContentWriteConversationPageReqVO pageReqVO) {
        return contentWriteConversationMapper.selectPage(pageReqVO);
    }

    @Override
    public Long createContentWriteConversationMy(AppContentWriteConversationCreateMyReqVO createReqVO, Long userId) {
        // 1.1 获得 AiContentWriteRoleDO 聊天角色
        if (createReqVO.getRoleId() == null) {
            throw exception(CONTENT_WRITE_CONVERSATION_NOT_EXISTS_BY_ID);
        }
        AiContentWriteRoleDO role = contentWriteRoleService.validateContentWriteRole(createReqVO.getRoleId());
        // 1.2 获得 AiChatModelDO 聊天模型
        AiYyModelDO model = role != null && role.getModelId() != null ? modelService.validateModel(role.getModelId())
                : null;
        Assert.notNull(model, MODEL_NOT_EXISTS.getMsg());

        // 2. 创建 AiVirtualpartnerConversationDO 聊天对话
        AiContentWriteConversationDO conversation = new AiContentWriteConversationDO().setUserId(userId).setPinned(false)
                .setModelId(model.getId()).setModel(model.getModel()).setTopP(role.getTopP())
                .setSystemMessage(role.getSystemMessage())
                .setTemperature(role.getTemperature()).setMaxTokens(role.getMaxTokens()).setMaxContexts(role.getMaxContexts());
        if (role != null) {
            conversation.setTitle(null != role.getName()? role.getName(): AiAssistantConversationDO.TITLE_DEFAULT).setRoleId(role.getId()).setSystemMessage(role.getSystemMessage());
        } else {
            conversation.setTitle(AiAssistantConversationDO.TITLE_DEFAULT);
        }
        contentWriteConversationMapper.insert(conversation);
        return conversation.getId();
    }

    @Override
    public PageResult<AiContentWriteConversationDO> getContentWriteConversationAppListByUserId(Long userId, AppContentWriteConversationPageReqVO pageReqVO) {
        return contentWriteConversationMapper.selectPage(userId, pageReqVO);
    }

    @Override
    public AiContentWriteConversationDO getContentWriteConversationById(Long userId) {
        return contentWriteConversationMapper.selectByUserIdDesc(userId);
    }

}
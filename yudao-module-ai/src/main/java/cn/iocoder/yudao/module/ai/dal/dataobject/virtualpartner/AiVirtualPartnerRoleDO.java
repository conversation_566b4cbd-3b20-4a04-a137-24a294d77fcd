package cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import java.util.List;

/**
 * AI 虚拟陪伴角色 DO
 */
@Table(name = "ai_virtual_partner_role")
@Comment(value = "虚拟陪伴角色")
@Entity
@TableName(value = "ai_virtual_partner_role", autoResultMap = true)
@KeySequence("ai_virtual_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiVirtualPartnerRoleDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;
    /**
     * 角色名称/智能体名称
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '角色名称'")
    private String name;

    /**
     * 音色id
     *  关联 {@link AiAudioConfigDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint COMMENT '音色id'")
    private Long audioId;

    /**
     * 封面描述
     */
    @Column(columnDefinition = "varchar(255) COMMENT '封面描述'")
    private String coverDesc;

    /**
     * 封面
     */
    @Column(columnDefinition = "varchar(255) COMMENT '封面'")
    private String coverUrl;

    /**
     * 聊天背景图
     */
    @Column(columnDefinition = "varchar(255) COMMENT '聊天背景图'")
    private String backgroundUrl;

    /**
     * 开场白
     */
    @Column(columnDefinition = "varchar(255) COMMENT '开场白'")
    private String cover;

    /**
     * 开场白语音文件
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '开场白语音文件'")
    private String coverVoiceFile;

    /**
     * 故事简介
     */
    @Column(length = 2000, columnDefinition = "varchar(2000) COMMENT '故事简介'")
    private String storyDesc;

    /**
     * 预设示例
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "json COMMENT '预设示例'")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> presetExample;

    /**
     * 输入框预置词
     */
    @Column(columnDefinition = "varchar(255) COMMENT '输入框预置词'")
    private String inputPresetWord;

    /**
     * 角色设定
     */
    @Column(length = 4000, columnDefinition = "varchar(4000) COMMENT '角色设定'")
    private String systemMessage;

    /**
     * 模型编号
     *
     * 关联 {@link AiYyModelDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint COMMENT '模型编号'")
    private Long modelId;

    /**
     * 模型平台
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '模型平台'")
    private String platform;

    /**
     * 模型版本
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '模型版本'")
    private String model;

    /**
     * 排序值
     */
    @Column(columnDefinition = "int COMMENT '排序值'")
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '状态'")
    private Integer status;

    /**
     * 温度参数
     * 较高的值将使输出更加随机，而较低的值将使输出更加集中和确定。可选：abab6.5、abab6.5s 默认取值0.1；
     * abab5.5s、abab5.5 默认取值0.9。
     * 低（0.01~0.2）：适合答案较明确的场景（如：知识问答、总结说明、情感分析、文本分类、大纲生成、作文批改）、信息提取；
     * ⾼（0.7〜1）：适合答案较开放发散的场景 （如：营销文案生成、人设对话）。
     *
     * 用于调整生成回复的随机性和多样性程度：较低的温度值会使输出更收敛于高频词汇，较高的则增加多样性
     */
    @Column(columnDefinition = "double(10,2) COMMENT '温度参数'")
    private Double temperature;

    /**
     * 采样方法
     * 数值越小结果确定性越强；数值越大，结果越随机。可选:
     * 各模型默认取值0.95
     */
    @Column(name = "top_p", columnDefinition = "double(10,2) COMMENT '采样方法'")
    private Double topP;

    /**
     * 点击量
     */
    @Column(columnDefinition = "bigint COMMENT '点击量'")
    private Long clickCount;

    /**
     * 单条回复的最大 Token 数量
     */
    @Column(columnDefinition = "int COMMENT '单条回复的最大 Token 数量'")
    private Integer maxTokens;
    /**
     * 上下文的最大 Message 数量
     */
    @Column(columnDefinition = "int COMMENT '上下文的最大 Message 数量'")
    private Integer maxContexts;
}

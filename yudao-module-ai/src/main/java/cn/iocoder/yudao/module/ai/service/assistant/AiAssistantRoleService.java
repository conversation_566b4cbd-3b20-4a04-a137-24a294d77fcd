package cn.iocoder.yudao.module.ai.service.assistant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * AI 聊天角色 Service 接口
 *
 * <AUTHOR>
 */
public interface AiAssistantRoleService {

    /**
     * 创建聊天角色
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveOrUpdateAssistantRole(@Valid AiAssistantRoleSaveReqVO createReqVO);

    /**
     * 更新聊天角色
     *
     * @param updateReqVO 更新信息
     */
    void updateAssistantRole(@Valid AiAssistantRoleSaveReqVO updateReqVO);

    /**
     * 删除聊天角色
     *
     * @param id 编号
     */
    void deleteAssistantRole(Long id);


    /**
     * 获得聊天角色
     *
     * @return AI 聊天角色
     */
    AiAssistantRoleDO getAssistantRole();

    /**
     * 获得聊天角色列表
     *
     * @param ids 编号数组
     * @return 聊天角色列表
     */
    List<AiAssistantRoleDO> getAssistantRoleList(Collection<Long> ids);

    default Map<Long, AiAssistantRoleDO> getAssistantRoleMap(Collection<Long> ids) {
        return convertMap(getAssistantRoleList(ids), AiAssistantRoleDO::getId);
    }

    /**
     * 校验聊天角色是否合法
     *
     * @param id 角色编号
     */
    AiAssistantRoleDO validateAssistantRole(Long id);

    /**
     * 获得聊天角色分页
     *
     * @param pageReqVO 分页查询
     * @return 聊天角色分页
     */
    PageResult<AiAssistantRoleDO> getAssistantRolePage(AiAssistantRolePageReqVO pageReqVO);

    /**
     * 根据名字获得聊天角色
     *
     * @param name 名字
     * @return 聊天角色列表
     */
    List<AiAssistantRoleDO> getAssistantRoleListByName(String name);

    /**
     * 获得默认的聊天模型
     *
     * 如果获取不到，则抛出 {@link cn.iocoder.yudao.framework.common.exception.ServiceException} 业务异常
     *
     * @return 聊天模型
     */
    AiAssistantRoleDO getRequiredDefaultAssistantModel();
}
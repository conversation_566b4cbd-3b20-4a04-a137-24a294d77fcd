package cn.iocoder.yudao.module.ai.service.image.config;

import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppAiImageLabRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.DrawImageConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.promptwords.AppPromptWordsRespVO;

import java.util.List;

public interface DrawImageConfigService {

    /**
     * 获取绘画配置信息
     * @return
     */
    DrawImageConfigRespVO getDrawImageConfig();

    /**
     * 获取灵感列表
     * @return
     */
    AppPromptWordsRespVO getInspiration(Long userId, Integer type);

    List<AppPromptWordsRespVO> getQuality(Long loginUserId,Integer type);

    /**
     * 获取AI图片配置实验室
     * @return
     */
    List<AppAiImageLabRespVO> getAiImageLab();
}

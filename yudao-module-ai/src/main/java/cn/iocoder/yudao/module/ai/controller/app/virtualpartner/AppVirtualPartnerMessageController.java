package cn.iocoder.yudao.module.ai.controller.app.virtualpartner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.*;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerMessageDO;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.AiVirtualPartnerConversationService;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.AiVirtualPartnerMessageService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 虚拟陪伴消息")
@RestController
@RequestMapping("/ai/virtualpartner/message")
@Slf4j
public class AppVirtualPartnerMessageController {

    @Resource
    private AiVirtualPartnerMessageService virtualPartnerMessageService;
    @Resource
    private AiVirtualPartnerConversationService virtualPartnerConversationService;

    @Operation(summary = "发送消息（段式）", description = "一次性返回，响应较慢")
    @PostMapping("/send")
    public CommonResult<AppAiVirtualPartnerMessageSendRespVO> appSendVirtualPartnerMessage(@Valid @RequestBody AppAiVirtualPartnerMessageSendReqVO sendReqVO) {
        return success(virtualPartnerMessageService.sendMessage(sendReqVO, getLoginUserId()));
    }

    @Operation(summary = "发送消息（流式）", description = "流式返回，响应较快")
    @PostMapping(value = "/send-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PermitAll // 解决 SSE 最终响应的时候，会被 Access Denied 拦截的问题
    public Flux<CommonResult<AppAiVirtualPartnerMessageSendRespVO>> appSendVirtualPartnerMessageStream(@Valid @RequestBody AppAiVirtualPartnerMessageSendReqVO sendReqVO) {
        return virtualPartnerMessageService.sendVirtualPartnerMessageStream(sendReqVO, getLoginUserId());
    }

    @Operation(summary = "获得指定对话的消息列表-分页")
    @GetMapping("/list-by-conversation-id")
    @Parameter(name = "conversationId", required = true, description = "对话编号", example = "1024")
    public CommonResult<PageResult<AppAiVirtualPartnerMessageRespVO>> getVirtualPartnerMessageListByConversationId(AppAiVirtualPartnerMessagePageReqVO pageReqVO) {
        AiVirtualPartnerConversationDO conversation = virtualPartnerConversationService.getVirtualPartnerConversation(pageReqVO.getConversationId());
        if (conversation == null || ObjUtil.notEqual(conversation.getUserId(), getLoginUserId())) {
            return success(PageResult.empty());
        }
        PageResult<AiVirtualPartnerMessageDO> messagePage = virtualPartnerMessageService.getVirtualPartnerMessagePageByConversationId(pageReqVO);
        if (CollUtil.isEmpty(messagePage.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(messagePage, AppAiVirtualPartnerMessageRespVO.class));
    }

    @Hidden
    @Operation(summary = "删除消息")
    @DeleteMapping("/delete")
    @Parameter(name = "id", required = true, description = "消息编号", example = "1024")
    public CommonResult<Boolean> deleteChatMessage(@RequestParam("id") Long id) {
        virtualPartnerMessageService.deleteVirtualPartnerMessage(id, getLoginUserId());
        return success(true);
    }

    @Hidden
    @Operation(summary = "删除指定对话的消息")
    @DeleteMapping("/delete-by-conversation-id")
    @Parameter(name = "conversationId", required = true, description = "对话编号", example = "1024")
    public CommonResult<Boolean> deleteVirtualPartnerMessageByConversationId(@RequestParam("conversationId") Long conversationId) {
        virtualPartnerMessageService.deleteVirtualPartnerMessageByConversationId(conversationId, getLoginUserId());
        return success(true);
    }

     /**
     * 消息点赞/点踩
     */

    @Operation(summary = "点赞/点踩")
    @PostMapping("/praise")
    public CommonResult<Boolean> praise(@Valid @RequestBody AppVirtualPartnerMessagePraiseReqVO reqVO) {
        virtualPartnerMessageService.praiseVirtualPartnerMessage(reqVO);
        return success(true);
    }
}

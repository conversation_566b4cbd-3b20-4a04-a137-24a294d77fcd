package cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.virtualpartner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessagePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerMessageDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * AI 虚拟陪伴 消息
 *
 */
@Mapper
public interface AiVirtualPartnerMessageMapper extends BaseMapperX<AiVirtualPartnerMessageDO> {

    default List<AiVirtualPartnerMessageDO> selectListByConversationId(Long conversationId) {
        return selectList(new LambdaQueryWrapperX<AiVirtualPartnerMessageDO>()
                .eq(AiVirtualPartnerMessageDO::getConversationId, conversationId)
                .orderByAsc(AiVirtualPartnerMessageDO::getId));
    }

    default Map<Long, Integer> selectCountMapByConversationId(Collection<Long> conversationIds) {
        // SQL count 查询
        List<Map<String, Object>> result = selectMaps(new QueryWrapper<AiVirtualPartnerMessageDO>()
                .select("COUNT(id) AS count, conversation_id AS conversationId")
                .in("conversation_id", conversationIds)
                .groupBy("conversation_id"));
        if (CollUtil.isEmpty(result)) {
            return Collections.emptyMap();
        }
        // 转换数据
        return CollectionUtils.convertMap(result,
                record -> MapUtil.getLong(record, "conversationId"),
                record -> MapUtil.getInt(record, "count" ));
    }

    default PageResult<AiVirtualPartnerMessageDO> selectPage(AppAiVirtualPartnerMessagePageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiVirtualPartnerMessageDO>()
                .eqIfPresent(AiVirtualPartnerMessageDO::getConversationId, pageReqVO.getConversationId())
                .eqIfPresent(AiVirtualPartnerMessageDO::getUserId, pageReqVO.getUserId())
                .likeIfPresent(AiVirtualPartnerMessageDO::getContent, pageReqVO.getContent())
                .betweenIfPresent(AiVirtualPartnerMessageDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(AiVirtualPartnerMessageDO::getId));
    }

    default PageResult<AiVirtualPartnerMessageDO> getVirtualPartnerMessagePageByConversationId(AppAiVirtualPartnerMessagePageReqVO pageReqVO){
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiVirtualPartnerMessageDO>()
                .eq(AiVirtualPartnerMessageDO::getConversationId, pageReqVO.getConversationId())
                .eqIfPresent(AiVirtualPartnerMessageDO::getUserId, pageReqVO.getUserId())
                .orderByDesc(AiVirtualPartnerMessageDO::getId));
    }

    default AiVirtualPartnerMessageDO selectById(Long id, Long userId) {
        LambdaQueryWrapperX<AiVirtualPartnerMessageDO> queryWrapper = new LambdaQueryWrapperX<AiVirtualPartnerMessageDO>()
                .eq(AiVirtualPartnerMessageDO::getId, id)
                .eq(AiVirtualPartnerMessageDO::getUserId, userId);
        AiVirtualPartnerMessageDO message = selectOne(queryWrapper);
        return message;
    }
}

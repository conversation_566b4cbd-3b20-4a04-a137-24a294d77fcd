package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 内容写作 对话 DO Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContentWriteConversationRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4717")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "上下文的最大 Message 数量")
    @ExcelProperty("上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "单条回复的最大 Token 数量")
    @ExcelProperty("单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模型标志")
    private String model;

    @Schema(description = "模型编号", example = "31192")
    @ExcelProperty("模型编号")
    private Long modelId;

    @Schema(description = "是否置顶", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否置顶")
    private Boolean pinned;

    @Schema(description = "置顶时间")
    @ExcelProperty("置顶时间")
    private LocalDateTime pinnedTime;

    @Schema(description = "角色编号", example = "5227")
    @ExcelProperty("角色编号")
    private Long roleId;

    @Schema(description = "角色设定")
    @ExcelProperty("角色设定")
    private String systemMessage;

    @Schema(description = "温度参数")
    @ExcelProperty("温度参数")
    private Double temperature;

    @Schema(description = "对话标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("对话标题")
    private String title;

    @Schema(description = "采样方法")
    @ExcelProperty("采样方法")
    private Double topP;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16872")
    @ExcelProperty("用户编号")
    private Long userId;

}
package cn.iocoder.yudao.module.ai.enums.image;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ResolutionPresetEnum {
    //默认
    DEFAULT("default", "默认", ""),
    NORMAL("normal", "高清", "一般清晰度（快速出图）"),
    HIGH("high", "超清", "高清(一般耗时)"),
    ULTRA("ultra", "", "超清（耗时较长）"),
    EXTREME("extreme", "", "极清（耗时最长）"),
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 名称
     */
    private final String name;

    /**
     * 对应触站AI的分辨率
     */
    private final String resolutionPreset;
}

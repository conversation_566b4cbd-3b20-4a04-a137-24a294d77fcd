package cn.iocoder.yudao.module.ai.util;

import java.security.SecureRandom;

/**
 * @Description: 邀请码工具类
 * @Date: 2025/1/20 22:54
 * @Author: zhangq
 * @Version: 1.0
 */
public class InvitationUtil {

    // 定义字符集：26个大写字母、26个小写字母、数字0-9
    private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final String ALL_CHARACTERS = UPPERCASE + LOWERCASE + DIGITS;

    private static final int INVITE_CODE_LENGTH = 8; // 邀请码的长度

    // 生成邀请码
    public static String generateInviteCode() {
        SecureRandom random = new SecureRandom();  // 使用安全随机数生成器
        StringBuilder inviteCode = new StringBuilder(INVITE_CODE_LENGTH);

        // 确保每个类型的字符至少出现一次
        inviteCode.append(UPPERCASE.charAt(random.nextInt(UPPERCASE.length())));  // 加入一个大写字母
        inviteCode.append(LOWERCASE.charAt(random.nextInt(LOWERCASE.length())));  // 加入一个小写字母
        inviteCode.append(DIGITS.charAt(random.nextInt(DIGITS.length())));        // 加入一个数字

        // 剩余部分从所有字符集中随机选择
        for (int i = 3; i < INVITE_CODE_LENGTH; i++) {
            int index = random.nextInt(ALL_CHARACTERS.length());  // 从所有字符集中选择
            inviteCode.append(ALL_CHARACTERS.charAt(index));
        }

        // 打乱生成的邀请码顺序
        String finalInviteCode = shuffleString(inviteCode.toString());

        return finalInviteCode;  // 返回生成的邀请码
    }

    // 打乱字符串顺序
    private static String shuffleString(String str) {
        char[] array = str.toCharArray();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < array.length; i++) {
            int j = random.nextInt(array.length);
            char temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
        return new String(array);
    }

    public static void main(String[] args) {
        InvitationUtil service = new InvitationUtil();
        String inviteCode = service.generateInviteCode();
        System.out.println("生成的邀请码: " + inviteCode);
    }
}

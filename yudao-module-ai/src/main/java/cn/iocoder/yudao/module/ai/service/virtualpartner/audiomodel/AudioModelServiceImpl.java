package cn.iocoder.yudao.module.ai.service.virtualpartner.audiomodel;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioModelDO;
import cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.audiomodel.AudioModelMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.AUDIO_MODEL_NOT_EXISTS;

/**
 * 音色模型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AudioModelServiceImpl implements AudioModelService {

    @Resource
    private AudioModelMapper audioModelMapper;

    @Override
    public Long createAudioModel(AudioModelSaveReqVO createReqVO) {
        // 插入
        AiAudioModelDO audioModel = BeanUtils.toBean(createReqVO, AiAudioModelDO.class);
        audioModelMapper.insert(audioModel);
        // 返回
        return audioModel.getId();
    }

    @Override
    public void updateAudioModel(AudioModelSaveReqVO updateReqVO) {
        // 校验存在
        validateAudioModelExists(updateReqVO.getId());
        // 更新
        AiAudioModelDO updateObj = BeanUtils.toBean(updateReqVO, AiAudioModelDO.class);
        audioModelMapper.updateById(updateObj);
    }

    @Override
    public void deleteAudioModel(Long id) {
        // 校验存在
        validateAudioModelExists(id);
        // 删除
        audioModelMapper.deleteById(id);
    }

    private void validateAudioModelExists(Long id) {
        if (audioModelMapper.selectById(id) == null) {
            throw exception(AUDIO_MODEL_NOT_EXISTS);
        }
    }

    @Override
    public AiAudioModelDO getAudioModel(Long id) {
        return audioModelMapper.selectById(id);
    }

    @Override
    public PageResult<AiAudioModelDO> getAudioModelPage(AudioModelPageReqVO pageReqVO) {
        return audioModelMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AiAudioModelDO> getAudioModelListByStatus(Integer status) {
        return audioModelMapper.selectList(status);
    }

}
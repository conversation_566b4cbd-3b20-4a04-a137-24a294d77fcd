package cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 风格模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AiModelStyleRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "8361")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "风格描述")
    @ExcelProperty("风格描述")
    private String modelStyleDesc;

    @Schema(description = "触站Model ID", example = "17062")
    @ExcelProperty("触站Model ID")
    private Integer modelStyleId;

    @Schema(description = "效果图")
    @ExcelProperty("效果图")
    private String modelStyleImg;

    @Schema(description = "风格名称", example = "赵六")
    @ExcelProperty("风格名称")
    private String modelStyleName;

    @Schema(description = "使用次数", example = "29343")
    @ExcelProperty("使用次数")
    private Integer modelStyleUseCount;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}
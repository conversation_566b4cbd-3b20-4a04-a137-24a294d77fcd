package cn.iocoder.yudao.module.ai.controller.app.contentwrite.category;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.category.vo.AppContentWriteCategoryRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteCategoryDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.category.ContentWriteCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户APP - AI 内容创作分类")
@RestController
@RequestMapping("/ai/contentwrite/category")
@Validated
public class AppContentWriteCategoryController {

    @Resource
    private ContentWriteCategoryService contentWriteCategoryService;

    @GetMapping("/simple-list")
    @Operation(summary = "获得内容创作分类列表-app")
    public CommonResult<List<AppContentWriteCategoryRespVO>> getContentWriteCategorySimpleList() {
        List<AiContentWriteCategoryDO> list = contentWriteCategoryService.getAppContentWriteCategorySimpleList();
        return success(BeanUtils.toBean(list, AppContentWriteCategoryRespVO.class));
    }
}
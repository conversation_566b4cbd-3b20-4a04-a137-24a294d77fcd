package cn.iocoder.yudao.module.ai.controller.app.virtualpartner;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRoleRespPageVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRoleRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.VirtualPartnerRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户APP - 虚拟陪伴角色")
@RestController
@RequestMapping("/ai/virtual-partner-role")
@Validated
public class AppVirtualPartnerRoleController {

    @Resource
    private VirtualPartnerRoleService virtualPartnerRoleService;

    @GetMapping("/get")
    @Operation(summary = "获得虚拟陪伴角色")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    public CommonResult<AppVirtualPartnerRoleRespVO> getAppVirtualPartnerRole(@RequestParam("id") Long id) {
        AiVirtualPartnerRoleDO virtualPartnerRole = virtualPartnerRoleService.getVirtualPartnerRole(id);
        return success(BeanUtils.toBean(virtualPartnerRole, AppVirtualPartnerRoleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得虚拟陪伴角色分页")
    public CommonResult<PageResult<AppVirtualPartnerRoleRespPageVO>> getAppVirtualPartnerRolePage(@Valid AppVirtualPartnerRolePageReqVO pageReqVO) {
        PageResult<AiVirtualPartnerRoleDO> pageResult = virtualPartnerRoleService.getAppVirtualPartnerRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppVirtualPartnerRoleRespPageVO.class));
    }

}
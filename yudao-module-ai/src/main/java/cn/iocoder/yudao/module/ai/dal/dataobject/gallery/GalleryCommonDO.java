package cn.iocoder.yudao.module.ai.dal.dataobject.gallery;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.glassfish.jaxb.core.v2.TODO;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import java.util.List;

/**
 * AI 公开画廊 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_gallery_common")
@Comment(value = "AI 公开画廊")
@Entity
@TableName(value = "ai_gallery_common", autoResultMap = true)
@KeySequence("ai_gallery_common_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GalleryCommonDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 封面
     */
    @Column(length = 500, columnDefinition = "varchar(500) COMMENT '封面'")
    private String coverUrl;

    /**
     * 提示词
     */
    @Column(length = 500, columnDefinition = "varchar(500) COMMENT '提示词'")
    private String prompt;

    /**
     * 同款图
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "json COMMENT '同款图'")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> imgUrls;

    /**
     * 排序
     */
    @Column(columnDefinition = "int COMMENT '排序'")
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    @Column(columnDefinition = "int COMMENT '状态'")
    private Integer status;

}
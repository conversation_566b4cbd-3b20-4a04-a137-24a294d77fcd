package cn.iocoder.yudao.module.ai.service.complaint;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.complaint.vo.ComplaintFeedbackSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.complaint.ComplaintFeedbackDO;
import jakarta.validation.Valid;

/**
 * 投诉/反馈 Service 接口
 *
 * <AUTHOR>
 */
public interface ComplaintFeedbackService {

    /**
     * 创建投诉/反馈
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createComplaintFeedback(@Valid ComplaintFeedbackSaveReqVO createReqVO);

    /**
     * 更新投诉/反馈
     *
     * @param updateReqVO 更新信息
     */
    void updateComplaintFeedback(@Valid ComplaintFeedbackSaveReqVO updateReqVO);

    /**
     * 删除投诉/反馈
     *
     * @param id 编号
     */
    void deleteComplaintFeedback(Long id);

    /**
     * 获得投诉/反馈
     *
     * @param id 编号
     * @return 投诉/反馈
     */
    ComplaintFeedbackDO getComplaintFeedback(Long id);

    /**
     * 获得投诉/反馈分页
     *
     * @param pageReqVO 分页查询
     * @return 投诉/反馈分页
     */
    PageResult<ComplaintFeedbackDO> getComplaintFeedbackPage(ComplaintFeedbackPageReqVO pageReqVO);

    /**
     * 创建投诉/反馈
     * @param createReqVO
     * @return
     */
    String createAppComplaintFeedback(Long userId,@Valid ComplaintFeedbackSaveReqVO createReqVO);
}
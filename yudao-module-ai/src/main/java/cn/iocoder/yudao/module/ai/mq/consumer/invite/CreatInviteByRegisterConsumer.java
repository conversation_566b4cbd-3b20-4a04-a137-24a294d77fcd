package cn.iocoder.yudao.module.ai.mq.consumer.invite;

import cn.iocoder.yudao.module.ai.service.invite.AiInviteService;
import cn.iocoder.yudao.module.member.api.message.user.MemberUserCreateMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @Description: 注册时创建邀请码
 * @Date: 2025/2/26 22:33
 * @Author: zhangq
 * @Version: 1.0
 */
@Component
@Slf4j
public class CreatInviteByRegisterConsumer {

    @Resource
    private AiInviteService inviteService;

    @EventListener
    @Async // Spring Event 默认在 Member Producer 发送的线程，通过 @Async 实现异步
    public void onMessage(MemberUserCreateMessage message) {
        log.info("[创建邀请码-onMessage][消息内容({})]", message);
        inviteService.creatInviteByRegister(message.getUserId());
    }
}

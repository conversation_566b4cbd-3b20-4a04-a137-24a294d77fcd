package cn.iocoder.yudao.module.ai.convert.banner;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerCreateReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerUpdateReqVO;
import cn.iocoder.yudao.module.ai.controller.app.banner.vo.AppBannerRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.banner.AiBannerDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BannerConvert {

    BannerConvert INSTANCE = Mappers.getMapper(BannerConvert.class);

    List<BannerRespVO> convertList(List<AiBannerDO> list);

    PageResult<BannerRespVO> convertPage(PageResult<AiBannerDO> pageResult);

    BannerRespVO convert(AiBannerDO banner);

    AiBannerDO convert(BannerCreateReqVO createReqVO);

    AiBannerDO convert(BannerUpdateReqVO updateReqVO);

    List<AppBannerRespVO> convertList01(List<AiBannerDO> bannerList);

}

package cn.iocoder.yudao.module.ai.service.assistant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AiAssistantConversationUpdateMyReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;

import java.util.List;

/**
 * AI 聊天对话 Service 接口
 *
 * <AUTHOR>
 */
public interface AiAssistantConversationService {

    /**
     * 创建【我的】聊天对话
     *
     * @param createReqVO 创建信息
     * @param userId 用户编号
     * @return 编号
     */
    Long createAssistantConversationMy(AiAssistantConversationCreateMyReqVO createReqVO, Long userId);

    /**
     * 更新【我的】聊天对话
     *
     * @param updateReqVO 更新信息
     * @param userId 用户编号
     */
    void updateChatConversationMy(AiAssistantConversationUpdateMyReqVO updateReqVO, Long userId);

    /**
     * 获得【我的】聊天对话列表
     *
     * @param userId 用户编号
     * @return 聊天对话列表
     */
    List<AiAssistantConversationDO> getAssistantConversationListByUserId(Long userId);

    /**
     * 获得聊天对话
     *
     * @param id 编号
     * @return 聊天对话
     */
    AiAssistantConversationDO getAssistantConversation(Long id);

    /**
     * 删除【我的】聊天对话
     *
     * @param id 编号
     * @param userId 用户编号
     */
    void deleteAssistantConversationMy(Long id, Long userId);

    /**
     * 【管理员】删除聊天对话
     *
     * @param id 编号
     */
    void deleteAssistantConversationByAdmin(Long id);

    /**
     * 校验聊天对话是否存在
     *
     * @param id 编号
     * @return 聊天对话
     */
    AiAssistantConversationDO validateAssistantConversationExists(Long id);

    /**
     * 删除【我的】 + 非置顶的聊天对话
     *
     * @param userId 用户编号
     */
    void deleteAssistantConversationMyByUnpinned(Long userId);

    /**
     * 获得聊天对话的分页列表
     *
     * @param pageReqVO 分页查询
     * @return 聊天对话的分页列表
     */
    PageResult<AiAssistantConversationDO> getAssistantConversationPage(AiAssistantConversationPageReqVO pageReqVO);

    /**
     * 获得【我的】聊天对话列表 -APP
     *
     * @param userId 用户编号
     * @return 聊天对话列表
     */
    List<AiAssistantConversationDO> getAssistantConversationAppListByUserId(Long userId);

    /**
     * 获得【我的】聊天对话,最新一条
     * @param userId
     * @return
     */
    AiAssistantConversationDO getAssistantConversationById(Long userId);
}

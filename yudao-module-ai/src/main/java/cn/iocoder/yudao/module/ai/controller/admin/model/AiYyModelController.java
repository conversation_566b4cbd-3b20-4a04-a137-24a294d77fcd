package cn.iocoder.yudao.module.ai.controller.admin.model;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.model.AiYyModelPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.model.AiYyModelRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.model.AiYyModelSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.service.model.AiYyModelService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;

@Tag(name = "管理后台 - AI咿呀 聊天模型")
@RestController
@RequestMapping("/ai/yy-model")
@Validated
public class AiYyModelController {

    @Resource
    private AiYyModelService modelService;

    @Hidden
    @PostMapping("/create")
    @Operation(summary = "创建聊天模型")
    @PreAuthorize("@ss.hasPermission('ai:yy-model:create')")
    public CommonResult<Long> createChatModel(@Valid @RequestBody AiYyModelSaveReqVO createReqVO) {
        return success(modelService.createModel(createReqVO));
    }

    @Hidden
    @PutMapping("/update")
    @Operation(summary = "更新聊天模型")
    @PreAuthorize("@ss.hasPermission('ai:yy-model:update')")
    public CommonResult<Boolean> updateChatModel(@Valid @RequestBody AiYyModelSaveReqVO updateReqVO) {
        modelService.updateModel(updateReqVO);
        return success(true);
    }

    @Hidden
    @DeleteMapping("/delete")
    @Operation(summary = "删除聊天模型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:yy-model:delete')")
    public CommonResult<Boolean> deleteChatModel(@RequestParam("id") Long id) {
        modelService.deleteModel(id);
        return success(true);
    }

    @Hidden
    @GetMapping("/get")
    @Operation(summary = "获得聊天模型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:yy-model:query')")
    public CommonResult<AiYyModelRespVO> getChatModel(@RequestParam("id") Long id) {
        AiYyModelDO chatModel = modelService.getModel(id);
        return success(BeanUtils.toBean(chatModel, AiYyModelRespVO.class));
    }

    @GetMapping("/getByPlatform")
    @Operation(summary = "获得聊天模型通过模型平台")
    @Parameter(name = "platform", description = "模型平台", required = true, example = "MiniMax")
    @PreAuthorize("@ss.hasPermission('ai:yy-model:query')")
    public CommonResult<List<AiYyModelRespVO>> getByPlatform(@RequestParam("platform") String platform) {
        List<AiYyModelDO> chatModelList = modelService.getChatModelByPlatform(platform);
        return success(BeanUtils.toBean(chatModelList, AiYyModelRespVO.class));
    }

    @Hidden
    @GetMapping("/page")
    @Operation(summary = "获得聊天模型分页")
    @PreAuthorize("@ss.hasPermission('ai:yy-model:query')")
    public CommonResult<PageResult<AiYyModelRespVO>> getChatModelPage(@Valid AiYyModelPageReqVO pageReqVO) {
        PageResult<AiYyModelDO> pageResult = modelService.getModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AiYyModelRespVO.class));
    }

    @Hidden
    @GetMapping("/simple-list")
    @Operation(summary = "获得聊天模型列表")
    @Parameter(name = "status", description = "状态", required = true, example = "1")
    public CommonResult<List<AiYyModelRespVO>> getChatModelSimpleList(@RequestParam("status") Integer status) {
        List<AiYyModelDO> list = modelService.getModelListByStatus(status);
        return success(convertList(list, model -> new AiYyModelRespVO().setId(model.getId())
                .setName(model.getName()).setModel(model.getModel()).setPlatform(model.getPlatform())));
    }

    @Hidden
    @GetMapping("/simple-list-type")
    @Operation(summary = "获得模型列表")
    @Parameter(name = "type", description = "类型", required = true, example = "1")
    @Parameter(name = "platform", description = "平台", example = "midjourney")
    public CommonResult<List<AiYyModelRespVO>> getModelSimpleList(
            @RequestParam("type") Integer type,
            @RequestParam(value = "platform", required = false) String platform) {
        List<AiYyModelDO> list = modelService.getModelListByStatusAndType(
                CommonStatusEnum.ENABLE.getStatus(), type, platform);
        return success(convertList(list, model -> new AiYyModelRespVO().setId(model.getId())
                .setName(model.getName()).setModel(model.getModel()).setPlatform(model.getPlatform())));
    }

}
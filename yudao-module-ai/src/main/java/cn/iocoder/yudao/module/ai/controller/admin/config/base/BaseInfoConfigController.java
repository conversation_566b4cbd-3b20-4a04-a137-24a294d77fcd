package cn.iocoder.yudao.module.ai.controller.admin.config.base;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.config.base.vo.BaseInfoConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.config.base.vo.BaseInfoConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.BaseInfoConfigDO;
import cn.iocoder.yudao.module.ai.service.config.base.BaseInfoConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 基础配置")
@RestController
@RequestMapping("/ai/base-info-config")
@Validated
public class BaseInfoConfigController {

    @Resource
    private BaseInfoConfigService baseInfoConfigService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "创建/修改基础配置")
    @PreAuthorize("@ss.hasPermission('ai:base-info-config:createOrUpdate')")
    public CommonResult<Long> saveOrUpdateBaseInfoConfig(@Valid @RequestBody BaseInfoConfigSaveReqVO createReqVO) {
        return success(baseInfoConfigService.saveOrUpdateBaseInfoConfig(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得基础配置")
    @PreAuthorize("@ss.hasPermission('ai:base-info-config:query')")
    public CommonResult<BaseInfoConfigRespVO> getSimpleBaseInfoConfig() {
        BaseInfoConfigDO baseInfoConfig = baseInfoConfigService.getSimpleBaseInfoConfig();
        return success(BeanUtils.toBean(baseInfoConfig, BaseInfoConfigRespVO.class));
    }

}
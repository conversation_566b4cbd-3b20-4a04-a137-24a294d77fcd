package cn.iocoder.yudao.module.ai.dto;

import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2025/1/19 18:13
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class MinMaxResponse {

    private Boolean lastChunk = true;
    private Boolean inputSensitive;
    private Boolean outputSensitive;
    private Integer inputSensitiveType;
    private Integer outputSensitiveType;
    private Usage usage;
    private BaseResponse baseResponse;
}

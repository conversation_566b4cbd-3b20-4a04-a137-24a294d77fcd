package cn.iocoder.yudao.module.ai.mq.consumer.coin;

import cn.iocoder.yudao.module.ai.service.config.base.BaseInfoConfigService;
import cn.iocoder.yudao.module.member.api.message.user.MemberUserCreateMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 用户注册时，赠送算力的消费者，基 {@link MemberUserCreateMessage} 消息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SendCoinByRegisterConsumer {

    @Resource
    private BaseInfoConfigService baseInfoConfigService;

    @EventListener
    @Async // Spring Event 默认在 Member Producer 发送的线程，通过 @Async 实现异步
    public void onMessage(MemberUserCreateMessage message) {
        log.info("[赠送算力-onMessage][消息内容({})]", message);
        baseInfoConfigService.sendCoinByRegister(message.getUserId());
    }

}

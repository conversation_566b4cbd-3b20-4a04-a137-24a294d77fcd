package cn.iocoder.yudao.module.ai.service.gallery;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryCommonDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryConfigDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageDO;
import cn.iocoder.yudao.module.ai.dal.mysql.gallery.GalleryCommonMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.image.draw.TaskDrawMapper;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.pay.api.coin.MemberCoinApi;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.ai.service.CommonService.checkUserCoin;
import static cn.iocoder.yudao.module.ai.service.CommonService.payMentReduceCoin;
import static cn.iocoder.yudao.module.ai.util.CompressImage.compressImageFromUrl;

/**
 * AI 公开画廊 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GalleryCommonServiceImpl implements GalleryCommonService {

    @Resource
    private GalleryCommonMapper galleryCommonMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private TaskDrawMapper taskDrawMapper;

    @Resource
    private GalleryConfigService galleryConfigService;

    @Resource
    private MemberCoinApi memberCoinApi;

    @Resource
    private FileApi fileApi;

    @Override
    public Long createGalleryCommon(GalleryCommonSaveReqVO createReqVO) {
        // 插入
        GalleryCommonDO galleryCommon = BeanUtils.toBean(createReqVO, GalleryCommonDO.class);
        galleryCommonMapper.insert(galleryCommon);
        // 返回
        return galleryCommon.getId();
    }

    @Override
    public void updateGalleryCommon(GalleryCommonSaveReqVO updateReqVO) {
        // 校验存在
        validateGalleryCommonExists(updateReqVO.getId());
        // 更新
        GalleryCommonDO updateObj = BeanUtils.toBean(updateReqVO, GalleryCommonDO.class);
        galleryCommonMapper.updateById(updateObj);
    }

    @Override
    public void deleteGalleryCommon(Long id) {
        // 校验存在
        validateGalleryCommonExists(id);
        // 删除
        galleryCommonMapper.deleteById(id);
    }

    private void validateGalleryCommonExists(Long id) {
        if (galleryCommonMapper.selectById(id) == null) {
            throw exception(GALLERY_COMMON_NOT_EXISTS);
        }
    }

    @Override
    public GalleryCommonDO getGalleryCommon(Long id) {
        return galleryCommonMapper.selectById(id);
    }

    @Override
    public PageResult<GalleryCommonDO> getGalleryCommonPage(GalleryCommonPageReqVO pageReqVO) {
        return galleryCommonMapper.selectPage(pageReqVO);
    }

    private static final String GALLERY_KEY_PREFIX = "user:gallery:";
    private static final String GALLERY_LIST_KEY = ":list";    // 缓存图片列表的 key 后缀
    private static final String GALLERY_CONSUMED_KEY = ":consumed";  // 缓存已消费图片的 key 后缀
    private static final String GALLERY_FIRST_PIC_KEY = ":firstPic"; // 缓存第一次返回的图片
    private static final String GALLERY_INDEX_KEY = ":index";

    @Override
    public AppGalleryCommonRespVO getAppGalleryCommon(Long userId,Long id) {
        GalleryCommonDO galleryCommon = galleryCommonMapper.selectById(id);
        List<String> imgUrls = galleryCommon.getImgUrls();
        String firstPicKey = GALLERY_KEY_PREFIX + userId +":"+id+ GALLERY_FIRST_PIC_KEY;
        String firstPicUrl ="";
        if (ObjectUtil.isNull(stringRedisTemplate.opsForValue().get(firstPicKey))){
            firstPicUrl = imgUrls.get(RandomUtil.randomInt(imgUrls.size()));
            stringRedisTemplate.opsForValue().set(firstPicKey, firstPicUrl);
            cacheImgUrls(userId, imgUrls,id);
        }else {
            firstPicUrl = stringRedisTemplate.opsForValue().get(firstPicKey);
        }

        AppGalleryCommonRespVO respVO = BeanUtils.toBean(galleryCommon, AppGalleryCommonRespVO.class);
        respVO.setImgUrl(firstPicUrl);
        respVO.setImgUrls(null);

        return respVO;
    }

    @Override
    public PageResult<GalleryCommonDO> getAppGalleryCommonPage(AppGalleryCommonPageReqVO pageReqVO) {
        return galleryCommonMapper.selectAppPage(pageReqVO);
    }

    @Override
    @Transactional
    public Long drawSame(Long userId,Long id) {
        if (userId == null){
            throw exception(USER_NOT_LOGIN);
        }

        //1. 检测是否有正在等待执行和执行中的任务 最大任务数3
        Assert.isFalse(
                taskDrawMapper.selectCount(new LambdaQueryWrapper<AiImageDO>()
                        .eq(AiImageDO::getUserId, userId)
                        .eq(AiImageDO::getPlatform, AiPlatformEnum.GALLERY.getPlatform())
                        .in(AiImageDO::getStatus, CollUtil.newArrayList(AiImageStatusEnum.WAITING.getStatus(), AiImageStatusEnum.IN_PROGRESS.getStatus())))
                        >= 3,
                () -> new ServiceException(AI_DRAW_TASK_IS_QUEUE_FULL)
        );
        //1.1 根据配置查询需要消耗积分数
        GalleryConfigDO galleryConfigDO = galleryConfigService.getGalleryConfig();
        if (ObjUtil.isNull(galleryConfigDO)){
            throw new ServiceException(GALLERY_CONFIG_NOT_EXISTS);
        }

        //1.2 校验用户算力余额
        checkUserCoin(userId, galleryConfigDO.getUseCoin());

        GalleryCommonDO galleryCommonDO = galleryCommonMapper.selectById(id);
        String nextImgUrl =getNextImgUrl(userId,id);
        if (ObjectUtil.isNull(nextImgUrl)){
            throw exception(GALLERY_IMAGE_NOT_EXISTS);
        }
        //获取压缩图片
        byte[] compressbyte = compressImageFromUrl(nextImgUrl,0.8);
        String compressPicUrl = fileApi.createFile(compressbyte);

        // 2. 保存数据库
        AiImageDO image = new AiImageDO().setUserId(userId).setPublicStatus(false)
                .setStatus(AiImageStatusEnum.WAITING.getStatus())
                .setModel(AiPlatformEnum.GALLERY.getPlatform()).setPrompt(galleryCommonDO.getPrompt())
                .setWidth(1024).setHeight(1024)
                .setCompressPicUrl(compressPicUrl)
                .setPicUrl(Collections.singletonList(nextImgUrl))
                .setModelName(AiPlatformEnum.GALLERY.getName()).setAiCoin(galleryConfigDO.getUseCoin());
        taskDrawMapper.insert(image);
        //扣减算力
        //3 扣减用户算力余额 包含更新用户总算力余额
        payMentReduceCoin(userId, galleryConfigDO.getUseCoin(),"AI-绘画广场-AI绘画", image.getId());
        PayCoinBizTypeEnum.setDescription(PayCoinBizTypeEnum.PAYMENT.getType(), "AI-绘画广场-AI绘画");
        memberCoinApi.reduceCoin(userId, galleryConfigDO.getUseCoin(), PayCoinBizTypeEnum.PAYMENT, id);
        //4 更新任务状态 待执行 加7-15秒延时
        getSelf().updataDrawSuccess(AiImageDO.builder().id(image.getId()).status(AiImageStatusEnum.SUCCESS.getStatus()).build());
        //taskDrawMapper.updateById(AiImageDO.builder().id(image.getId()).status(AiImageStatusEnum.SUCCESS.getStatus()).build());
        return image.getId();
    }

    @Async
    public void updataDrawSuccess(AiImageDO aiImageDO) {
        // 更新数据库
        //加7-15秒延时
        int delaySeconds = RandomUtil.randomInt(7, 15);
        ThreadUtil.sleep(delaySeconds * 1000);
        taskDrawMapper.updateById(aiImageDO);
    }

    // 存储用户的图片 URL 列表到 Redis
    public void cacheImgUrls(Long userId, List<String> imgUrls, Long id) {
        try {
            String listKey = GALLERY_KEY_PREFIX + userId+":"+ id + GALLERY_LIST_KEY;
            String consumedKey = GALLERY_KEY_PREFIX + userId +":"+ id + GALLERY_CONSUMED_KEY;

            // 存入图片列表到 Redis
            stringRedisTemplate.opsForValue().set(listKey, JSON.toJSONString(imgUrls));
            // 初始化已消费列表为空
            stringRedisTemplate.opsForValue().set(consumedKey, JSON.toJSONString(new ArrayList<>()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getNextImgUrl(Long userId, Long id) {
        try {
            String listKey = GALLERY_KEY_PREFIX + userId+":"+ id + GALLERY_LIST_KEY;
            String consumedKey = GALLERY_KEY_PREFIX + userId+":"+ id + GALLERY_CONSUMED_KEY;
            String firstPicKey = GALLERY_KEY_PREFIX + userId+":"+ id + GALLERY_FIRST_PIC_KEY;
            String indexKey = GALLERY_KEY_PREFIX + userId+":"+ id + GALLERY_INDEX_KEY;  // 存储轮转选择的索引

            // 获取缓存中的图片列表和已消费的图片列表
            String imgUrlsJson = stringRedisTemplate.opsForValue().get(listKey);
            String consumedJson = stringRedisTemplate.opsForValue().get(consumedKey);
            String firstPic = stringRedisTemplate.opsForValue().get(firstPicKey);

            if (imgUrlsJson == null || consumedJson == null) {
                return null; // 如果没有缓存数据，返回 null
            }

            List<String> imgUrls = JSON.parseArray(imgUrlsJson, String.class);
            List<String> consumedList = JSON.parseArray(consumedJson, String.class);

            // 去除已消费的图片以及第一次返回的图片
            List<String> availableImages = new ArrayList<>(imgUrls);
            availableImages.removeAll(consumedList);
            if (!firstPic.isEmpty()) {
                availableImages.remove(firstPic); // 排除第一次返回的图片
            }

            if (availableImages.isEmpty()) {
                // 如果所有图片都已经消费完，重新开始循环，但排除第一次返回的图片
                availableImages = new ArrayList<>(imgUrls);
                availableImages.remove(firstPic); // 排除第一次返回的图片
                consumedList = new ArrayList<>();
                stringRedisTemplate.delete(consumedKey);
            }

            // 获取轮转选择的当前索引
            Integer currentIndex = null;
            String indexValue = stringRedisTemplate.opsForValue().get(indexKey);
            if (indexValue != null) {
                try {
                    currentIndex = Integer.valueOf(indexValue);
                } catch (NumberFormatException e) {
                    // 如果无法解析为整数，设置为默认值
                    currentIndex = 0;
                }
            }

            // 如果没有索引或者索引越界，则从头开始
            if (currentIndex == null || currentIndex >= availableImages.size()) {
                currentIndex = 0;
            }


            // 根据当前索引选择图片
            String nextImgUrl = availableImages.get(currentIndex);

            // 更新轮转索引，确保下次选择的是下一个图片
            currentIndex = (currentIndex + 1) % availableImages.size();  // 如果到达列表末尾，重置为 0
            stringRedisTemplate.opsForValue().set(indexKey, String.valueOf(currentIndex));

            // 更新已消费列表
            consumedList.add(nextImgUrl);
            stringRedisTemplate.opsForValue().set(consumedKey, JSON.toJSONString(consumedList));

            // 如果是第一次返回图片，记录下来
            if (firstPic.isEmpty()) {
                stringRedisTemplate.opsForValue().set(firstPicKey, nextImgUrl);
            }

            return nextImgUrl;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private GalleryCommonServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
package cn.iocoder.yudao.module.ai.controller.admin.gallery;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryCommonDO;
import cn.iocoder.yudao.module.ai.service.gallery.GalleryCommonService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI 绘画广场")
@RestController
@RequestMapping("/ai/gallery-common")
@Validated
public class GalleryCommonController {

    @Resource
    private GalleryCommonService galleryCommonService;

    @PostMapping("/create")
    @Operation(summary = "创建AI 绘画广场")
    @PreAuthorize("@ss.hasPermission('ai:gallery-common:create')")
    public CommonResult<Long> createGalleryCommon(@Valid @RequestBody GalleryCommonSaveReqVO createReqVO) {
        return success(galleryCommonService.createGalleryCommon(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI 绘画广场")
    @PreAuthorize("@ss.hasPermission('ai:gallery-common:update')")
    public CommonResult<Boolean> updateGalleryCommon(@Valid @RequestBody GalleryCommonSaveReqVO updateReqVO) {
        galleryCommonService.updateGalleryCommon(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 绘画广场")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:gallery-common:delete')")
    public CommonResult<Boolean> deleteGalleryCommon(@RequestParam("id") Long id) {
        galleryCommonService.deleteGalleryCommon(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI 绘画广场")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:gallery-common:query')")
    public CommonResult<GalleryCommonRespVO> getGalleryCommon(@RequestParam("id") Long id) {
        GalleryCommonDO galleryCommon = galleryCommonService.getGalleryCommon(id);
        return success(BeanUtils.toBean(galleryCommon, GalleryCommonRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI 绘画广场分页")
    @PreAuthorize("@ss.hasPermission('ai:gallery-common:query')")
    public CommonResult<PageResult<GalleryCommonRespVO>> getGalleryCommonPage(@Valid GalleryCommonPageReqVO pageReqVO) {
        PageResult<GalleryCommonDO> pageResult = galleryCommonService.getGalleryCommonPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GalleryCommonRespVO.class));
    }

    @Hidden
    @GetMapping("/export-excel")
    @Operation(summary = "导出AI 绘画广场 Excel")
    @PreAuthorize("@ss.hasPermission('ai:gallery-common:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGalleryCommonExcel(@Valid GalleryCommonPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GalleryCommonDO> list = galleryCommonService.getGalleryCommonPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI 绘画广场.xls", "数据", GalleryCommonRespVO.class,
                        BeanUtils.toBean(list, GalleryCommonRespVO.class));
    }

}
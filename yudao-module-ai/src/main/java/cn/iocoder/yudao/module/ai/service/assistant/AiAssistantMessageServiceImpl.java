package cn.iocoder.yudao.module.ai.service.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageSendReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.message.AiAssistantMessageSendRespVO;
import cn.iocoder.yudao.module.ai.controller.app.assistant.vo.message.AppAssistantMessagePraiseReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantMessageDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.dal.mysql.assistant.AiAssistantMessageMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.assistant.AiAssistantRoleMapper;
import cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.iocoder.yudao.module.ai.service.model.AiYyModelService;
import cn.iocoder.yudao.module.ai.service.sensitiveword.SensitiveWordService;
import cn.iocoder.yudao.module.ai.util.AiUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.StreamingChatModel;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.ai.service.CommonService.checkUserCoin;
import static cn.iocoder.yudao.module.ai.service.CommonService.payMentReduceCoin;

/**
 * AI 聊天消息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiAssistantMessageServiceImpl implements AiAssistantMessageService {

    @Resource
    private AiAssistantMessageMapper assistantMessageMapper;

    @Resource
    private AiAssistantConversationService assistantConversationService;

    @Resource
    private AiAssistantRoleMapper assistantRoleMapper;

    @Resource
    private AiYyModelService modelService;

    @Resource
    private SensitiveWordService sensitiveWordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiAssistantMessageSendRespVO sendMessage(AiAssistantMessageSendReqVO sendReqVO, Long userId) {
        // 1.1 校验对话存在
        AiAssistantConversationDO conversation = assistantConversationService.validateAssistantConversationExists(sendReqVO.getConversationId());
        if (ObjUtil.notEqual(conversation.getUserId(), userId)) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        List<AiAssistantMessageDO> historyMessages = assistantMessageMapper.selectListByConversationId(conversation.getId());
        // 1.2 校验模型
        AiYyModelDO model = modelService.validateModel(conversation.getModelId());
        ChatModel chatModel = modelService.getChatModel(model.getKeyId());

        // 2. 插入 user 发送消息
        AiAssistantMessageDO userMessage = createAssistantMessage(conversation.getId(), null, model,
                userId, conversation.getRoleId(), MessageType.USER, sendReqVO.getContent(), sendReqVO.getUseContext(),null);

        // 3.1 插入 assistant 接收消息
        AiAssistantMessageDO assistantMessage = createAssistantMessage(conversation.getId(), userMessage.getId(), model,
                userId, conversation.getRoleId(), MessageType.ASSISTANT, "", sendReqVO.getUseContext(),null);

        // 3.2 创建 chat 需要的 Prompt
        Prompt prompt = buildPrompt(conversation, historyMessages, model, sendReqVO);
        ChatResponse chatResponse = chatModel.call(prompt);

        // 3.3段式返回
        String newContent = chatResponse.getResult().getOutput().getText();
        assistantMessageMapper.updateById(new AiAssistantMessageDO().setId(assistantMessage.getId()).setContent(newContent));
        return new AiAssistantMessageSendRespVO().setSend(BeanUtils.toBean(userMessage, AiAssistantMessageSendRespVO.Message.class))
                .setReceive(BeanUtils.toBean(assistantMessage, AiAssistantMessageSendRespVO.Message.class).setContent(newContent));
    }

    @Override
    public Flux<CommonResult<AiAssistantMessageSendRespVO>> sendAssistantMessageStream(AiAssistantMessageSendReqVO sendReqVO, Long userId) {
        sendReqVO.setUseContext(true);
        // 1.1 校验对话存在
        AiAssistantConversationDO conversation = assistantConversationService.validateAssistantConversationExists(sendReqVO.getConversationId());
        if (ObjUtil.notEqual(conversation.getUserId(), userId)) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        List<AiAssistantMessageDO> historyMessages = assistantMessageMapper.selectListByConversationId(conversation.getId());
        // 1.2 校验模型
        AiYyModelDO model = modelService.validateModel(conversation.getModelId());
        StreamingChatModel chatModel = modelService.getChatModel(model.getKeyId());
        //1.3 敏感词校验
        boolean isValid = sensitiveWordService.isTextValid(sendReqVO.getContent());
        if (!isValid){
            throw exception(SENSITIVE_WORD_IS_EXISTS_APP);
        }

        // 1.4 校验积分
        AiAssistantRoleDO role = assistantRoleMapper.selectById(conversation.getRoleId());
        checkUserCoin(userId, role.getCoin());

        // 2. 插入 user 发送消息
        AiAssistantMessageDO userMessage = createAssistantMessage(conversation.getId(), null, model,
                userId, conversation.getRoleId(), MessageType.USER, sendReqVO.getContent(), sendReqVO.getUseContext(), null);

        // 3.1 插入 assistant 接收消息
        AiAssistantMessageDO assistantMessage = createAssistantMessage(conversation.getId(), userMessage.getId(), model,
                userId, conversation.getRoleId(), MessageType.ASSISTANT, "", sendReqVO.getUseContext(),role.getCoin());
        assistantMessage.setTenantId(role.getTenantId());
        assistantMessage.setUserId(userId);
        // 3.2 构建 Prompt，并进行调用
        Prompt prompt = buildPrompt(conversation, historyMessages, model, sendReqVO);

        // 🎯 核心改动：包装成热流，可多订阅，防断开
        Flux<ChatResponse> streamResponse = chatModel.stream(prompt)
                .publish()
                .autoConnect(0)// 立刻connect，不等待订阅
                .cache();

        //Flux<ChatResponse> streamResponse = chatModel.stream(prompt);

        // success 为 true 表示流处理成功，否则为异常情况 扣减用户算力
        payMentReduceCoin(assistantMessage.getUserId(), assistantMessage.getAiCoin(),"智能助手", assistantMessage.getId());
        // 3.3 流式返回
        // TODO 注意：Schedulers.immediate() 目的是，避免默认 Schedulers.parallel() 并发消费 chunk 导致 SSE 响应前端会乱序问题
        // 5. 后台订阅：不中断任务，写入数据库
        StringBuilder contentBuffer = new StringBuilder();
        streamResponse
                .map(chunk -> {
                    String content = Optional.ofNullable(chunk.getResult())
                            .map(r -> r.getOutput().getText())
                            .orElse("");
                    contentBuffer.append(content);
                    return content;
                })
                .doOnComplete(() -> {
                    log.info("[sendChatMessageStream] AI响应完毕，写库");
                    saveStreamedMessageToDatabase(assistantMessage, contentBuffer.toString(), true);
                })
                .doOnError(error -> {
                    log.error("[sendChatMessageStream] 流处理异常：", error);
                    saveStreamedMessageToDatabase(assistantMessage, error.getMessage(), false);
                })
                .subscribe(); // ✅ 后台执行，独立于前端
        // 6. 前端订阅：流式返回响应（保留原逻辑）
        return streamResponse.map(chunk -> {
                    String newContent = Optional.ofNullable(chunk.getResult())
                            .map(r -> r.getOutput().getText())
                            .orElse("");
                    return success(new AiAssistantMessageSendRespVO()
                            .setSend(BeanUtils.toBean(userMessage, AiAssistantMessageSendRespVO.Message.class))
                            .setReceive(BeanUtils.toBean(assistantMessage, AiAssistantMessageSendRespVO.Message.class)
                                    .setContent(newContent)));
                })
                .onErrorResume(error -> {
                    log.error("[sendChatMessageStream] SSE流异常：", error);
                    return Flux.just(error(ErrorCodeConstants.CHAT_STREAM_ERROR));
                });
        /*Flux<ChatResponse> streamResponse = chatModel.stream(prompt);

        // 4.3 流式返回
        StringBuffer contentBuffer = new StringBuffer();
        return streamResponse.map(chunk -> {
            // 响应结果
            String newContent = chunk.getResult() != null ? chunk.getResult().getOutput().getText() : null;
            newContent = StrUtil.nullToDefault(newContent, ""); // 避免 null 的 情况
            contentBuffer.append(newContent);
            return success(new AiAssistantMessageSendRespVO()
                    .setSend(BeanUtils.toBean(userMessage, AiAssistantMessageSendRespVO.Message.class))
                    .setReceive(BeanUtils.toBean(assistantMessage, AiAssistantMessageSendRespVO.Message.class)
                            .setContent(newContent)));
        }).doOnComplete(() -> {
            // 忽略租户，因为 Flux 异步无法透传租户
            TenantUtils.executeIgnore(() -> assistantMessageMapper.updateById(
                    new AiAssistantMessageDO().setId(assistantMessage.getId()).setContent(contentBuffer.toString())));
        }).doOnError(throwable -> {
            log.error("[sendChatMessageStream][userId({}) sendReqVO({}) 发生异常]", userId, sendReqVO, throwable);
            // 忽略租户，因为 Flux 异步无法透传租户
            TenantUtils.executeIgnore(() -> assistantMessageMapper.updateById(
                    new AiAssistantMessageDO().setId(assistantMessage.getId()).setContent(throwable.getMessage())));
        }).onErrorResume(error -> Flux.just(error(ErrorCodeConstants.CHAT_STREAM_ERROR)));*/
    }

    private void saveStreamedMessageToDatabase(AiAssistantMessageDO assistantMessage, String content, Boolean success) {
        /*TenantUtils.execute(assistantMessage.getTenantId(), () -> {
            // success 为 true 表示流处理成功，否则为异常情况 扣减用户算力
            payMentReduceCoin(assistantMessage.getUserId(), assistantMessage.getAiCoin(),"智能助手", assistantMessage.getId());
        });*/
        // 使用异步线程池执行数据库保存，避免阻塞流的处理
        TenantUtils.executeIgnore(() -> {
            // 更新数据库，避免重复保存
            AiAssistantMessageDO updateMessage = new AiAssistantMessageDO()
                    .setId(assistantMessage.getId())
                    .setContent(content);
            // 进行数据库更新操作
            assistantMessageMapper.updateById(updateMessage);
        });

    }

    private Prompt buildPrompt(AiAssistantConversationDO conversation, List<AiAssistantMessageDO> messages,
                               AiYyModelDO model, AiAssistantMessageSendReqVO sendReqVO) {
        // 1. 构建 Prompt Message 列表
        List<Message> chatMessages = new ArrayList<>();

        // 1.2 system context 角色设定
        if (StrUtil.isNotBlank(conversation.getSystemMessage())) {
            chatMessages.add(new SystemMessage(conversation.getSystemMessage()));
        }
        // 1.3 history message 历史消息
        List<AiAssistantMessageDO> contextMessages = filterContextMessages(messages, conversation, sendReqVO);
        contextMessages.forEach(message -> chatMessages.add(AiUtils.buildMessage(message.getType(), message.getContent())));
        // 1.4 当前 user message 新发送消息
        chatMessages.add(new UserMessage(sendReqVO.getContent()));

        // 2. 构建 ChatOptions 对象
        AiPlatformEnum platform = AiPlatformEnum.validatePlatform(model.getPlatform());
        ChatOptions chatOptions = AiUtils.buildChatOptions(platform, model.getModel(),
                conversation.getTemperature(), conversation.getMaxTokens(), conversation.getTopP(),null,null);
        return new Prompt(chatMessages, chatOptions);
    }

    /**
     * 从历史消息中，获得倒序的 n 组消息作为消息上下文
     * <p>
     * n 组：指的是 user + assistant 形成一组
     *
     * @param messages     消息列表
     * @param conversation 对话
     * @param sendReqVO    发送请求
     * @return 消息上下文
     */
    private List<AiAssistantMessageDO> filterContextMessages(List<AiAssistantMessageDO> messages,
                                                        AiAssistantConversationDO conversation,
                                                        AiAssistantMessageSendReqVO sendReqVO) {
        if (conversation.getMaxContexts() == null || ObjUtil.notEqual(sendReqVO.getUseContext(), Boolean.TRUE)) {
            return Collections.emptyList();
        }
        List<AiAssistantMessageDO> contextMessages = new ArrayList<>(conversation.getMaxContexts() * 2);
        for (int i = messages.size() - 1; i >= 0; i--) {
            AiAssistantMessageDO assistantMessage = CollUtil.get(messages, i);
            if (assistantMessage == null || assistantMessage.getReplyId() == null) {
                continue;
            }
            AiAssistantMessageDO userMessage = CollUtil.get(messages, i - 1);
            if (userMessage == null || ObjUtil.notEqual(assistantMessage.getReplyId(), userMessage.getId())
                    || StrUtil.isEmpty(assistantMessage.getContent())) {
                continue;
            }
            // 由于后续要 reverse 反转，所以先添加 assistantMessage
            contextMessages.add(assistantMessage);
            contextMessages.add(userMessage);
            // 超过最大上下文，结束
            if (contextMessages.size() >= conversation.getMaxContexts() * 2) {
                break;
            }
        }
        Collections.reverse(contextMessages);
        return contextMessages;
    }

    private AiAssistantMessageDO createAssistantMessage(Long conversationId, Long replyId,
                                                        AiYyModelDO model, Long userId, Long roleId,
                                              MessageType messageType, String content, Boolean useContext, Integer coin) {
        AiAssistantMessageDO message = new AiAssistantMessageDO().setConversationId(conversationId).setReplyId(replyId)
                .setModel(model.getModel()).setModelId(model.getId()).setUserId(userId)
                .setType(messageType.getValue()).setContent(content).setUseContext(useContext)
                .setAiCoin(coin);
        message.setCreateTime(LocalDateTime.now());
        assistantMessageMapper.insert(message);
        return message;
    }

    @Override
    public List<AiAssistantMessageDO> getAssistantMessageListByConversationId(Long conversationId) {
        return assistantMessageMapper.selectListByConversationId(conversationId);
    }

    @Override
    public void deleteAssistantMessage(Long id, Long userId) {
        // 1. 校验消息存在
        AiAssistantMessageDO message = assistantMessageMapper.selectById(id);
        if (message == null || ObjUtil.notEqual(message.getUserId(), userId)) {
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }
        // 2. 执行删除
        assistantMessageMapper.deleteById(id);
    }

    @Override
    public void deleteAssistantMessageByConversationId(Long conversationId, Long userId) {
        // 1. 校验消息存在
        List<AiAssistantMessageDO> messages = assistantMessageMapper.selectListByConversationId(conversationId);
        if (CollUtil.isEmpty(messages) || ObjUtil.notEqual(messages.get(0).getUserId(), userId)) {
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }
        // 2. 执行删除
        assistantMessageMapper.deleteBatchIds(convertList(messages, AiAssistantMessageDO::getId));
    }

    @Override
    public void deleteAssistantMessageByAdmin(Long id) {
        // 1. 校验消息存在
        AiAssistantMessageDO message = assistantMessageMapper.selectById(id);
        if (message == null) {
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }
        // 2. 执行删除
        assistantMessageMapper.deleteById(id);
    }

    @Override
    public Map<Long, Integer> getAssistantMessageCountMap(Collection<Long> conversationIds) {
        return assistantMessageMapper.selectCountMapByConversationId(conversationIds);
    }

    @Override
    public PageResult<AiAssistantMessageDO> getAssistantMessagePage(AiAssistantMessagePageReqVO pageReqVO) {
        return assistantMessageMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AiAssistantMessageDO> getAssistantMessagePageByConversationId(AiAssistantMessagePageReqVO pageReqVO) {
        return assistantMessageMapper.getAssistantMessagePageByConversationId(pageReqVO);
    }

    @Override
    public void praiseAssistantMessage(AppAssistantMessagePraiseReqVO reqVO) {
        validateAssistantMessageExists(reqVO.getId());
        // 更新
        AiAssistantMessageDO updateObj = BeanUtils.toBean(reqVO, AiAssistantMessageDO.class);
        assistantMessageMapper.updateById(updateObj);
    }

    /**
     * 校验消息是否存在
     * @param id
     * @return
     */
    private AiAssistantMessageDO validateAssistantMessageExists(Long id) {
        AiAssistantMessageDO assistantMessage = assistantMessageMapper.selectById(id);
        if (assistantMessage == null) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        return assistantMessage;
    }

}

package cn.iocoder.yudao.module.ai.dal.dataobject.model;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * AI API 秘钥 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_yy_api_key")
@Comment(value = "AI 咿呀API 秘钥")
@Entity
@TableName("ai_yy_api_key")
@KeySequence("ai_yy_api_key_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiYyApiKeyDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId
    private Long id;
    /**
     * 名称
     */
    @Column(name = "name", columnDefinition = "varchar(255) NOT NULL COMMENT '名称'")
    private String name;
    /**
     * 密钥
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) NOT NULL COMMENT '密钥'")
    private String apiKey;
    /**
     * 平台
     *
     * 枚举 {@link AiPlatformEnum}
     */
    @Column(name = "platform", columnDefinition = "varchar(255) NOT NULL COMMENT '平台'")
    private String platform;
    /**
     * API 地址
     */
    @Column(name = "url", columnDefinition = "varchar(255) COMMENT 'API 地址'")
    private String url;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Column(columnDefinition = "int NOT NULL COMMENT '状态'")
    private Integer status;

    /**
     * 回调地址
     */
    @Column(length = 500, columnDefinition = "varchar(500) DEFAULT NULL COMMENT '回调地址'")
    private String callbackUrl;

    /**
     * 标识
     */
    @Column(length = 255, columnDefinition = "varchar(255) DEFAULT NULL COMMENT '标识'")
    private String sign;
}

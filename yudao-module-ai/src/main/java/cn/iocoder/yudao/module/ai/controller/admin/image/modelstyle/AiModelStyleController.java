package cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStylePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStyleRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStyleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import cn.iocoder.yudao.module.ai.service.image.aimodelstyle.AiModelStyleService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 风格模型")
@RestController
@RequestMapping("/ai/model-style")
@Validated
public class AiModelStyleController {

    @Resource
    private AiModelStyleService modelStyleService;

    @PostMapping("/create")
    @Operation(summary = "创建风格模型")
    @PreAuthorize("@ss.hasPermission('ai:model-style:create')")
    public CommonResult<Long> createModelStyle(@Valid @RequestBody AiModelStyleSaveReqVO createReqVO) {
        return success(modelStyleService.createModelStyle(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新风格模型")
    @PreAuthorize("@ss.hasPermission('ai:model-style:update')")
    public CommonResult<Boolean> updateModelStyle(@Valid @RequestBody AiModelStyleSaveReqVO updateReqVO) {
        modelStyleService.updateModelStyle(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除风格模型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:model-style:delete')")
    public CommonResult<Boolean> deleteModelStyle(@RequestParam("id") Long id) {
        modelStyleService.deleteModelStyle(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得风格模型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:model-style:query')")
    public CommonResult<AiModelStyleRespVO> getModelStyle(@RequestParam("id") Long id) {
        AiModelStyleDO modelStyle = modelStyleService.getModelStyle(id);
        return success(BeanUtils.toBean(modelStyle, AiModelStyleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得风格模型分页")
    @PreAuthorize("@ss.hasPermission('ai:model-style:query')")
    public CommonResult<PageResult<AiModelStyleRespVO>> getModelStylePage(@Valid AiModelStylePageReqVO pageReqVO) {
        PageResult<AiModelStyleDO> pageResult = modelStyleService.getModelStylePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AiModelStyleRespVO.class));
    }

    @Hidden
    @GetMapping("/export-excel")
    @Operation(summary = "导出风格模型 Excel")
    @PreAuthorize("@ss.hasPermission('ai:model-style:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportModelStyleExcel(@Valid AiModelStylePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiModelStyleDO> list = modelStyleService.getModelStylePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "风格模型.xls", "数据", AiModelStyleRespVO.class,
                        BeanUtils.toBean(list, AiModelStyleRespVO.class));
    }

}
package cn.iocoder.yudao.module.ai.controller.admin.gallery;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryConfigDO;
import cn.iocoder.yudao.module.ai.service.gallery.GalleryConfigService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI 绘画广场配置")
@RestController
@RequestMapping("/ai/gallery-config")
@Validated
public class GalleryConfigController {

    @Resource
    private GalleryConfigService galleryConfigService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "创建AI 绘画广场配置")
    //@PreAuthorize("@ss.hasPermission('ai:gallery-config:create')")
    public CommonResult<Long> saveOrUpdateGalleryConfig(@Valid @RequestBody GalleryConfigSaveReqVO createReqVO) {
        return success(galleryConfigService.saveOrUpdateGalleryConfig(createReqVO));
    }

    @Hidden
    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 绘画广场配置")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('ai:gallery-config:delete')")
    public CommonResult<Boolean> deleteGalleryConfig(@RequestParam("id") Long id) {
        galleryConfigService.deleteGalleryConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI 绘画广场配置")
    //@PreAuthorize("@ss.hasPermission('ai:gallery-config:query')")
    public CommonResult<GalleryConfigRespVO> getGalleryConfig() {
        GalleryConfigDO galleryConfig = galleryConfigService.getGalleryConfig();
        return success(BeanUtils.toBean(galleryConfig, GalleryConfigRespVO.class));
    }

}
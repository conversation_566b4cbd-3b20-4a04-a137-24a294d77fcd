package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioModelDO;
import cn.iocoder.yudao.module.ai.service.virtualpartner.audiomodel.AudioModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;

@Tag(name = "管理后台 - 音色模型")
@RestController
@RequestMapping("/ai/audio-model")
@Validated
public class AudioModelController {

    @Resource
    private AudioModelService audioModelService;

    @PostMapping("/create")
    @Operation(summary = "创建音色模型")
    @PreAuthorize("@ss.hasPermission('ai:audio-model:create')")
    public CommonResult<Long> createAudioModel(@Valid @RequestBody AudioModelSaveReqVO createReqVO) {
        return success(audioModelService.createAudioModel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新音色模型")
    @PreAuthorize("@ss.hasPermission('ai:audio-model:update')")
    public CommonResult<Boolean> updateAudioModel(@Valid @RequestBody AudioModelSaveReqVO updateReqVO) {
        audioModelService.updateAudioModel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除音色模型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:audio-model:delete')")
    public CommonResult<Boolean> deleteAudioModel(@RequestParam("id") Long id) {
        audioModelService.deleteAudioModel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得音色模型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:audio-model:query')")
    public CommonResult<AudioModelRespVO> getAudioModel(@RequestParam("id") Long id) {
        AiAudioModelDO audioModel = audioModelService.getAudioModel(id);
        return success(BeanUtils.toBean(audioModel, AudioModelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得音色模型分页")
    @PreAuthorize("@ss.hasPermission('ai:audio-model:query')")
    public CommonResult<PageResult<AudioModelRespVO>> getAudioModelPage(@Valid AudioModelPageReqVO pageReqVO) {
        PageResult<AiAudioModelDO> pageResult = audioModelService.getAudioModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AudioModelRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出音色模型 Excel")
    @PreAuthorize("@ss.hasPermission('ai:audio-model:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAudioModelExcel(@Valid AudioModelPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiAudioModelDO> list = audioModelService.getAudioModelPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "音色模型.xls", "数据", AudioModelRespVO.class,
                        BeanUtils.toBean(list, AudioModelRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得音色模型列表")
    @Parameter(name = "status", description = "状态", required = true, example = "1")
    public CommonResult<List<AudioModelRespVO>> getAudioModelSimpleList(@RequestParam("status") Integer status) {
        List<AiAudioModelDO> list = audioModelService.getAudioModelListByStatus(status);
        return success(convertList(list, model -> new AudioModelRespVO().setId(model.getId())
                .setVoiceName(model.getVoiceName()).setVoiceId(model.getVoiceId())
                )
        );
    }

}
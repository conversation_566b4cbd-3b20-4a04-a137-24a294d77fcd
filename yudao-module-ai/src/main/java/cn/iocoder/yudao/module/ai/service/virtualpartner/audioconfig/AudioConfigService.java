package cn.iocoder.yudao.module.ai.service.virtualpartner.audioconfig;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AiTextToAudioMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioConfigDO;
import com.minimaxi.platform.t2a.api.MinMaxT2AApi;
import jakarta.validation.Valid;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 语音配置 Service 接口
 *
 * <AUTHOR>
 */
public interface AudioConfigService {

    /**
     * 创建语音配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAudioConfig(@Valid AudioConfigSaveReqVO createReqVO);

    /**
     * 更新语音配置
     *
     * @param updateReqVO 更新信息
     */
    void updateAudioConfig(@Valid AudioConfigSaveReqVO updateReqVO);

    /**
     * 删除语音配置
     *
     * @param id 编号
     */
    void deleteAudioConfig(Long id);

    /**
     * 获得语音配置
     *
     * @param id 编号
     * @return 语音配置
     */
    AiAudioConfigDO getAudioConfig(Long id);

    /**
     * 获得语音配置分页
     *
     * @param pageReqVO 分页查询
     * @return 语音配置分页
     */
    PageResult<AiAudioConfigDO> getAudioConfigPage(AudioConfigPageReqVO pageReqVO);

    /**
     * 发送语音消息（流式）
     * @param sendReqVO
     * @param loginUserId
     * @return
     */
    Flux<CommonResult<MinMaxT2AApi.T2ACompletion>> sendAudioMessageStream(AudioConfigSaveReqVO sendReqVO, Long loginUserId);

    /**
     * 发送语音消息（段式）
     * @param sendReqVO
     * @param loginUserId
     * @return
     */
    AiTextToAudioMessageRespVO sendAudioMessage(AudioConfigSaveReqVO sendReqVO, Long loginUserId);

    /**
     * 获取语音配置列表
     * @param status
     * @return
     */
    List<AiAudioConfigDO> getAudioConfigListByStatus(Integer status);
}
package cn.iocoder.yudao.module.ai.controller.admin.config.base.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 基础配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BaseInfoConfigRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "6148")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "被好友成功邀请，赠送算力")
    @ExcelProperty("被好友成功邀请，赠送算力")
    private Integer inviteFriendGive;

    @Schema(description = "成功邀请好友，赠送算力")
    @ExcelProperty("成功邀请好友，赠送算力")
    private Integer inviteGive;

    @Schema(description = "新用户注册赠送算力")
    @ExcelProperty("新用户注册赠送算力")
    private Integer registerGive;

    @Schema(description = "苹果应用商店ID")
    @ExcelProperty("苹果应用商店ID")
    private String appStoreId;

    @Schema(description = "联系客服配置")
    @ExcelProperty("联系客服配置")
    private String contactCustomer;
}
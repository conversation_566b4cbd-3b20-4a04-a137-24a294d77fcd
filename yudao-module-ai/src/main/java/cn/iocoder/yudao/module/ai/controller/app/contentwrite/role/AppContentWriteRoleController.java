package cn.iocoder.yudao.module.ai.controller.app.contentwrite.role;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.role.vo.AppContentWriteRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.role.vo.AppContentWriteRoleRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteRoleDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.role.ContentWriteRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户APP - 内容创作角色")
@RestController
@RequestMapping("/ai/content-write-role")
@Validated
public class AppContentWriteRoleController {

    @Resource
    private ContentWriteRoleService contentWriteRoleService;

    @GetMapping("/get")
    @Operation(summary = "获取内容创作角色")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    public CommonResult<AppContentWriteRoleRespVO> getAppContentWriteRole(@RequestParam("id") Long id) {
        AiContentWriteRoleDO contentWriteRole = contentWriteRoleService.getContentWriteRole(id);
        return success(BeanUtils.toBean(contentWriteRole, AppContentWriteRoleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获取内容创作角色分页")
    public CommonResult<PageResult<AppContentWriteRoleRespVO>> getAppContentWriteRolePage(@Valid AppContentWriteRolePageReqVO pageReqVO) {
        ContentWriteRolePageReqVO rolePageReqVO = BeanUtils.toBean(pageReqVO, ContentWriteRolePageReqVO.class);
        PageResult<AiContentWriteRoleDO> pageResult = contentWriteRoleService.getContentWriteRolePage(rolePageReqVO);
        return success(BeanUtils.toBean(pageResult, AppContentWriteRoleRespVO.class));
    }
}
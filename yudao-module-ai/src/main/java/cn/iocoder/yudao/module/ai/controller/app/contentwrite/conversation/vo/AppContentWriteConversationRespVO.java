package cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户APP - AI 内容写作对话 Response VO")
@Data
public class AppContentWriteConversationRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4717")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16872")
    private Long userId;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private String model;

    @Schema(description = "模型编号", example = "31192")
    private Long modelId;

    @Schema(description = "角色编号", example = "5227")
    private Long roleId;

    @Schema(description = "温度参数")
    private Double temperature;

    @Schema(description = "对话标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "采样方法")
    private Double topP;

    @Schema(description = "上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "单条回复的最大 Token 数量")
    private Integer maxTokens;
}
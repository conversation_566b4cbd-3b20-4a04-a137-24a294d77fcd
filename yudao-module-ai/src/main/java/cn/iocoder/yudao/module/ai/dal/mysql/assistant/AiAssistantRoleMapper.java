package cn.iocoder.yudao.module.ai.dal.mysql.assistant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.assistantRole.AiAssistantRolePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantRoleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI 聊天角色 Mapper
 *
 */
@Mapper
public interface AiAssistantRoleMapper extends BaseMapperX<AiAssistantRoleDO> {

    default PageResult<AiAssistantRoleDO> selectPage(AiAssistantRolePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiAssistantRoleDO>()
                .likeIfPresent(AiAssistantRoleDO::getName, reqVO.getName())
                .orderByAsc(AiAssistantRoleDO::getSort));
    }

    default List<AiAssistantRoleDO> selectListByName(String name) {
        return selectList(new LambdaQueryWrapperX<AiAssistantRoleDO>()
                .likeIfPresent(AiAssistantRoleDO::getName, name)
                .orderByAsc(AiAssistantRoleDO::getSort));
    }

    default AiAssistantRoleDO selectFirstByDefault(Integer status){
        return selectOne(new LambdaQueryWrapperX<AiAssistantRoleDO>()
                .eq(AiAssistantRoleDO::getDefaultRole, status)
                .eq(AiAssistantRoleDO::getStatus, status));
    };
}

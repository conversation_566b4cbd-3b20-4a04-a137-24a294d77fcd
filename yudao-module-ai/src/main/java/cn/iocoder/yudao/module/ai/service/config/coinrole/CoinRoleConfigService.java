package cn.iocoder.yudao.module.ai.service.config.coinrole;

import cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo.CoinRoleConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.CoinRoleConfigDO;
import jakarta.validation.Valid;

/**
 * 算力规则配置 Service 接口
 *
 * <AUTHOR>
 */
public interface CoinRoleConfigService {

    /**
     * 创建/修改算力规则配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveOrUpdateCoinRoleConfig(@Valid CoinRoleConfigSaveReqVO createReqVO);

    /**
     * 获得基础配置
     *
     * @return 基础配置
     */
    CoinRoleConfigDO getSimpleCoinRoleConfig();
}
package cn.iocoder.yudao.module.ai.dal.dataobject.invite;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * 邀请记录 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_invite_record")
@Comment(value = "邀请记录")
@Entity
@TableName("ai_invite_record")
@KeySequence("ai_invite_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiInviteRecordDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Integer id;
    /**
     * 用户编号
     * <p>
     * 关联 MemberUserDO.id
     */
    @Column(columnDefinition = "bigint COMMENT '用户编号'")
    private Long userId;
    /**
     * 业务编号
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '业务编号'")
    private String bizId;
    /**
     * 业务类型
     * <p>
     * 枚举 {@link }
     */
    @Column(columnDefinition = "int COMMENT '业务类型'")
    private Integer bizType;

    /**
     * 标题
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '标题'")
    private String title;
    /**
     * 说明
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '说明'")
    private String description;

    /**
     * 算力
     */
    @Column(columnDefinition = "int COMMENT '算力'")
    private Integer coin;

    /**
     * 状态
     * <p>
     * 枚举 {@link }
     */
    @Column(columnDefinition = "int COMMENT '状态'")
    private Integer status;
    /**
     * 来源用户编号
     * <p>
     * 关联 MemberUserDO.id 字段，被推广用户的编号
     */
    @Column(columnDefinition = "bigint COMMENT '来源用户编号'")
    private Long sourceUserId;

    /**
     * 来源算力
     */
    @Column(columnDefinition = "int COMMENT '来源算力'")
    private Integer sourceCoin;

    /**
     * 邀请码
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '邀请码'")
    private String inviteCode;
}

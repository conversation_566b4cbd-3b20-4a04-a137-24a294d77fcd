package cn.iocoder.yudao.module.ai.controller.app.myapi.vo;

/**
 * @Description: TODO
 * @Date: 2025/1/13 15:50
 * @Author: zhangq
 * @Version: 1.0
 */

import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 创建图片的响应 VO
 */
@Data
public class AppDrawImageWorksRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;
    /**
     * 压缩图片地址
     */
    @Schema(description = "压缩图片地址")
    @ExcelProperty("压缩图片地址")
    private String compressPicUrl;
    /**
     * 名称
     */
    @Schema(description = "绘画图片地址")
    @ExcelProperty("名称")
    private List<String> picUrl;

    @Schema(description = "状态, 0 待处理 10 生成中 20 已完成 30 失败")
    private Integer status;

    @Schema(description = "提示词")
    @ExcelProperty("提示词")
    private String prompt;
}

package cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 提示词 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PromptWordsRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "28829")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "提示词")
    @ExcelProperty("提示词")
    private String promptWords;

    @Schema(description = "提示词类型", example = "1")
    @ExcelProperty(value = "提示词类型", converter = DictConvert.class)
    //@DictFormat("prompt_words_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String promptWordsType;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

}
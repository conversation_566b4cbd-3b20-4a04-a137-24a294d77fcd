package cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 风格模型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AiModelStylePageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "风格描述")
    private String modelStyleDesc;

    @Schema(description = "触站Model ID", example = "17062")
    private Integer modelStyleId;

    @Schema(description = "效果图")
    private String modelStyleImg;

    @Schema(description = "风格名称", example = "赵六")
    private String modelStyleName;

    @Schema(description = "使用次数", example = "29343")
    private Integer modelStyleUseCount;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
package cn.iocoder.yudao.module.ai.dal.dataobject.complaint;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import java.util.List;

/**
 * 投诉 DO
 */
@Table(name = "ai_complaint_feedback")
@Comment(value = "投诉/反馈")
@Entity
@TableName(value = "ai_complaint_feedback", autoResultMap = true)
@KeySequence("ai_complaint_feedback_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintFeedbackDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    @Column(columnDefinition = "bigint COMMENT '用户编号'")
    private Long userId;

    /**
     * 手机号
     */
    @Column(length = 11, columnDefinition = "varchar(11) COMMENT '手机号'")
    private String mobile;

    /**
     * 类型 投诉 反馈
     */
    @Column(length = 32,columnDefinition = "varchar(32) COMMENT '类型'")
    private String type;

    /**
     * 投诉图片
     */
    @Type(JsonType.class)
    @Column(length = 2048,columnDefinition = "varchar(2048) COMMENT '投诉图片'")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;

    /**
     * 投诉编号
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '编号'")
    private String no;

    /**
     * 原因
     */
    @Column(length = 1024,columnDefinition = "varchar(1024) COMMENT '原因'")
    private String content;

    /**
     * 反馈类型(1.回答速度慢 2.内容错误)
     */
    @Column(length = 32,columnDefinition = "varchar(32) COMMENT '反馈类型'")
    private String feedbackType;

    /**
     * 反馈类目 1-建议 2-吐槽 3-bug 4-其他
     */
    @Column(length = 32,columnDefinition = "varchar(32) COMMENT '反馈类目'")
    private String feedbackCategory;

    /**
     * 资源类型 1-智能助手 2-虚拟陪伴 3-内容创作
     */
    @Column(length = 32,columnDefinition = "varchar(32) COMMENT '资源类型'")
    private String resourceType;

    /**
     * 资源Id
     */
    @Column(columnDefinition = "varchar(255) COMMENT '资源id'")
    private String resourceId;
}

package cn.iocoder.yudao.module.ai.controller.admin.promptwords;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.promptwords.PromptWordsDO;
import cn.iocoder.yudao.module.ai.service.promptwords.PromptWordsService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 绘画/智能助手提示词")
@RestController
@RequestMapping("/ai/ai-prompt-words")
@Validated
public class PromptWordsController {

    @Resource
    private PromptWordsService promptWordsService;

    @PostMapping("/create")
    @Operation(summary = "创建提示词")
    @PreAuthorize("@ss.hasPermission('ai:prompt-words:create')")
    public CommonResult<Long> createPromptWords(@Valid @RequestBody PromptWordsSaveReqVO createReqVO) {
        return success(promptWordsService.createPromptWords(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新提示词")
    @PreAuthorize("@ss.hasPermission('ai:prompt-words:update')")
    public CommonResult<Boolean> updatePromptWords(@Valid @RequestBody PromptWordsSaveReqVO updateReqVO) {
        promptWordsService.updatePromptWords(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除提示词")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:prompt-words:delete')")
    public CommonResult<Boolean> deletePromptWords(@RequestParam("id") Long id) {
        promptWordsService.deletePromptWords(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得提示词")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:prompt-words:query')")
    public CommonResult<PromptWordsRespVO> getPromptWords(@RequestParam("id") Long id) {
        PromptWordsDO promptWords = promptWordsService.getPromptWords(id);
        return success(BeanUtils.toBean(promptWords, PromptWordsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得提示词分页")
    @PreAuthorize("@ss.hasPermission('ai:prompt-words:query')")
    public CommonResult<PageResult<PromptWordsRespVO>> getPromptWordsPage(@Valid PromptWordsPageReqVO pageReqVO) {
        PageResult<PromptWordsDO> pageResult = promptWordsService.getPromptWordsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PromptWordsRespVO.class));
    }

    @Hidden
    @GetMapping("/export-excel")
    @Operation(summary = "导出提示词 Excel")
    @PreAuthorize("@ss.hasPermission('ai:prompt-words:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPromptWordsExcel(@Valid PromptWordsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PromptWordsDO> list = promptWordsService.getPromptWordsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "提示词.xls", "数据", PromptWordsRespVO.class,
                        BeanUtils.toBean(list, PromptWordsRespVO.class));
    }

}
package cn.iocoder.yudao.module.ai.controller.admin.image.config.vo;

import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 配置新增/修改 Request VO")
@Data
public class ImageConfigSaveReqVO {

    @Schema(description = "编号，唯一自增，修改时传值", example = "1")
    private Long id;

    @Schema(description = "绘画基础消耗（算力/张）")
    private Integer drawBaseConsume;

    @Schema(description = "脸部修复消耗（算力/张）")
    private Integer faceFixConsume;

    @Schema(description = "高清修复消耗（算力/张）")
    private Integer highFixConsume;

    @Schema(description = "高清修复-超清消耗（算力/张）")
    private Integer highFixSuperConsume;

    @Schema(description = "默认绘画数量（1-5）")
    private Integer defaultDrawNum;

    @Schema(description = "脸部修复默认状态")
    private Integer faceFixDefaultStatus;

    @Schema(description = "竖图宽度")
    private Integer portraitWidth;

    @Schema(description = "竖图高度")
    private Integer portraitHeight;

    @Schema(description = "方图宽度")
    private Integer squareWidth;

    @Schema(description = "方图高度")
    private Integer squareHeight;

    @Schema(description = "横图宽度")
    private Integer landscapeWidth;

    @Schema(description = "横图高度")
    private Integer landscapeHeight;

    @Schema(description = "封面")
    private String coverImage;

    /**
     * 名称
     */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyim")
    @ExcelProperty("名称")
    private String name;

    /**
     *  描述
     */
    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是描述")
    @ExcelProperty("描述")
    private String description;
}
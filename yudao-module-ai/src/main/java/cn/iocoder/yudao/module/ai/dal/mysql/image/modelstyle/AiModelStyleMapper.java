package cn.iocoder.yudao.module.ai.dal.mysql.image.modelstyle;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo.AiModelStylePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 风格模型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiModelStyleMapper extends BaseMapperX<AiModelStyleDO> {

    default PageResult<AiModelStyleDO> selectPage(AiModelStylePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiModelStyleDO>()
                .betweenIfPresent(AiModelStyleDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiModelStyleDO::getModelStyleDesc, reqVO.getModelStyleDesc())
                .eqIfPresent(AiModelStyleDO::getModelStyleId, reqVO.getModelStyleId())
                .eqIfPresent(AiModelStyleDO::getModelStyleImg, reqVO.getModelStyleImg())
                .likeIfPresent(AiModelStyleDO::getModelStyleName, reqVO.getModelStyleName())
                .eqIfPresent(AiModelStyleDO::getModelStyleUseCount, reqVO.getModelStyleUseCount())
                .eqIfPresent(AiModelStyleDO::getSort, reqVO.getSort())
                .eqIfPresent(AiModelStyleDO::getStatus, reqVO.getStatus())
                .orderByDesc(AiModelStyleDO::getId));
    }

}
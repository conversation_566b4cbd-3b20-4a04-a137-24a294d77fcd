package cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.invite.inviteconfig.vo.InviteConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteConfigDO;
import cn.iocoder.yudao.module.ai.service.invite.inviteconfig.InviteConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 邀请设置")
@RestController
@RequestMapping("/ai/invite-config")
@Validated
public class InviteConfigController {

    @Resource
    private InviteConfigService inviteConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建邀请设置")
    @PreAuthorize("@ss.hasPermission('ai:invite-config:create')")
    public CommonResult<Long> createInviteConfig(@Valid @RequestBody InviteConfigSaveReqVO createReqVO) {
        return success(inviteConfigService.createInviteConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新邀请设置")
    @PreAuthorize("@ss.hasPermission('ai:invite-config:update')")
    public CommonResult<Boolean> updateInviteConfig(@Valid @RequestBody InviteConfigSaveReqVO updateReqVO) {
        inviteConfigService.updateInviteConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除邀请设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:invite-config:delete')")
    public CommonResult<Boolean> deleteInviteConfig(@RequestParam("id") Long id) {
        inviteConfigService.deleteInviteConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得邀请设置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:invite-config:query')")
    public CommonResult<InviteConfigRespVO> getInviteConfig(@RequestParam("id") Long id) {
        AiInviteConfigDO inviteConfig = inviteConfigService.getInviteConfig(id);
        return success(BeanUtils.toBean(inviteConfig, InviteConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得邀请设置分页")
    @PreAuthorize("@ss.hasPermission('ai:invite-config:query')")
    public CommonResult<PageResult<InviteConfigRespVO>> getInviteConfigPage(@Valid InviteConfigPageReqVO pageReqVO) {
        PageResult<AiInviteConfigDO> pageResult = inviteConfigService.getInviteConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InviteConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出邀请设置 Excel")
    @PreAuthorize("@ss.hasPermission('ai:invite-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInviteConfigExcel(@Valid InviteConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiInviteConfigDO> list = inviteConfigService.getInviteConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "邀请设置.xls", "数据", InviteConfigRespVO.class,
                        BeanUtils.toBean(list, InviteConfigRespVO.class));
    }

}
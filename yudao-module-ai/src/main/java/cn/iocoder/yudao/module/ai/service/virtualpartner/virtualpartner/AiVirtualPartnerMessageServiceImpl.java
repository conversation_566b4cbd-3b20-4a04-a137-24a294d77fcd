package cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessageSendReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessageSendRespVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppVirtualPartnerMessagePraiseReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerMessageDO;
import cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.virtualpartner.AiVirtualPartnerMessageMapper;
import cn.iocoder.yudao.module.ai.dto.BaseResponse;
import cn.iocoder.yudao.module.ai.dto.MinMaxAllResponse;
import cn.iocoder.yudao.module.ai.dto.MinMaxResponse;
import cn.iocoder.yudao.module.ai.dto.Usage;
import cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.iocoder.yudao.module.ai.service.model.AiYyApiKeyService;
import cn.iocoder.yudao.module.ai.service.model.AiYyModelService;
import cn.iocoder.yudao.module.ai.service.sensitiveword.SensitiveWordService;
import cn.iocoder.yudao.module.ai.util.AiUtils;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.StreamingChatModel;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;

/**
 * AI 聊天消息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiVirtualPartnerMessageServiceImpl implements AiVirtualPartnerMessageService {

    @Resource
    private AiVirtualPartnerMessageMapper virtualPartnerMessageMapper;

    @Resource
    private AiVirtualPartnerConversationService virVirtualPartnerConversationService;

    @Resource
    private AiYyModelService modelService;

    @Resource
    private AiYyApiKeyService apiKeyService;

    @Resource
    private SensitiveWordService sensitiveWordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppAiVirtualPartnerMessageSendRespVO sendMessage(AppAiVirtualPartnerMessageSendReqVO sendReqVO, Long userId) {
        // 1.1 校验对话存在
        AiVirtualPartnerConversationDO conversation = virVirtualPartnerConversationService.validateVirtualPartnerConversationExists(sendReqVO.getConversationId());
        if (ObjUtil.notEqual(conversation.getUserId(), userId)) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        List<AiVirtualPartnerMessageDO> historyMessages = virtualPartnerMessageMapper.selectListByConversationId(conversation.getId());
        // 1.2 校验模型
        AiYyModelDO model = modelService.validateModel(conversation.getModelId());
        ChatModel chatModel = modelService.getChatModel(model.getKeyId());

        // 2. 插入 user 发送消息
        AiVirtualPartnerMessageDO userMessage = createAssistantMessage(conversation.getId(), null, model,
                userId, conversation.getRoleId(), MessageType.USER, sendReqVO.getContent(), sendReqVO.getUseContext(),null);

        // 3.1 插入 assistant 接收消息
        AiVirtualPartnerMessageDO assistantMessage = createAssistantMessage(conversation.getId(), userMessage.getId(), model,
                userId, conversation.getRoleId(), MessageType.ASSISTANT, "", sendReqVO.getUseContext(),null);

        // 3.2 创建 chat 需要的 Prompt
        Prompt prompt = buildPrompt(conversation, historyMessages, model, sendReqVO);
        ChatResponse chatResponse = chatModel.call(prompt);

        // 3.3段式返回
        log.info("[sendMessage][chatResponse({})]", JSONObject.toJSONString(chatResponse));
        Set<Map.Entry<String, Object>> metadataMap = chatResponse.getMetadata().entrySet();
        log.info("[sendMessage][metadataMap({})]", JSONObject.toJSONString(metadataMap));
        String newContent = chatResponse.getResult().getOutput().getText();
        virtualPartnerMessageMapper.updateById(new AiVirtualPartnerMessageDO().setId(assistantMessage.getId()).setContent(newContent));
        return new AppAiVirtualPartnerMessageSendRespVO().setSend(BeanUtils.toBean(userMessage, AppAiVirtualPartnerMessageSendRespVO.Message.class))
                .setReceive(BeanUtils.toBean(assistantMessage, AppAiVirtualPartnerMessageSendRespVO.Message.class).setContent(newContent));
    }

    @Override
    public Flux<CommonResult<AppAiVirtualPartnerMessageSendRespVO>> sendVirtualPartnerMessageStream(AppAiVirtualPartnerMessageSendReqVO sendReqVO, Long userId) {
        // 1.1 校验对话存在
        AiVirtualPartnerConversationDO conversation = virVirtualPartnerConversationService.validateVirtualPartnerConversationExists(sendReqVO.getConversationId());
        if (ObjUtil.notEqual(conversation.getUserId(), userId)) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        List<AiVirtualPartnerMessageDO> historyMessages = virtualPartnerMessageMapper.selectListByConversationId(conversation.getId());
        // 1.2 校验模型
        AiYyModelDO model = modelService.validateModel(conversation.getModelId());
        StreamingChatModel chatModel = modelService.getChatModel(model.getKeyId());
        //1.3 敏感词校验
        boolean isValid = sensitiveWordService.isTextValid(sendReqVO.getContent());
        if (!isValid){
            throw exception(SENSITIVE_WORD_IS_EXISTS_APP);
        }

        // 1.4 校验积分
        //AiVirtualPartnerRoleDO role = virtualPartnerRoleMapper.selectById(conversation.getRoleId());
        //checkUserCoin(userId, role.getCoin());

        // 2. 插入 user 发送消息
        AiVirtualPartnerMessageDO userMessage = createAssistantMessage(conversation.getId(), null, model,
                userId, conversation.getRoleId(), MessageType.USER, sendReqVO.getContent(), sendReqVO.getUseContext(), null);

        // 3.1 插入 assistant 接收消息
        /*AiVirtualPartnerMessageDO assistantMessage = createAssistantMessage(conversation.getId(), userMessage.getId(), model,
                userId, conversation.getRoleId(), MessageType.ASSISTANT, "", sendReqVO.getUseContext(),role.getCoin());*/
        AiVirtualPartnerMessageDO assistantMessage = createAssistantMessage(conversation.getId(), userMessage.getId(), model,
                userId, conversation.getRoleId(), MessageType.ASSISTANT, "", sendReqVO.getUseContext(),null);

        // 3.2 构建 Prompt，并进行调用
        Prompt prompt = buildPrompt(conversation, historyMessages, model, sendReqVO);
        Flux<ChatResponse> streamResponse = chatModel.stream(prompt);

        // 3.3 流式返回
        // TODO 注意：Schedulers.immediate() 目的是，避免默认 Schedulers.parallel() 并发消费 chunk 导致 SSE 响应前端会乱序问题
        StringBuffer contentBuffer = new StringBuffer();
        AtomicReference<Set<Map.Entry<String, Object>>> metadataMap = new AtomicReference<>();
        AtomicReference<MinMaxResponse> minMaxResponse = new AtomicReference<>(null);
        MinMaxAllResponse allResponse = new MinMaxAllResponse();
        return streamResponse.map(chunk -> {
                    //log.info("[sendChatMessageStream] 流处理中，chunk[getResult] = {}", JSONObject.toJSONString(chunk.getResult()));
                    //log.info("[sendChatMessageStream] 流处理中，chunk[getMetadata] = {}", JSONObject.toJSONString(chunk.getMetadata()));
                    metadataMap.set(chunk.getMetadata().entrySet());
                    //log.info("[sendChatMessageStream] 流处理中，chunk[metadataMap] = {}", JSONObject.toJSONString(metadataMap.get()));
                    MinMaxResponse getMinMaxResponse = getMinMaxResponse(metadataMap.get());
                    if (getMinMaxResponse.getLastChunk()){
                        allResponse.setChatResponseMetadata(chunk.getMetadata());
                        allResponse.setGeneration(chunk.getResult());
                        minMaxResponse.set(getMinMaxResponse);
                        log.info("[sendChatMessageStream] 流处理中，chunk[minMaxResponse] = {}", minMaxResponse.get());
                    }
                    String newContent = chunk.getResult() != null ? chunk.getResult().getOutput().getText() : null;
                    newContent = StrUtil.nullToDefault(newContent, ""); // 避免 null 的情况
                    contentBuffer.append(newContent);

                    // 响应结果
                    return success(new AppAiVirtualPartnerMessageSendRespVO().setSend(BeanUtils.toBean(userMessage, AppAiVirtualPartnerMessageSendRespVO.Message.class))
                            .setReceive(BeanUtils.toBean(assistantMessage, AppAiVirtualPartnerMessageSendRespVO.Message.class).setContent(newContent)));
                })
                .doOnComplete(() -> {
                    // 流处理完成后，保存已响应的数据到数据库
                    log.info("[sendChatMessageStream] 流处理完成，准备保存数据");
                    // 异步保存数据
                    if(model.getModel().equals(AiPlatformEnum.MINI_MAX.getPlatform())){

                    }
                    saveStreamedMessageToDatabase(assistantMessage, contentBuffer.toString(), true,minMaxResponse.get(),allResponse,prompt);
                })
                .doOnError(throwable -> {
                    log.error("[sendChatMessageStream] 发生异常: ", throwable);
                    // 发生错误时保存异常消息
                    saveStreamedMessageToDatabase(assistantMessage, throwable.getMessage(), false,minMaxResponse.get(),allResponse,prompt);
                })
                .doOnCancel(() -> {
                    // 用户断开时保存流数据
                    log.warn("[sendChatMessageStream] 用户({}) 中途断开连接, 停止处理", userId);
                    saveStreamedMessageToDatabase(assistantMessage, contentBuffer.toString(), true,minMaxResponse.get(),allResponse,prompt);
                })
                .onErrorResume(error -> Flux.just(error(ErrorCodeConstants.CHAT_STREAM_ERROR)));
    }

    private void saveStreamedMessageToDatabase(AiVirtualPartnerMessageDO assistantMessage, String content, Boolean success,MinMaxResponse minMaxResponse,MinMaxAllResponse allResponse, Prompt prompt) {
        // 使用异步线程池执行数据库保存，避免阻塞流的处理
        TenantUtils.executeIgnore(() -> {
            // 更新数据库，避免重复保存
            AiVirtualPartnerMessageDO updateMessage = new AiVirtualPartnerMessageDO()
                    .setId(assistantMessage.getId())
                    .setContent(content)
                    .setResponseParams(JSONObject.toJSONString(allResponse))
                    .setRequestParams(JSONObject.toJSONString(prompt))
                    .setInputSensitive(minMaxResponse.getInputSensitive())
                    .setOutputSensitive(minMaxResponse.getOutputSensitive())
                    .setInputSensitiveType(minMaxResponse.getInputSensitiveType())
                    .setOutputSensitiveType(minMaxResponse.getOutputSensitiveType());

            // 进行数据库更新操作
            virtualPartnerMessageMapper.updateById(updateMessage);
            // success 为 true 表示流处理成功，否则为异常情况 扣减用户算力
            if (success) {
                //payMentReduceCoin(assistantMessage.getUserId(), assistantMessage.getAiCoin(),"虚拟陪伴", assistantMessage.getId());
            }
        });
    }

    private Prompt buildPrompt(AiVirtualPartnerConversationDO conversation, List<AiVirtualPartnerMessageDO> messages,
                               AiYyModelDO model, AppAiVirtualPartnerMessageSendReqVO sendReqVO) {
        // 1. 构建 Prompt Message 列表
        List<Message> chatMessages = new ArrayList<>();

        // 1.2 system context 角色设定
        if (StrUtil.isNotBlank(conversation.getSystemMessage())) {
            chatMessages.add(new SystemMessage(conversation.getSystemMessage()));
        }
        // 1.3 history message 历史消息
        List<AiVirtualPartnerMessageDO> contextMessages = filterContextMessages(messages, conversation, sendReqVO);
        contextMessages.forEach(message -> chatMessages.add(AiUtils.buildMessage(message.getType(), message.getContent())));
        // 1.4 user message 新发送消息
        chatMessages.add(new UserMessage(sendReqVO.getContent()));

        // 2. 构建 ChatOptions 对象
        AiPlatformEnum platform = AiPlatformEnum.validatePlatform(model.getPlatform());
        ChatOptions chatOptions = AiUtils.buildChatOptions(platform, model.getModel(),
                conversation.getTemperature(), conversation.getMaxTokens(), conversation.getTopP(),null,null);
        return new Prompt(chatMessages, chatOptions);
    }

    /**
     * 从历史消息中，获得倒序的 n 组消息作为消息上下文
     * <p>
     * n 组：指的是 user + assistant 形成一组
     *
     * @param messages     消息列表
     * @param conversation 对话
     * @param sendReqVO    发送请求
     * @return 消息上下文
     */
    private List<AiVirtualPartnerMessageDO> filterContextMessages(List<AiVirtualPartnerMessageDO> messages,
                                                             AiVirtualPartnerConversationDO conversation,
                                                             AppAiVirtualPartnerMessageSendReqVO sendReqVO) {
        if (conversation.getMaxContexts() == null || ObjUtil.notEqual(sendReqVO.getUseContext(), Boolean.TRUE)) {
            return Collections.emptyList();
        }
        List<AiVirtualPartnerMessageDO> contextMessages = new ArrayList<>(conversation.getMaxContexts() * 2);
        for (int i = messages.size() - 1; i >= 0; i--) {
            AiVirtualPartnerMessageDO virtualPartnerMessage = CollUtil.get(messages, i);
            if (virtualPartnerMessage == null || virtualPartnerMessage.getReplyId() == null) {
                continue;
            }
            AiVirtualPartnerMessageDO userMessage = CollUtil.get(messages, i - 1);
            if (userMessage == null || ObjUtil.notEqual(virtualPartnerMessage.getReplyId(), userMessage.getId())
                    || StrUtil.isEmpty(virtualPartnerMessage.getContent())) {
                continue;
            }
            // 由于后续要 reverse 反转，所以先添加 assistantMessage
            contextMessages.add(virtualPartnerMessage);
            contextMessages.add(userMessage);
            // 超过最大上下文，结束
            if (contextMessages.size() >= conversation.getMaxContexts() * 2) {
                break;
            }
        }
        Collections.reverse(contextMessages);
        return contextMessages;
    }

    private AiVirtualPartnerMessageDO createAssistantMessage(Long conversationId, Long replyId,
                                                        AiYyModelDO model, Long userId, Long roleId,
                                              MessageType messageType, String content, Boolean useContext, Integer coin) {
        AiVirtualPartnerMessageDO message = new AiVirtualPartnerMessageDO().setConversationId(conversationId).setReplyId(replyId)
                .setModel(model.getModel()).setModelId(model.getId()).setUserId(userId)
                .setType(messageType.getValue()).setContent(content).setUseContext(useContext)
                .setAiCoin(coin);
        message.setCreateTime(LocalDateTime.now());
        virtualPartnerMessageMapper.insert(message);
        return message;
    }

    private MinMaxResponse getMinMaxResponse(Set<Map.Entry<String, Object>> metadataMap) {
        MinMaxResponse minMaxResponse = new MinMaxResponse();
        Usage usage = new Usage();
        BaseResponse baseResponse = new BaseResponse();

        // 使用 Map 来简化字段映射
        Map<String, Consumer<Object>> fieldProcessors = new HashMap<>();

        // 填充字段处理映射
        fieldProcessors.put("inputSensitive", value -> minMaxResponse.setInputSensitive(parseBoolean(value)));
        fieldProcessors.put("outputSensitive", value -> minMaxResponse.setOutputSensitive(parseBoolean(value)));
        fieldProcessors.put("inputSensitiveType", value -> minMaxResponse.setInputSensitiveType(parseInteger(value)));
        fieldProcessors.put("outputSensitiveType", value -> minMaxResponse.setOutputSensitiveType(parseInteger(value)));
        fieldProcessors.put("statusCode", value -> baseResponse.setStatusCode(Long.valueOf(Objects.toString(value, "0"))));
        fieldProcessors.put("message", value -> baseResponse.setMessage(Objects.toString(value, "")));
        fieldProcessors.put("promptTokens", value -> usage.setPromptTokens(parseInteger(value)));
        fieldProcessors.put("totalTokens", value -> usage.setTotalTokens(parseInteger(value)));
        fieldProcessors.put("totalCharacters", value -> usage.setTotalCharacters(parseInteger(value)));
        fieldProcessors.put("totalAsrTime", value -> usage.setTotalAsrTime(parseInteger(value)));

        // 遍历 metadataMap，处理字段
        for (Map.Entry<String, Object> entry : metadataMap) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 如果存在字段映射，则调用对应的处理方法
            Consumer<Object> processor = fieldProcessors.get(key);
            if (processor != null) {
                processor.accept(value);
            }
        }
        if (baseResponse.getStatusCode() != null && baseResponse.getStatusCode() == -1L) {
           minMaxResponse.setLastChunk(false);
        }
        // 设置 Usage 和 BaseResponse
        minMaxResponse.setUsage(usage);
        minMaxResponse.setBaseResponse(baseResponse);

        return minMaxResponse;
    }

    // 辅助方法：解析布尔值
    private boolean parseBoolean(Object value) {
        return Boolean.parseBoolean(Objects.toString(value, "false"));
    }

    // 辅助方法：解析整型值
    private int parseInteger(Object value) {
        try {
            return Integer.parseInt(Objects.toString(value, "0"));
        } catch (NumberFormatException e) {
            log.warn("Failed to parse integer for value: {}", value);
            return 0; // 返回默认值
        }
    }


    @Override
    public List<AiVirtualPartnerMessageDO> getVirtualPartnerMessageListByConversationId(Long conversationId) {
        return virtualPartnerMessageMapper.selectListByConversationId(conversationId);
    }

    @Override
    public void deleteVirtualPartnerMessage(Long id, Long userId) {
        // 1. 校验消息存在
        AiVirtualPartnerMessageDO message = virtualPartnerMessageMapper.selectById(id);
        if (message == null || ObjUtil.notEqual(message.getUserId(), userId)) {
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }
        // 2. 执行删除
        virtualPartnerMessageMapper.deleteById(id);
    }

    @Override
    public void deleteVirtualPartnerMessageByConversationId(Long conversationId, Long userId) {
        // 1. 校验消息存在
        List<AiVirtualPartnerMessageDO> messages = virtualPartnerMessageMapper.selectListByConversationId(conversationId);
        if (CollUtil.isEmpty(messages) || ObjUtil.notEqual(messages.get(0).getUserId(), userId)) {
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }
        // 2. 执行删除
        virtualPartnerMessageMapper.deleteBatchIds(convertList(messages, AiVirtualPartnerMessageDO::getId));
    }

    @Override
    public void deleteVirtualPartnerMessageByAdmin(Long id) {
        // 1. 校验消息存在
        AiVirtualPartnerMessageDO message = virtualPartnerMessageMapper.selectById(id);
        if (message == null) {
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }
        // 2. 执行删除
        virtualPartnerMessageMapper.deleteById(id);
    }

    @Override
    public Map<Long, Integer> getVirtualPartnerMessageCountMap(Collection<Long> conversationIds) {
        return virtualPartnerMessageMapper.selectCountMapByConversationId(conversationIds);
    }

    @Override
    public PageResult<AiVirtualPartnerMessageDO> getVirtualPartnerMessagePage(AppAiVirtualPartnerMessagePageReqVO pageReqVO) {
        return virtualPartnerMessageMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AiVirtualPartnerMessageDO> getVirtualPartnerMessagePageByConversationId(AppAiVirtualPartnerMessagePageReqVO pageReqVO) {
        return virtualPartnerMessageMapper.getVirtualPartnerMessagePageByConversationId(pageReqVO);
    }

    @Override
    public void praiseVirtualPartnerMessage(AppVirtualPartnerMessagePraiseReqVO reqVO) {
        validateVirtualPartnerMessageExists(reqVO.getId());
        // 更新
        AiVirtualPartnerMessageDO updateObj = BeanUtils.toBean(reqVO, AiVirtualPartnerMessageDO.class);
        virtualPartnerMessageMapper.updateById(updateObj);
    }

    /**
     * 校验消息是否存在
     * @param id
     * @return
     */
    private AiVirtualPartnerMessageDO validateVirtualPartnerMessageExists(Long id) {
        AiVirtualPartnerMessageDO assistantMessage = virtualPartnerMessageMapper.selectById(id);
        if (assistantMessage == null) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        return assistantMessage;
    }

    /**
     * 校验消息是否是当前用户的并且是否存在
     * @param id
     * @return
     */
    @Override
    public AiVirtualPartnerMessageDO validateVirtualPartnerMessageExists(Long id, Long userId) {
        AiVirtualPartnerMessageDO assistantMessage = virtualPartnerMessageMapper.selectById(id,userId);
        if (assistantMessage == null) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        return assistantMessage;
    }

}

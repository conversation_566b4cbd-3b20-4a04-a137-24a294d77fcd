package cn.iocoder.yudao.module.ai.dal.mysql.gallery;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryConfigDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 画廊配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GalleryConfigMapper extends BaseMapperX<GalleryConfigDO> {

    default GalleryConfigDO selectDefaultConfig(){
        // 构建查询条件，按id降序排列并限制只查询一条记录
        LambdaQueryWrapper<GalleryConfigDO> lambdaQuery =  new LambdaQueryWrapper<>();
        lambdaQuery.orderByDesc(GalleryConfigDO::getId).last("LIMIT 1");
        // 执行查询
        return selectOne(lambdaQuery);
    }

    default PageResult<GalleryConfigDO> selectPage(GalleryConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GalleryConfigDO>()
                .betweenIfPresent(GalleryConfigDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(GalleryConfigDO::getUseCoin, reqVO.getUseCoin())
                .orderByDesc(GalleryConfigDO::getId));
    }

}
package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategoryPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategoryRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategorySaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteCategoryDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.category.ContentWriteCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI内容创作分类")
@RestController
@RequestMapping("/ai/content-write-category")
@Validated
public class ContentWriteCategoryController {

    @Resource
    private ContentWriteCategoryService contentWriteCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建AI 内容创作分类")
    @PreAuthorize("@ss.hasPermission('ai:content-write-category:create')")
    public CommonResult<Long> createContentWriteCategory(@Valid @RequestBody ContentWriteCategorySaveReqVO createReqVO) {
        return success(contentWriteCategoryService.createContentWriteCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI 内容创作分类")
    @PreAuthorize("@ss.hasPermission('ai:content-write-category:update')")
    public CommonResult<Boolean> updateContentWriteCategory(@Valid @RequestBody ContentWriteCategorySaveReqVO updateReqVO) {
        contentWriteCategoryService.updateContentWriteCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 内容创作分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:content-write-category:delete')")
    public CommonResult<Boolean> deleteContentWriteCategory(@RequestParam("id") Long id) {
        contentWriteCategoryService.deleteContentWriteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI 内容创作分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:content-write-category:query')")
    public CommonResult<ContentWriteCategoryRespVO> getContentWriteCategory(@RequestParam("id") Long id) {
        AiContentWriteCategoryDO contentWriteCategory = contentWriteCategoryService.getContentWriteCategory(id);
        return success(BeanUtils.toBean(contentWriteCategory, ContentWriteCategoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI 内容创作分类分页")
    @PreAuthorize("@ss.hasPermission('ai:content-write-category:query')")
    public CommonResult<PageResult<ContentWriteCategoryRespVO>> getContentWriteCategoryPage(@Valid ContentWriteCategoryPageReqVO pageReqVO) {
        PageResult<AiContentWriteCategoryDO> pageResult = contentWriteCategoryService.getContentWriteCategoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContentWriteCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出AI 内容创作分类 Excel")
    @PreAuthorize("@ss.hasPermission('ai:content-write-category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContentWriteCategoryExcel(@Valid ContentWriteCategoryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiContentWriteCategoryDO> list = contentWriteCategoryService.getContentWriteCategoryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI 内容创作分类.xls", "数据", ContentWriteCategoryRespVO.class,
                        BeanUtils.toBean(list, ContentWriteCategoryRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得内容创作分类列表")
    @Parameter(name = "status", description = "状态", required = true, example = "1")
    public CommonResult<List<ContentWriteCategoryRespVO>> getContentWriteCategorySimpleList(@RequestParam("status") Integer status) {
        List<AiContentWriteCategoryDO> list = contentWriteCategoryService.getContentWriteCategoryListByStatus(status);
        return success(BeanUtils.toBean(list, ContentWriteCategoryRespVO.class));
    }
}
package cn.iocoder.yudao.module.ai.controller.app.image;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppAiImageLabRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.DrawImageConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.promptwords.AppPromptWordsRespVO;
import cn.iocoder.yudao.module.ai.enums.promptwords.PromptWordsTypeEnum;
import cn.iocoder.yudao.module.ai.service.image.config.DrawImageConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @Description: 获取绘画配置信息
 * @Date: 2024/7/28 20:10
 * @Author: zhangq
 * @Version: 1.0
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping( "/ai/image-config")
@Tag(name = "用户 APP - 获取绘画配置信息")
public class AppDrawConfigController {


    @Resource
    private DrawImageConfigService drawImageConfigService;

    /**
     * 获取绘画配置信息
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获取绘画配置信息")
    public CommonResult<DrawImageConfigRespVO>  getDrawImageConfig() {
        DrawImageConfigRespVO respVO = drawImageConfigService.getDrawImageConfig();
        return success(respVO);
    }

    /**
     * 获取灵感列表-提示词
     */
    @GetMapping("/get-inspiration")
    @Operation(summary = "绘画-获取灵感列表")
    public CommonResult<AppPromptWordsRespVO>  getImageInspiration() {
        AppPromptWordsRespVO respVO = drawImageConfigService.getInspiration(getLoginUserId(), PromptWordsTypeEnum.IMAGE_PROMPT_WORDS.getType());
        return success(respVO);
    }

    /**
     * 获取绘画质量-提示词
     */
    @GetMapping("/get-quality")
    @Operation(summary = "绘画-获取绘画质量")
    public CommonResult<List<AppPromptWordsRespVO>>  getImageQuality() {
        List<AppPromptWordsRespVO> respVOS = drawImageConfigService.getQuality(getLoginUserId(),PromptWordsTypeEnum.IMAGE_QUALITY_PROMPT_WORDS.getType());
        return success(respVOS);
    }

    /**
     * 获取ai实验室 列表
     */
    @GetMapping("/get-lab")
    @Operation(summary = "绘画-获取ai实验室列表")
    public CommonResult<List<AppAiImageLabRespVO>>  getAiImageLab() {
        List<AppAiImageLabRespVO> respVOS = drawImageConfigService.getAiImageLab();
        return success(respVOS);
    }
}

package cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation;

import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConversationDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.conversation.ContentWriteConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - AI内容写作对话")
@RestController
@RequestMapping("/ai/contentwrite/conversation")
@Validated
public class AppContentWriteConversationController {

    @Resource
    private ContentWriteConversationService contentWriteConversationService;

    @PostMapping("/create-my")
    @Operation(summary = "创建【我的】聊天对话")
    public CommonResult<Long> createVirtualPartnerConversationMy(@RequestBody @Valid AppContentWriteConversationCreateMyReqVO createReqVO) {
        return success(contentWriteConversationService.createContentWriteConversationMy(createReqVO, getLoginUserId()));
    }

    @GetMapping("/my-list")
    @Operation(summary = "获得【我的】聊天对话列表-按时间倒序排")
    public CommonResult<PageResult<AppContentWriteConversationRespVO>> getContentWriteConversationMyList(@Valid AppContentWriteConversationPageReqVO pageReqVO) {
        PageResult<AiContentWriteConversationDO> pageResult = contentWriteConversationService.getContentWriteConversationAppListByUserId(getLoginUserId(),pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppContentWriteConversationRespVO.class));
    }

    @GetMapping("/get-my")
    @Operation(summary = "获得【我的】聊天对话,最新一条")
    public CommonResult<AppContentWriteConversationRespVO> getContentWriteConversationMy() {
        AiContentWriteConversationDO conversation = contentWriteConversationService.getContentWriteConversationById(getLoginUserId());
        if (conversation != null && ObjUtil.notEqual(conversation.getUserId(), getLoginUserId())) {
            conversation = null;
        }
        return success(BeanUtils.toBean(conversation, AppContentWriteConversationRespVO.class));
    }

}
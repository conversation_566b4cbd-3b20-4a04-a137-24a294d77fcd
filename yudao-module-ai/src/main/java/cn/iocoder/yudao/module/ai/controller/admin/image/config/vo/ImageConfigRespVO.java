package cn.iocoder.yudao.module.ai.controller.admin.image.config.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImageConfigRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "6148")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "绘画基础消耗（算力/张）")
    @ExcelProperty("绘画基础消耗（算力/张）")
    private Integer drawBaseConsume;

    @Schema(description = "脸部修复消耗（算力/张）")
    @ExcelProperty("脸部修复消耗（算力/张）")
    private Integer faceFixConsume;

    @Schema(description = "高清修复消耗（算力/张）")
    @ExcelProperty("高清修复消耗（算力/张）")
    private Integer highFixConsume;

    @Schema(description = "高清修复-超清消耗（算力/张）")
    @ExcelProperty("高清修复-超清消耗（算力/张）")
    private Integer highFixSuperConsume;

    @Schema(description = "默认绘画数量（1-5）")
    @ExcelProperty("默认绘画数量（1-5）")
    private Integer defaultDrawNum;

    @Schema(description = "脸部修复默认状态")
    @ExcelProperty("脸部修复默认状态")
    private Integer faceFixDefaultStatus;

    @Schema(description = "竖图宽度")
    @ExcelProperty("竖图宽度")
    private Integer portraitWidth;

    @Schema(description = "竖图高度")
    @ExcelProperty("竖图高度")
    private Integer portraitHeight;

    @Schema(description = "方图宽度")
    @ExcelProperty("方图宽度")
    private Integer squareWidth;

    @Schema(description = "方图高度")
    @ExcelProperty("方图高度")
    private Integer squareHeight;

    @Schema(description = "横图宽度")
    @ExcelProperty("横图宽度")
    private Integer landscapeWidth;

    @Schema(description = "横图高度")
    @ExcelProperty("横图高度")
    private Integer landscapeHeight;

    @Schema(description = "封面")
    @ExcelProperty("封面")
    private String coverImage;

    /**
     * 名称
     */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyim")
    @ExcelProperty("名称")
    private String name;

    /**
     *  描述
     */
    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是描述")
    @ExcelProperty("描述")
    private String description;
}
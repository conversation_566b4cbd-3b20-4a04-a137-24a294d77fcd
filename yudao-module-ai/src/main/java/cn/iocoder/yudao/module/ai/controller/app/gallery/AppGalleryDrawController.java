package cn.iocoder.yudao.module.ai.controller.app.gallery;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryCommonDO;
import cn.iocoder.yudao.module.ai.service.gallery.GalleryCommonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户APP - AI 绘画广场")
@RestController
@RequestMapping("/ai/gallery-draw")
@Validated
public class AppGalleryDrawController {

    @Resource
    private GalleryCommonService galleryCommonService;

    @GetMapping("/get")
    @Operation(summary = "用户APP-根据ID获得绘画广场详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    public CommonResult<AppGalleryCommonRespVO> getGalleryCommonById(@RequestParam("id") Long id) {
        AppGalleryCommonRespVO galleryCommon = galleryCommonService.getAppGalleryCommon(getLoginUserId(),id);
        return success(BeanUtils.toBean(galleryCommon, AppGalleryCommonRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "用户APP 绘画广场分页")
    public CommonResult<PageResult<AppGalleryCommonRespVO>> getAppGalleryCommonPage(@Valid AppGalleryCommonPageReqVO pageReqVO) {
        PageResult<GalleryCommonDO> pageResult = galleryCommonService.getAppGalleryCommonPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppGalleryCommonRespVO.class));
    }

    /**
     * 画同款
     */
    @PostMapping("/draw-same")
    @Operation(summary = "用户APP-画同款")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    public CommonResult<Long> drawSame(@RequestParam("id") Long id) {
        Long taskId = galleryCommonService.drawSame(getLoginUserId(),id);
        return success(taskId);
    }

}
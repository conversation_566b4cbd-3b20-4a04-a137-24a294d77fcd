package cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.audioconfig;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 语音配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AudioConfigMapper extends BaseMapperX<AiAudioConfigDO> {

    default PageResult<AiAudioConfigDO> selectPage(AudioConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiAudioConfigDO>()
                .betweenIfPresent(AiAudioConfigDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiAudioConfigDO::getAudioFile, reqVO.getAudioFile())
                .eqIfPresent(AiAudioConfigDO::getAudioLength, reqVO.getAudioLength())
                .eqIfPresent(AiAudioConfigDO::getAudioSampleRate, reqVO.getAudioSampleRate())
                .eqIfPresent(AiAudioConfigDO::getAudioSize, reqVO.getAudioSize())
                .eqIfPresent(AiAudioConfigDO::getBitrate, reqVO.getBitrate())
                .eqIfPresent(AiAudioConfigDO::getContent, reqVO.getContent())
                .eqIfPresent(AiAudioConfigDO::getErrorMessage, reqVO.getErrorMessage())
                .betweenIfPresent(AiAudioConfigDO::getFinishTime, reqVO.getFinishTime())
                .eqIfPresent(AiAudioConfigDO::getInvisibleCharacterRatio, reqVO.getInvisibleCharacterRatio())
                .eqIfPresent(AiAudioConfigDO::getModel, reqVO.getModel())
                .eqIfPresent(AiAudioConfigDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AiAudioConfigDO::getOutputFormat, reqVO.getOutputFormat())
                .eqIfPresent(AiAudioConfigDO::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AiAudioConfigDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AiAudioConfigDO::getSubtitleFile, reqVO.getSubtitleFile())
                .eqIfPresent(AiAudioConfigDO::getUsageCharacters, reqVO.getUsageCharacters())
                .eqIfPresent(AiAudioConfigDO::getVoiceId, reqVO.getVoiceId())
                .eqIfPresent(AiAudioConfigDO::getVoiceList, reqVO.getVoiceList())
                .likeIfPresent(AiAudioConfigDO::getVoiceName, reqVO.getVoiceName())
                .eqIfPresent(AiAudioConfigDO::getWordCount, reqVO.getWordCount())
                .eqIfPresent(AiAudioConfigDO::getEnable, reqVO.getEnable())
                .orderByDesc(AiAudioConfigDO::getId));
    }

    default List<AiAudioConfigDO> selectList(Integer status) {
        return selectList(new LambdaQueryWrapperX<AiAudioConfigDO>()
                .eq(AiAudioConfigDO::getStatus, status)
                .orderByAsc(AiAudioConfigDO::getId));
    }
}
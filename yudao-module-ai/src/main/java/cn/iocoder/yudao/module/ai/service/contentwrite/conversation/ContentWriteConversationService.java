package cn.iocoder.yudao.module.ai.service.contentwrite.conversation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo.ContentWriteConversationSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.conversation.vo.AppContentWriteConversationPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConversationDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * AI 内容写作 对话 DO Service 接口
 *
 * <AUTHOR>
 */
public interface ContentWriteConversationService {

    /**
     * 创建AI 内容写作 对话 DO
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContentWriteConversation(@Valid ContentWriteConversationSaveReqVO createReqVO);

    /**
     * 更新AI 内容写作 对话 DO
     *
     * @param updateReqVO 更新信息
     */
    void updateContentWriteConversation(@Valid ContentWriteConversationSaveReqVO updateReqVO);

    /**
     * 删除AI 内容写作 对话 DO
     *
     * @param id 编号
     */
    void deleteContentWriteConversation(Long id);

    /**
     * 获得AI 内容写作 对话 DO
     *
     * @param id 编号
     * @return AI 内容写作 对话 DO
     */
    AiContentWriteConversationDO getContentWriteConversation(Long id);

    /**
     * 获得AI 内容写作 对话 DO分页
     *
     * @param pageReqVO 分页查询
     * @return AI 内容写作 对话 DO分页
     */
    PageResult<AiContentWriteConversationDO> getContentWriteConversationPage(ContentWriteConversationPageReqVO pageReqVO);

    /**
     * 创建【我的】聊天对话
     * @param createReqVO
     * @param userId
     * @return
     */
    Long createContentWriteConversationMy(@Valid AppContentWriteConversationCreateMyReqVO createReqVO, Long userId);

    /**
     * 获得【我的】聊天对话列表-按时间倒序排-取最新的前30条保证速度
     * @param userId
     * @param pageReqVO
     * @return
     */
    PageResult<AiContentWriteConversationDO> getContentWriteConversationAppListByUserId(Long userId, AppContentWriteConversationPageReqVO pageReqVO);

    /**
     * 获得【我的】聊天对话,最新一条
     * @param userId
     * @return
     */
    AiContentWriteConversationDO getContentWriteConversationById(Long userId);

    /**
     * 校验【我的】聊天对话
     * @param conversationId
     * @return
     */
    AiContentWriteConversationDO validateContentWriteConversationExists(@NotNull(message = "聊天对话编号不能为空") Long conversationId);
}
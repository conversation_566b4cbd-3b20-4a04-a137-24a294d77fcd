package cn.iocoder.yudao.module.ai.service.config.base;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.config.base.vo.BaseInfoConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.BaseInfoConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.base.BaseInfoConfigMapper;
import cn.iocoder.yudao.module.pay.api.coin.MemberCoinApi;
import cn.iocoder.yudao.module.pay.api.coin.dto.MemberCoinRespDTO;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.BASE_INFO_CONFIG_NOT_EXISTS;

/**
 * 基础配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BaseInfoConfigServiceImpl implements BaseInfoConfigService {

    @Resource
    private BaseInfoConfigMapper baseInfoConfigMapper;
    @Resource
    private MemberCoinApi memberCoinApi;

    @Override
    public Long saveOrUpdateBaseInfoConfig(BaseInfoConfigSaveReqVO createReqVO) {
        // 插入
        BaseInfoConfigDO baseInfoConfig = BeanUtils.toBean(createReqVO, BaseInfoConfigDO.class);
        if (null == baseInfoConfig.getId() || baseInfoConfig.getId() == 0){
            baseInfoConfigMapper.insert(baseInfoConfig);
        }else{
            // 校验存在
            validateBaseInfoConfigExists(baseInfoConfig.getId());
            baseInfoConfigMapper.updateById(baseInfoConfig);
        }
        // 返回
        return baseInfoConfig.getId();
    }

    private void validateBaseInfoConfigExists(Long id) {
        if (baseInfoConfigMapper.selectById(id) == null) {
            throw exception(BASE_INFO_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public BaseInfoConfigDO getSimpleBaseInfoConfig() {
        List<BaseInfoConfigDO> list = baseInfoConfigMapper.selectList();
        return CollectionUtils.getFirst(list);
    }

    /**
     * 新用户注册赠送算力
     * @param userId 用户编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendCoinByRegister(Long userId) {
        BaseInfoConfigDO configDO = getSimpleBaseInfoConfig();
        if (null != configDO){
            // 查询已赠送算力用户
            List<MemberCoinRespDTO> coinRespDTO = memberCoinApi.getCoinTransactionByUserIdAndBizType(userId, PayCoinBizTypeEnum.REGISTER_GIFT);
            if (CollUtil.isEmpty(coinRespDTO)) {
                // 赠送算力 这里失败也需要重试
                memberCoinApi.sendCoinByRegister(userId, configDO.getRegisterGive(),PayCoinBizTypeEnum.REGISTER_GIFT,PayCoinBizTypeEnum.REGISTER_GIFT.name());
            }
        }else{//这里需要优化 TODO 如果失败需要重试走 mq
            throw new RuntimeException("基础配置未配置-赠送算力失败,会员ID:"+userId);
        }
    }

}
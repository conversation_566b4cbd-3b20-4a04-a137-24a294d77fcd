package cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner;

import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AppAiVirtualpartnerConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * AI 虚拟陪伴对话 Service 接口
 *
 * <AUTHOR>
 */
public interface AiVirtualPartnerConversationService {

    /**
     * 创建【我的】聊天对话
     *
     * @param createReqVO 创建信息
     * @param userId 用户编号
     * @return 编号
     */
    Long createVirtualpartnerConversationMy(@Valid AppAiVirtualpartnerConversationCreateMyReqVO createReqVO, Long userId);

    /**
     * 获得【我的】聊天对话列表-APP
     * @param loginUserId
     * @return
     */
    List<AiVirtualPartnerConversationDO> getVirtualPartnerConversationAppListByUserId(Long loginUserId);

    /**
     * 获得【我的】聊天对话,最新一条
     * @param loginUserId
     * @return
     */
    AiVirtualPartnerConversationDO getVirtualPartnerConversationById(Long loginUserId);

    /**
     * 获得【我的】聊天对话
     * @param conversationId
     * @return
     */
    AiVirtualPartnerConversationDO getVirtualPartnerConversation(Long conversationId);

    /**
     * 校验【我的】聊天对话是否存在
     * @param conversationId
     * @return
     */
    AiVirtualPartnerConversationDO validateVirtualPartnerConversationExists(@NotNull(message = "聊天对话编号不能为空") Long conversationId);
}

package cn.iocoder.yudao.module.ai.service.virtualpartner.audiomodel;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audiomodel.vo.AudioModelSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioModelDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 音色模型 Service 接口
 *
 * <AUTHOR>
 */
public interface AudioModelService {

    /**
     * 创建音色模型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAudioModel(@Valid AudioModelSaveReqVO createReqVO);

    /**
     * 更新音色模型
     *
     * @param updateReqVO 更新信息
     */
    void updateAudioModel(@Valid AudioModelSaveReqVO updateReqVO);

    /**
     * 删除音色模型
     *
     * @param id 编号
     */
    void deleteAudioModel(Long id);

    /**
     * 获得音色模型
     *
     * @param id 编号
     * @return 音色模型
     */
    AiAudioModelDO getAudioModel(Long id);

    /**
     * 获得音色模型分页
     *
     * @param pageReqVO 分页查询
     * @return 音色模型分页
     */
    PageResult<AiAudioModelDO> getAudioModelPage(AudioModelPageReqVO pageReqVO);

    /**
     * 获得音色模型列表
     * @param status
     * @return
     */
    List<AiAudioModelDO> getAudioModelListByStatus(Integer status);
}
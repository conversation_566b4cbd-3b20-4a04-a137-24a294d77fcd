package cn.iocoder.yudao.module.ai.controller.app.contentwrite.category.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户APP - AI 内容创作分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppContentWriteCategoryRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "29933")
    private Long id;

    @Schema(description = "分类名称", example = "AI")
    private String name;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "是否推荐 0默认 1推荐", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer recommend;

}
package cn.iocoder.yudao.module.ai.util;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigSaveReqVO;
import com.minimaxi.platform.t2a.api.MinMaxT2AApi;
import com.minimaxi.platform.t2a.api.MiniMaxT2AOptions;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * @Date: 2024/7/27 01:21
 * @Author: zhangq
 * @Version: 1.0
 */
public class MiniMaxT2AUtils {

    public static MiniMaxT2AOptions buildMiniMaxT2AOptions(AudioConfigSaveReqVO reqVO, boolean isStream) {
        List<MinMaxT2AApi.TimberWeights> timberWeights = null;
        if (!CollectionUtils.isAnyEmpty(reqVO.getVoiceList())){
            timberWeights = new ArrayList<>();
            for (Map<String,String> map : reqVO.getVoiceList()){
                MinMaxT2AApi.TimberWeights timberWeight = new MinMaxT2AApi.TimberWeights(map.get("voiceId"),Integer.parseInt(map.get("weight")));
                timberWeights.add(timberWeight);
            }
        }
        MiniMaxT2AOptions option = MiniMaxT2AOptions.builder().withModel(reqVO.getModel())
                .withAudioSetting(new MinMaxT2AApi.AudioSettings(32000, 128000, reqVO.getOutputFormat(),1))
                .withStream(isStream)
                .withText(reqVO.getContent())
                .withVoiceSetting(new MinMaxT2AApi.VoiceSetting(1.0f,1.0f,0,null != reqVO.getVoiceId()? reqVO.getVoiceId(): ""))
                .withTimberWeights(timberWeights)
                .build();
        return option;
    }
}

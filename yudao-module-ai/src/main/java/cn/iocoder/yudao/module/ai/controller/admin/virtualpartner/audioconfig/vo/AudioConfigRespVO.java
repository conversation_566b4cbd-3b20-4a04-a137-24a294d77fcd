package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 语音配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AudioConfigRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "5852")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "语音文件")
    @ExcelProperty("语音文件")
    private String audioFile;

    @Schema(description = "音频时长")
    @ExcelProperty("音频时长")
    private Long audioLength;

    @Schema(description = "音频采样率")
    @ExcelProperty("音频采样率")
    private Long audioSampleRate;

    @Schema(description = "音频大小")
    @ExcelProperty("音频大小")
    private Long audioSize;

    @Schema(description = "音频比特率")
    @ExcelProperty("音频比特率")
    private Long bitrate;

    @Schema(description = "文字内容")
    @ExcelProperty("文字内容")
    private String content;

    @Schema(description = "生成错误信息")
    @ExcelProperty("生成错误信息")
    private String errorMessage;

    @Schema(description = "完成时间")
    @ExcelProperty("完成时间")
    private LocalDateTime finishTime;

    @Schema(description = "非法字符占比")
    @ExcelProperty("非法字符占比")
    private Double invisibleCharacterRatio;

    @Schema(description = "模型标志")
    @ExcelProperty("模型标志")
    private String model;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3256")
    @ExcelProperty("模型编号")
    private Long modelId;

    @Schema(description = "输出格式")
    @ExcelProperty("输出格式")
    private String outputFormat;

    @Schema(description = "模型平台")
    @ExcelProperty("模型平台")
    private String platform;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "字幕文件")
    @ExcelProperty("字幕文件")
    private String subtitleFile;

    @Schema(description = "消费字符数")
    @ExcelProperty("消费字符数")
    private Long usageCharacters;

    @Schema(description = "音色编号", example = "22446")
    @ExcelProperty("音色编号")
    private String voiceId;

    @Schema(description = "多音色列表")
    @ExcelProperty("多音色列表")
    private List<Map<String,String>> voiceList;

    @Schema(description = "音色名称", example = "张三")
    @ExcelProperty("音色名称")
    private String voiceName;

    @Schema(description = "可读字数", example = "23758")
    @ExcelProperty("可读字数")
    private Long wordCount;

}
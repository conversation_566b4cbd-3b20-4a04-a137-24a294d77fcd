package cn.iocoder.yudao.module.ai.controller.app.myapi.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Description: TODO
 * @Date: 2025/1/13 22:38
 * @Author: zhangq
 * @Version: 1.0
 */
@Schema(description = "用户APP - 绘画作品分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppDrawImageWorksPageReqVO extends PageParam {
}

package cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI 语音配置 消息 DO
 *
 * @since 2024/4/14 17:35
 * @since 2024/4/14 17:35
 */
@Table(name = "ai_audio_config")
@Comment(value = "语音配置")
@Entity
@TableName(value = "ai_audio_config", autoResultMap = true)
@KeySequence("ai_audio_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiAudioConfigDO extends TenantBaseDO {

    /**
     * 编号，作为每条聊天记录的唯一标识符
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 音色名称 系统定义
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '音色名称'")
    private String voiceName;

    /**
     * 模型平台
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '模型平台'")
    private String platform;

    /**
     * 模型标志
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '模型标志'")
    private String model;

    /**
     * 模型编号
     *
     * 关联 {@link AiYyModelDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '模型编号'")
    private Long modelId;

    /**
     * 音色编号
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '音色编号'")
    private String voiceId;

    /**
     * 多音色列表
     */
    @Type(JsonType.class)
    @Column(columnDefinition = "json COMMENT '多音色列表'")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<VoiceWeight> voiceList;

    /**
     * 文字内容
     */
    @Column(columnDefinition = "text COMMENT '文字内容'")
    private String content;

    /**
     * 模型ID 用于在咨询/反馈时帮助定位问题
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '模型ID 用于在咨询/反馈时帮助定位问题'")
    private String traceId;

    /**
     * 语音文件
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '语音文件'")
    private String audioFile;

    /**
     * subtitleFile
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '字幕文件'")
    private String subtitleFile;

    /**
     * 音频时长
     */
    @Column(columnDefinition = "bigint COMMENT '音频时长'")
    private Long audioLength;

    /**
     * 音频采样率
     */
    @Column(columnDefinition = "bigint COMMENT '音频采样率'")
    private Long audioSampleRate;

    /**
     * 音频大小。单位为字节。
     */
    @Column(columnDefinition = "bigint COMMENT '音频大小'")
    private Long audioSize;

    /**
     * 音频比特率
     */
    @Column(columnDefinition = "bigint COMMENT '音频比特率'")
    private Long bitrate;

    /**
     * 可读字数。已经发音的字数统计（不算标点等其他符号，包含汉字数字字母）
     */
    @Column(columnDefinition = "bigint COMMENT '可读字数'")
    private Long wordCount;

    /**
     * 非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
     */
    @Column(columnDefinition = "double COMMENT '非法字符占比'")
    private Double invisibleCharacterRatio;

    /**
     * 消费字符数。本次语音生成的计费字符数。
     */
    @Column(columnDefinition = "bigint COMMENT '消费字符数'")
    private Long usageCharacters;

    /**
     * 输出格式
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '输出格式'")
    private String outputFormat;

    /**
     * 生成状态
     *
     * 枚举 {@link AiImageStatusEnum}
     */
    @Column(columnDefinition = "int COMMENT '生成状态'")
    private Integer status;

    /**
     * 状态
     */
    @Column(columnDefinition = "int COMMENT '是否启用'")
    private Integer enable;

    /**
     * 生成错误信息
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '生成错误信息'")
    private String errorMessage;

    /**
     * 完成时间
     */
    @Column(columnDefinition = "datetime COMMENT '完成时间'")
    private LocalDateTime finishTime;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VoiceWeight {

        /**
         * 音色编号
         */
        @Column(length = 64, columnDefinition = "varchar(64) COMMENT '音色编号'")
        private String voiceId;

        /**
         * 音色权重
         */
        @Column(columnDefinition = "int COMMENT '音色权重'")
        private Integer weight;
    }
}

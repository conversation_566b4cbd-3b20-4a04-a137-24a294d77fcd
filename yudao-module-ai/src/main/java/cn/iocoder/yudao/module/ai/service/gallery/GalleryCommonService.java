package cn.iocoder.yudao.module.ai.service.gallery;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryCommonDO;
import jakarta.validation.Valid;

/**
 * AI 公开画廊 Service 接口
 *
 * <AUTHOR>
 */
public interface GalleryCommonService {

    /**
     * 创建AI 公开画廊
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGalleryCommon(@Valid GalleryCommonSaveReqVO createReqVO);

    /**
     * 更新AI 公开画廊
     *
     * @param updateReqVO 更新信息
     */
    void updateGalleryCommon(@Valid GalleryCommonSaveReqVO updateReqVO);

    /**
     * 删除AI 公开画廊
     *
     * @param id 编号
     */
    void deleteGalleryCommon(Long id);

    /**
     * 获得AI 公开画廊
     *
     * @param id 编号
     * @return AI 公开画廊
     */
    GalleryCommonDO getGalleryCommon(Long id);

    /**
     * 获得AI 公开画廊分页
     *
     * @param pageReqVO 分页查询
     * @return AI 公开画廊分页
     */
    PageResult<GalleryCommonDO> getGalleryCommonPage(GalleryCommonPageReqVO pageReqVO);

    /**
     * 获得AI 公开画廊
     *
     * @param id 编号
     * @return AI 公开画廊
     */
    AppGalleryCommonRespVO getAppGalleryCommon(Long userId,Long id);

    /**
     * 获得AI 公开画廊分页 APP
     * @param pageReqVO
     * @return
     */
    PageResult<GalleryCommonDO> getAppGalleryCommonPage(@Valid AppGalleryCommonPageReqVO pageReqVO);

    /**
     * 画图
     * @param id
     * @return
     */
    Long drawSame(Long userId,Long id);
}
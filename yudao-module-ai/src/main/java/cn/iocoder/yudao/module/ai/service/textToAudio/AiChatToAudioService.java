package cn.iocoder.yudao.module.ai.service.textToAudio;

import cn.iocoder.yudao.module.ai.controller.app.textToAudio.message.AppAiTextToAudioMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.app.textToAudio.message.AppAiTextToAudioReqVO;

/**
 * AI 聊天信息转语音信息 Service 接口
 *
 * <AUTHOR>
 */
public interface AiChatToAudioService {

    /**
     * 文字转语音
     * @param reqVO
     * @param userId 用户id
     * @return
     */
    AppAiTextToAudioMessageRespVO textToVoice(AppAiTextToAudioReqVO reqVO, Long userId);
}

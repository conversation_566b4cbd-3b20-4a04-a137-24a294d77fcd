package cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessagePageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessageSendReqVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppAiVirtualPartnerMessageSendRespVO;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message.AppVirtualPartnerMessagePraiseReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerMessageDO;
import jakarta.validation.Valid;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * AI 聊天消息 Service 接口
 *
 * <AUTHOR>
 */
public interface AiVirtualPartnerMessageService {

    /**
     * 发送消息
     *
     * @param sendReqVO 发送信息
     * @param userId 用户编号
     * @return 发送结果
     */
    AppAiVirtualPartnerMessageSendRespVO sendMessage(AppAiVirtualPartnerMessageSendReqVO sendReqVO, Long userId);

    /**
     * 发送消息
     *
     * @param sendReqVO 发送信息
     * @param userId 用户编号
     * @return 发送结果
     */
    Flux<CommonResult<AppAiVirtualPartnerMessageSendRespVO>> sendVirtualPartnerMessageStream(AppAiVirtualPartnerMessageSendReqVO sendReqVO, Long userId);

    /**
     * 获得指定对话的消息列表
     *
     * @param conversationId 对话编号
     * @return 消息列表
     */
    List<AiVirtualPartnerMessageDO> getVirtualPartnerMessageListByConversationId(Long conversationId);

    /**
     * 删除消息
     *
     * @param id 消息编号
     * @param userId 用户编号
     */
    void deleteVirtualPartnerMessage(Long id, Long userId);

    /**
     * 删除指定对话的消息
     *
     * @param conversationId 对话编号
     * @param userId 用户编号
     */
    void deleteVirtualPartnerMessageByConversationId(Long conversationId, Long userId);

    /**
     * 【管理员】删除消息
     *
     * @param id 消息编号
     */
    void deleteVirtualPartnerMessageByAdmin(Long id);

    /**
     * 获得聊天对话的消息数量 Map
     *
     * @param conversationIds 对话编号数组
     * @return 消息数量 Map
     */
    Map<Long, Integer> getVirtualPartnerMessageCountMap(Collection<Long> conversationIds);

    /**
     * 获得聊天消息的分页
     *
     * @param pageReqVO 分页查询
     * @return 聊天消息的分页
     */
    PageResult<AiVirtualPartnerMessageDO> getVirtualPartnerMessagePage(AppAiVirtualPartnerMessagePageReqVO pageReqVO);

    /**
     * 获得指定对话的聊天消分页
     * @param conversationId 对话编号
     * @return 聊天消息的分页
     */
    PageResult<AiVirtualPartnerMessageDO> getVirtualPartnerMessagePageByConversationId(AppAiVirtualPartnerMessagePageReqVO conversationId);

    /**
     * 点赞消息
     * @param reqVO
     */
    void praiseVirtualPartnerMessage(@Valid AppVirtualPartnerMessagePraiseReqVO reqVO);

    /**
     * 校验消息是否存在
     *
     * @param id 消息编号
     * @param userId 用户编号
     * @return 消息
     */
    AiVirtualPartnerMessageDO validateVirtualPartnerMessageExists(Long id, Long userId);
}

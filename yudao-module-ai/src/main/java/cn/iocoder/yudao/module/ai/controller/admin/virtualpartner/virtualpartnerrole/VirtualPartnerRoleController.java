package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRolePageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRoleRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.virtualpartnerrole.vo.VirtualPartnerRoleSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.VirtualPartnerRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 虚拟陪伴角色")
@RestController
@RequestMapping("/ai/virtual-partner-role")
@Validated
public class VirtualPartnerRoleController {

    @Resource
    private VirtualPartnerRoleService virtualPartnerRoleService;

    @PostMapping("/create")
    @Operation(summary = "创建虚拟陪伴角色")
    @PreAuthorize("@ss.hasPermission('ai:virtual-partner-role:create')")
    public CommonResult<Long> createVirtualPartnerRole(@Valid @RequestBody VirtualPartnerRoleSaveReqVO createReqVO) {
        return success(virtualPartnerRoleService.createVirtualPartnerRole(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新虚拟陪伴角色")
    @PreAuthorize("@ss.hasPermission('ai:virtual-partner-role:update')")
    public CommonResult<Boolean> updateVirtualPartnerRole(@Valid @RequestBody VirtualPartnerRoleSaveReqVO updateReqVO) {
        virtualPartnerRoleService.updateVirtualPartnerRole(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除虚拟陪伴角色")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:virtual-partner-role:delete')")
    public CommonResult<Boolean> deleteVirtualPartnerRole(@RequestParam("id") Long id) {
        virtualPartnerRoleService.deleteVirtualPartnerRole(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得虚拟陪伴角色")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:virtual-partner-role:query')")
    public CommonResult<VirtualPartnerRoleRespVO> getVirtualPartnerRole(@RequestParam("id") Long id) {
        AiVirtualPartnerRoleDO virtualPartnerRole = virtualPartnerRoleService.getVirtualPartnerRole(id);
        return success(BeanUtils.toBean(virtualPartnerRole, VirtualPartnerRoleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得虚拟陪伴角色分页")
    @PreAuthorize("@ss.hasPermission('ai:virtual-partner-role:query')")
    public CommonResult<PageResult<VirtualPartnerRoleRespVO>> getVirtualPartnerRolePage(@Valid VirtualPartnerRolePageReqVO pageReqVO) {
        PageResult<AiVirtualPartnerRoleDO> pageResult = virtualPartnerRoleService.getVirtualPartnerRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VirtualPartnerRoleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出虚拟陪伴角色 Excel")
    @PreAuthorize("@ss.hasPermission('ai:virtual-partner-role:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportVirtualPartnerRoleExcel(@Valid VirtualPartnerRolePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiVirtualPartnerRoleDO> list = virtualPartnerRoleService.getVirtualPartnerRolePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "虚拟陪伴角色.xls", "数据", VirtualPartnerRoleRespVO.class,
                        BeanUtils.toBean(list, VirtualPartnerRoleRespVO.class));
    }

}
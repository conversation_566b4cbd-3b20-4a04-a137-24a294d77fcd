package cn.iocoder.yudao.module.ai.dal.dataobject.textToAudio;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerMessageDO;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * AI 文本语音生成 消息 DO
 *
 * @since 2024/4/14 17:35
 * @since 2024/4/14 17:35
 */
@Table(name = "ai_text_to_audio_message")
@Comment(value = "文本语音生成")
@Entity
@TableName("ai_text_to_audio_message")
@KeySequence("ai_text_to_audio_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiTextToAudioMessageDO extends BaseDO {

    /**
     * 编号，作为每条聊天记录的唯一标识符
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 对话编号
     *
     * 关联 {@link AiVirtualPartnerMessageDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '对话编号'")
    private Long messageId;

    /**
     * 模型ID 用于在咨询/反馈时帮助定位问题
     */
    @Column(length = 64, columnDefinition = "varchar(64) DEFAULT NULL COMMENT '模型ID 用于在咨询/反馈时帮助定位问题'")
    private String traceId;

    /**
     * 模型平台
     */
    @Column(length = 32, columnDefinition = "varchar(32) DEFAULT NULL COMMENT '模型平台'")
    private String platform;
    /**
     * 用户编号
     *
     * 关联 AdminUserDO 的 userId 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '用户编号'")
    private Long userId;

    /**
     * 模型标志
     */
    @Column(length = 32, columnDefinition = "varchar(32) DEFAULT NULL COMMENT '模型标志'")
    private String model;
    /**
     * 模型编号
     *
     * 关联 {@link AiVirtualPartnerMessageDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '模型编号'")
    private Long modelId;

    /**
     * 文字内容
     */
    @Column(columnDefinition = "text COMMENT '文字内容'")
    private String content;

    /**
     * 语音文件
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) DEFAULT NULL COMMENT '语音文件'")
    private String audioFile;

    /**
     * subtitleFile
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) DEFAULT NULL COMMENT '字幕文件'")
    private String subtitleFile;

    /**
     * 音频时长
     */
    @Column(columnDefinition = "bigint COMMENT '音频时长'")
    private Long audioLength;

    /**
     * 音频采样率
     */
    @Column(columnDefinition = "bigint COMMENT '音频采样率'")
    private Long audioSampleRate;

    /**
     * 音频大小。单位为字节。
     */
    @Column(columnDefinition = "bigint COMMENT '音频大小'")
    private Long audioSize;

    /**
     * 音频比特率
     */
    @Column(columnDefinition = "bigint COMMENT '音频比特率'")
    private Long bitrate;

    /**
     * 可读字数。已经发音的字数统计（不算标点等其他符号，包含汉字数字字母）
     */
    @Column(columnDefinition = "bigint COMMENT '可读字数'")
    private Long wordCount;

    /**
     * 非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
     */
    @Column(columnDefinition = "double COMMENT '非法字符占比'")
    private Double invisibleCharacterRatio;

    /**
     * 消费字符数。本次语音生成的计费字符数。
     */
    @Column(columnDefinition = "bigint COMMENT '消费字符数'")
    private Long usageCharacters;

    /**
     * 输出格式
     */
    @Column(length = 32, columnDefinition = "varchar(32) COMMENT '输出格式'")
    private String outputFormat;

    /**
     * 生成状态
     *
     * 枚举 {@link AiImageStatusEnum}
     */
    @Column(columnDefinition = "int COMMENT '生成状态'")
    private Integer status;

    /**
     * 生成错误信息
     */
    @Column(length = 1024, columnDefinition = "varchar(1024) COMMENT '生成错误信息'")
    private String errorMessage;

    /**
     * 完成时间
     */
    @Column(columnDefinition = "datetime COMMENT '完成时间'")
    private LocalDateTime finishTime;
}

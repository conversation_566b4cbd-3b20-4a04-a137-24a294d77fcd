package cn.iocoder.yudao.module.ai.service.contentwrite.category;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategoryPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategorySaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteCategoryDO;
import cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.category.ContentWriteCategoryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CONTENT_WRITE_CATEGORY_NOT_EXISTS;

/**
 * AI 内容创作分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContentWriteCategoryServiceImpl implements ContentWriteCategoryService {

    @Resource
    private ContentWriteCategoryMapper contentWriteCategoryMapper;

    @Override
    public Long createContentWriteCategory(ContentWriteCategorySaveReqVO createReqVO) {
        // 插入
        AiContentWriteCategoryDO contentWriteCategory = BeanUtils.toBean(createReqVO, AiContentWriteCategoryDO.class);
        contentWriteCategoryMapper.insert(contentWriteCategory);
        // 返回
        return contentWriteCategory.getId();
    }

    @Override
    public void updateContentWriteCategory(ContentWriteCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateContentWriteCategoryExists(updateReqVO.getId());
        // 更新
        AiContentWriteCategoryDO updateObj = BeanUtils.toBean(updateReqVO, AiContentWriteCategoryDO.class);
        contentWriteCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteContentWriteCategory(Long id) {
        // 校验存在
        validateContentWriteCategoryExists(id);
        // 删除
        contentWriteCategoryMapper.deleteById(id);
    }

    private void validateContentWriteCategoryExists(Long id) {
        if (contentWriteCategoryMapper.selectById(id) == null) {
            throw exception(CONTENT_WRITE_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public AiContentWriteCategoryDO getContentWriteCategory(Long id) {
        return contentWriteCategoryMapper.selectById(id);
    }

    @Override
    public PageResult<AiContentWriteCategoryDO> getContentWriteCategoryPage(ContentWriteCategoryPageReqVO pageReqVO) {
        return contentWriteCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AiContentWriteCategoryDO> getContentWriteCategoryListByStatus(Integer status) {
        return contentWriteCategoryMapper.selectList(status);
    }

    @Override
    public List<AiContentWriteCategoryDO> getAppContentWriteCategorySimpleList() {
        return contentWriteCategoryMapper.selectList(CommonStatusEnum.ENABLE.getStatus());
    }

}
package cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - AI算力规则配置新增/修改 Request VO")
@Data
public class CoinRoleConfigSaveReqVO {

    @Schema(description = "编号，唯一自增，修改时传值", example = "6148")
    private Long id;

    /**
     * 算力细则
     */
    @Schema(description = "算力细则")
    private String coinRole;

    /**
     * 算力说明
     */
    @Schema(description = "算力说明")
    private String coinRoleDesc;
}
package cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.role;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.role.vo.ContentWriteRolePageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteRoleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 内容创作角色 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentWriteRoleMapper extends BaseMapperX<AiContentWriteRoleDO> {

    default PageResult<AiContentWriteRoleDO> selectPage(ContentWriteRolePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiContentWriteRoleDO>()
                .betweenIfPresent(AiContentWriteRoleDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiContentWriteRoleDO::getCategoryId, reqVO.getCategoryId())
                //.eqIfPresent(AiContentWriteRoleDO::getBackgroundUrl, reqVO.getBackgroundUrl())
                .eqIfPresent(AiContentWriteRoleDO::getClickCount, reqVO.getClickCount())
                .eqIfPresent(AiContentWriteRoleDO::getCover, reqVO.getCover())
                .eqIfPresent(AiContentWriteRoleDO::getDescContent, reqVO.getDescContent())
                .eqIfPresent(AiContentWriteRoleDO::getCoverUrl, reqVO.getCoverUrl())
                //.eqIfPresent(AiContentWriteRoleDO::getCoverVoiceFile, reqVO.getCoverVoiceFile())
                .eqIfPresent(AiContentWriteRoleDO::getInputPresetWord, reqVO.getInputPresetWord())
                .eqIfPresent(AiContentWriteRoleDO::getMaxContexts, reqVO.getMaxContexts())
                .eqIfPresent(AiContentWriteRoleDO::getMaxTokens, reqVO.getMaxTokens())
                .eqIfPresent(AiContentWriteRoleDO::getModel, reqVO.getModel())
                .eqIfPresent(AiContentWriteRoleDO::getModelId, reqVO.getModelId())
                .likeIfPresent(AiContentWriteRoleDO::getName, reqVO.getName())
                .eqIfPresent(AiContentWriteRoleDO::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AiContentWriteRoleDO::getPresetExample, reqVO.getPresetExample())
                .eqIfPresent(AiContentWriteRoleDO::getSort, reqVO.getSort())
                .eqIfPresent(AiContentWriteRoleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AiContentWriteRoleDO::getSystemMessage, reqVO.getSystemMessage())
                .eqIfPresent(AiContentWriteRoleDO::getTemperature, reqVO.getTemperature())
                .eqIfPresent(AiContentWriteRoleDO::getTopP, reqVO.getTopP())
                .orderByDesc(AiContentWriteRoleDO::getId));
    }

}
package cn.iocoder.yudao.module.ai.controller.app.myapi;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.app.myapi.vo.AppDrawImageWorksPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.myapi.vo.AppDrawImageWorksRespVO;
import cn.iocoder.yudao.module.ai.convert.image.DrawImageConvert;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageDO;
import cn.iocoder.yudao.module.ai.service.image.draw.TaskDrawService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * @Description: TODO
 * @Module: 我的信息接口
 * @Date: 2025/1/13 22:25
 * @Author: zhangq
 * @Version: 1.0
 */
@Tag(name = "用户APP - 我的 接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/my-info")
@Validated
public class AppMyResourceController {


    private final TaskDrawService taskDrawService;

    /**
     * 获取我的作品
     *
     * @return
     */
    @Operation(summary = "获取我的作品-分页查询")
    @PostMapping("/image-works")
    public CommonResult<PageResult<AppDrawImageWorksRespVO>> getAppDrawImageWorksPage(@RequestBody AppDrawImageWorksPageReqVO pageReqVO) {
        Long userId = getLoginUserId();
        PageResult<AiImageDO> page = taskDrawService.getAppDrawImageWorksPage(pageReqVO,userId);
        PageResult<AppDrawImageWorksRespVO> pageResult = DrawImageConvert.INSTANCE.convertAppPage(page);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(pageResult);
    }

    @Operation(summary = "获取我的作品-详情")
    @GetMapping("/image-works/detail")
    public CommonResult<AppDrawImageWorksRespVO> getAppDrawImageWorksDetail(@RequestParam("id") Long id) {
        Long userId = getLoginUserId();
        AiImageDO aiImageDO = taskDrawService.getById(id);
        if (aiImageDO == null || !aiImageDO.getUserId().equals(userId)) {
            return success(null);
        }
        AppDrawImageWorksRespVO appDrawImageWorksRespVO = DrawImageConvert.INSTANCE.convertApp(aiImageDO);
        return success(appDrawImageWorksRespVO);
    }
}

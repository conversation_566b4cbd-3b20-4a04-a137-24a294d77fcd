package cn.iocoder.yudao.module.ai.controller.admin.config.coinrole;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo.CoinRoleConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo.CoinRoleConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.CoinRoleConfigDO;
import cn.iocoder.yudao.module.ai.service.config.coinrole.CoinRoleConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI算力规则配置")
@RestController
@RequestMapping("/ai/coin-role-config")
@Validated
public class CoinRoleConfigController {

    @Resource
    private CoinRoleConfigService coinRoleConfigService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "创建/修改AI算力规则配置")
    @PreAuthorize("@ss.hasPermission('ai:base-info-config:createOrUpdate')")
    public CommonResult<Long> saveOrUpdateCoinRoleConfig(@Valid @RequestBody CoinRoleConfigSaveReqVO createReqVO) {
        return success(coinRoleConfigService.saveOrUpdateCoinRoleConfig(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI算力规则配置")
    @PreAuthorize("@ss.hasPermission('ai:base-info-config:query')")
    public CommonResult<CoinRoleConfigRespVO> getSimpleBaseInfoConfig() {
        CoinRoleConfigDO roleConfig = coinRoleConfigService.getSimpleCoinRoleConfig();
        return success(BeanUtils.toBean(roleConfig, CoinRoleConfigRespVO.class));
    }

}
package cn.iocoder.yudao.module.ai.convert.image;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppAspectRatioPresetRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppModelStyleRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.DrawImageConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.app.myapi.vo.AppDrawImageWorksRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DrawImageConvert {

    DrawImageConvert INSTANCE = Mappers.getMapper(DrawImageConvert.class);

    List<AppModelStyleRespVO> convertModelStyleList(List<AiModelStyleDO> list);

    AppAspectRatioPresetRespVO convertAspectRatioPreset(AiImageConfigDO configDO);

    DrawImageConfigRespVO convertDrawImageConfig(AiImageConfigDO configDO);

    PageResult<AppDrawImageWorksRespVO> convertAppPage(PageResult<AiImageDO> page);

    AppDrawImageWorksRespVO convertApp(AiImageDO aiImageDO);
}

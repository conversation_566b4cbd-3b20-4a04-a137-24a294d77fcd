package cn.iocoder.yudao.module.ai.dal.dataobject.invite;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * @Description: 邀请设置
 * @Date: 2025/4/6 12:56
 * @Author: zhangq
 * @Version: 1.0
 */
@Table(name = "ai_invite_config")
@Comment(value = "邀请设置")
@Entity
@TableName("ai_invite_config")
@KeySequence("ai_invite_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiInviteConfigDO extends TenantBaseDO {

    /**
     * 用户编号
     * <p>
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 邀请图片
     */
    @Column(columnDefinition = "varchar(500) COMMENT '邀请图片'")
    private String imageUrl;
}

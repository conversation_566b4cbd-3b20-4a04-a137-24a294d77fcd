package cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation.AppAiVirtualpartnerConversationCreateMyReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import cn.iocoder.yudao.module.ai.dal.mysql.virtualpartner.virtualpartner.AiVirtualPartnerConversationMapper;
import cn.iocoder.yudao.module.ai.service.model.AiYyModelService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.*;

/**
 * AI 聊天对话 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AiVirtualPartnerConversationServiceImpl implements AiVirtualPartnerConversationService {

    @Resource
    private AiVirtualPartnerConversationMapper virtualPartnerConversationMapper;

    @Resource
    private AiYyModelService chatModalService;
    @Resource
    private VirtualPartnerRoleService virtualPartnerRoleService;

    @Override
    public Long createVirtualpartnerConversationMy(AppAiVirtualpartnerConversationCreateMyReqVO createReqVO, Long userId) {
        // 1.1 获得 AiVirtualpartnerRoleDO 聊天角色
        if (createReqVO.getRoleId() == null) {
            throw exception(VIRTUAL_PARTNER_ROLE_NOT_EXISTS_BY_ID);
        }
        AiVirtualPartnerRoleDO role = virtualPartnerRoleService.validateVirtualPartnerRole(createReqVO.getRoleId());
        // 1.2 获得 AiChatModelDO 聊天模型
        AiYyModelDO model = role != null && role.getModelId() != null ? chatModalService.validateModel(role.getModelId())
                : null;
        Assert.notNull(model, MODEL_NOT_EXISTS.getMsg());

        // 2. 创建 AiVirtualpartnerConversationDO 聊天对话
        AiVirtualPartnerConversationDO conversation = new AiVirtualPartnerConversationDO().setUserId(userId).setPinned(false)
                .setModelId(model.getId()).setModel(model.getModel()).setTopP(role.getTopP())
                .setSystemMessage(role.getSystemMessage())
                .setTemperature(role.getTemperature()).setMaxTokens(role.getMaxTokens()).setMaxContexts(role.getMaxContexts());
        if (role != null) {
            conversation.setTitle(null != role.getName()? role.getName():AiAssistantConversationDO.TITLE_DEFAULT).setRoleId(role.getId()).setSystemMessage(role.getSystemMessage());
        } else {
            conversation.setTitle(AiAssistantConversationDO.TITLE_DEFAULT);
        }
        virtualPartnerConversationMapper.insert(conversation);
        return conversation.getId();
    }

    /*private void validateChatModel(AiYyModelDO model) {
        if (ObjectUtil.isAllNotEmpty(model.getTemperature(), model.getMaxTokens(), model.getMaxContexts())) {
            return;
        }
        throw exception(CHAT_CONVERSATION_MODEL_ERROR);
    }*/

    @Override
    public List<AiVirtualPartnerConversationDO> getVirtualPartnerConversationAppListByUserId(Long userId) {
        return virtualPartnerConversationMapper.selectAppListByUserId(userId);
    }

    @Override
    public AiVirtualPartnerConversationDO getVirtualPartnerConversationById(Long userId) {
        return virtualPartnerConversationMapper.selectByUserIdDesc(userId);
    }

    @Override
    public AiVirtualPartnerConversationDO getVirtualPartnerConversation(Long id) {
        return virtualPartnerConversationMapper.selectById(id);
    }

    @Override
    public AiVirtualPartnerConversationDO validateVirtualPartnerConversationExists(Long id) {
        AiVirtualPartnerConversationDO conversation = virtualPartnerConversationMapper.selectById(id);
        if (conversation == null) {
            throw exception(CHAT_CONVERSATION_NOT_EXISTS);
        }
        return conversation;
    }

}

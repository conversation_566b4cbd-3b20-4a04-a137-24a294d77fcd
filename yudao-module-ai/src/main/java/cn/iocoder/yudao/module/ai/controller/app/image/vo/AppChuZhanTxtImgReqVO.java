package cn.iocoder.yudao.module.ai.controller.app.image.vo;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 触站AI
 *提交绘画的参数，此接口为异步提交任务接口，并不直接返回最终的绘画结果，请调用获取任务详情接口获取绘画的进度或者结果
 */
@NoArgsConstructor
@Data
public class AppChuZhanTxtImgReqVO implements Serializable {

    @Schema(description = "模型平台", requiredMode = Schema.RequiredMode.REQUIRED, example = "ChuZhan")
    private String platform;

    /**
     * 图片尺寸
     */
    @Schema(description = "图片尺寸 竖图-portrait,方图-square,横图-landscape")
    private String imgSize;

    /**
     * 生成图片宽度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生
     */
    //@Schema(description = "生成图片宽度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生")
    //private Long width;
    /**
     * 生成图片高度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生
     */
    //@Schema(description = "生成图片高度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生")
    //private Long height;
    /**
     * 高清化倍率，对生成的图片进行高清化处理，仅在`文生图`模式有效，取值范围为1~3之间
     */
    //@Schema(description = "高清化倍率，对生成的图片进行高清化处理，仅在`文生图`模式有效，取值范围为1~3之间")
    //private Double hrScale;

    /**
     * 高清处理步数，高清迭代步数，（不建议少于15，会严重影响画面生成效果）
     */
    //@Schema(description = "高清处理步数，高清迭代步数，（不建议少于15，会严重影响画面生成效果）" , example = "20")
    //private Long hrSteps;

    /**
     * 参考图
     */
    @Schema(description = "参考图,目前没用到可不传")
    private String img;

    /**
     *
     * 风格ID，指定要生成对应风格预设的图片，触站AI官方会预设大量可以直接进行生产使用的风格预设，部分可用ID请参考[此文档](https://chuzhanai.apifox.cn/doc-3699065)
     * 此字段也可以使用模型训练API自定义风格产出的结果，详见[模型训练](https://chuzhanai.apifox.cn/api-123833428)
     */
    @Schema(description = "风格ID，指定要生成对应风格预设的图片，触站AI官方会预设大量可以直接进行生产使用的风格预设，部分可用ID请参考[此文档](https://chuzhanai.apifox.cn/doc-3699065)")
    private Integer modelStyleId;

    /**
     * 预测模拟积分消耗，如果传值为true则代表本次是测试模拟积分消耗量，不进行真正的绘画操作，仅用于计算同参数下积分消耗量，不扣减任何积分
     */
    @Schema(description = "预测模拟积分消耗，如果传值为true则代表本次是测试模拟积分消耗量，不进行真正的绘画操作，仅用于计算同参数下积分消耗量，不扣减任何积分")
    private Boolean predictConsume = true;

    /**
     * 描述词条，描述画面中需要出现的内容，支持中英文描述
     * 详细词条技巧请参考[此文章](https://www.huashi6.com/article/detail-13420.html)
     */
    @Schema(description = "描述词条，描述画面中需要出现的内容，支持中英文描述\n" +
            "详细词条技巧请参考[此文章](https://www.huashi6.com/article/detail-13420.html)")
    private String prompt;

    /**
     * 负面词条，用于排除画面中要出现的内容描述，支持中英文描述
     */
    @Schema(description = "负面词条\n"+
              "用于排除画面中要出现的内容描述，支持中英文描述")
    private String negativePrompt;

    /**
     * 自定义请求回调标识，任意长度不超过32的字符串，具体[请参考回调使用指南](https://chuzhanai.apifox.cn/doc-3556414)
     */
    @Hidden
    @Schema(description = "自定义请求回调标识，任意长度不超过32的字符串，具体[请参考回调使用指南](https://chuzhanai.apifox.cn/doc-3556414)")
    private String nonce;

    @Schema(description = "default normal high 说明 默认 高清 超清")
    private String resolutionPreset;

    /**
     * 脸部修复开关
     */
    @Schema(description = "脸部修复开关")
    private Boolean faceFix;

    /**
     * 细节倍率 取值范围为1~9小于5则画面偏向简单扁平话、草稿化大于5则画面偏向添加更多光影、服饰、头发等细节详细效果
     */
    @Schema(description = "细节倍率 取值范围为1~9小于5则画面偏向简单扁平话、草稿化大于5则画面偏向添加更多光影、服饰、头发等细节详细效果")
    private Integer detailsLevel;

    @Schema(description = "单次批量生成数量,单次生成图片数量不可以超过6个")
    private Integer batchSize;

    /**
     * 消耗积分
     */
    @Schema(description = "消耗积分")
    private Integer aiCoin;
}
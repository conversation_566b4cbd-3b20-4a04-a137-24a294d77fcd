package cn.iocoder.yudao.module.ai.service.image.draw;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppChuZhanTxtImgReqVO;
import cn.iocoder.yudao.module.ai.controller.app.myapi.vo.AppDrawImageWorksPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageDO;
import com.baomidou.mybatisplus.extension.service.IService;

public interface TaskDrawService extends IService<AiImageDO> {

    /**
     * 绘制图片
     *
     * @param userId 用户编号
     * @param params 绘制请求
     * @return 绘画编号
     */
    Long createTaskAndBack(Long userId, AppChuZhanTxtImgReqVO params);

    /**
     * 测试MQ处理绘画任务
     * @param taskId
     */
    void testMQ(Long taskId);

    /**
     * 删除绘画任务
     * @param id
     * @param userId
     */
    void deleteDrawImage(Long id, Long userId);

    /**
     * 撤回绘画任务
     * @param id
     * @param userId
     */
    void revoke(Long id, Long userId);

    /**
     * 获取用户绘画任务分页
     * @param pageReqVO
     * @return
     */
    PageResult<AiImageDO> getAppDrawImageWorksPage(AppDrawImageWorksPageReqVO pageReqVO, Long userId);

    /**
     * 执行绘画任务回调通知
     *
     * 注意，该方法提供给定时任务调用。目前是 yudao-server 进行调用
     * @return 通知数量
     */
    int executeDrawTask();
}

package cn.iocoder.yudao.module.ai.dal.mysql.assistant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.assistant.vo.conversation.AiAssistantConversationPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI 聊天对话 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiAssistantConversationMapper extends BaseMapperX<AiAssistantConversationDO> {

    default List<AiAssistantConversationDO> selectListByUserId(Long userId) {
        return selectList(AiAssistantConversationDO::getUserId, userId);
    }

    default List<AiAssistantConversationDO> selectListByUserIdAndPinned(Long userId, boolean pinned) {
        return selectList(new LambdaQueryWrapperX<AiAssistantConversationDO>()
                .eq(AiAssistantConversationDO::getUserId, userId)
                .eq(AiAssistantConversationDO::getPinned, pinned));
    }

    default PageResult<AiAssistantConversationDO> selectAssistantConversationPage(AiAssistantConversationPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiAssistantConversationDO>()
                .eqIfPresent(AiAssistantConversationDO::getUserId, pageReqVO.getUserId())
                .likeIfPresent(AiAssistantConversationDO::getTitle, pageReqVO.getTitle())
                .betweenIfPresent(AiAssistantConversationDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(AiAssistantConversationDO::getId));
    }

    default List<AiAssistantConversationDO> selectAppListByUserId(Long userId){
        return selectList(new LambdaQueryWrapperX<AiAssistantConversationDO>()
                .eq(AiAssistantConversationDO::getUserId, userId)
                .orderByDesc(AiAssistantConversationDO::getCreateTime)
                .last("LIMIT 30")
        );
    }

    default AiAssistantConversationDO selectByUserIdDesc(Long userId){
        return selectOne(new LambdaQueryWrapperX<AiAssistantConversationDO>()
                .eq(AiAssistantConversationDO::getUserId, userId)
                .orderByDesc(AiAssistantConversationDO::getId)
                .last("LIMIT 1")
        );
    };
}

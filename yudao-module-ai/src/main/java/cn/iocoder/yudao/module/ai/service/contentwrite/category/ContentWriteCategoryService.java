package cn.iocoder.yudao.module.ai.service.contentwrite.category;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategoryPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategorySaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteCategoryDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * AI 内容创作分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ContentWriteCategoryService {

    /**
     * 创建AI 内容创作分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContentWriteCategory(@Valid ContentWriteCategorySaveReqVO createReqVO);

    /**
     * 更新AI 内容创作分类
     *
     * @param updateReqVO 更新信息
     */
    void updateContentWriteCategory(@Valid ContentWriteCategorySaveReqVO updateReqVO);

    /**
     * 删除AI 内容创作分类
     *
     * @param id 编号
     */
    void deleteContentWriteCategory(Long id);

    /**
     * 获得AI 内容创作分类
     *
     * @param id 编号
     * @return AI 内容创作分类
     */
    AiContentWriteCategoryDO getContentWriteCategory(Long id);

    /**
     * 获得AI 内容创作分类分页
     *
     * @param pageReqVO 分页查询
     * @return AI 内容创作分类分页
     */
    PageResult<AiContentWriteCategoryDO> getContentWriteCategoryPage(ContentWriteCategoryPageReqVO pageReqVO);

    /**
     * 获得AI 内容创作分类列表
     * @param status
     * @return
     */
    List<AiContentWriteCategoryDO> getContentWriteCategoryListByStatus(Integer status);

    /**
     * 获得AI 内容创作分类列表
     * @return
     */
    List<AiContentWriteCategoryDO> getAppContentWriteCategorySimpleList();
}
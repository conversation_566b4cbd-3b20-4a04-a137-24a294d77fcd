package cn.iocoder.yudao.module.ai.service.model;

import cn.iocoder.yudao.module.ai.framework.ai.core.model.midjourney.api.MidjourneyApi;
import cn.iocoder.yudao.module.ai.framework.ai.core.model.suno.api.SunoApi;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.model.AiYyModelPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.model.AiYyModelSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import jakarta.validation.Valid;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.image.ImageModel;
import org.springframework.ai.vectorstore.VectorStore;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * AI 聊天模型 Service 接口
 *
 * <AUTHOR>
 * @since 2024/4/24 19:42
 */
public interface AiYyModelService {

    /**
     * 创建聊天模型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createModel(@Valid AiYyModelSaveReqVO createReqVO);

    /**
     * 更新聊天模型
     *
     * @param updateReqVO 更新信息
     */
    void updateModel(@Valid AiYyModelSaveReqVO updateReqVO);

    /**
     * 删除聊天模型
     *
     * @param id 编号
     */
    void deleteModel(Long id);

    /**
     * 获得聊天模型
     *
     * @param id 编号
     * @return 聊天模型
     */
    AiYyModelDO getModel(Long id);

    /**
     * 获得默认的聊天模型
     *
     * 如果获取不到，则抛出 {@link cn.iocoder.yudao.framework.common.exception.ServiceException} 业务异常
     *
     * @return 聊天模型
     */
    AiYyModelDO getRequiredDefaultModel();

    /**
     * 获得聊天模型分页
     *
     * @param pageReqVO 分页查询
     * @return 聊天模型分页
     */
    PageResult<AiYyModelDO> getModelPage(AiYyModelPageReqVO pageReqVO);

    /**
     * 校验聊天模型
     *
     * @param id 编号
     * @return 聊天模型
     */
    AiYyModelDO validateModel(Long id);

    /**
     * 获得模型列表
     *
     * @param status 状态
     * @param type 类型
     * @param platform 平台，允许空
     * @return 模型列表
     */
    List<AiYyModelDO> getModelListByStatusAndType(Integer status, @Nullable Integer type,
                                                @Nullable String platform);
    /**
     * 获得聊天模型列表
     *
     * @param status 状态
     * @return 聊天模型列表
     */
    List<AiYyModelDO> getModelListByStatus(Integer status);

    // ========== 与 Spring AI 集成 ==========

    /**
     * 获得 ChatModel 对象
     *
     * @param id 编号
     * @return ChatModel 对象
     */
    ChatModel getChatModel(Long id);

    /**
     * 获得 ImageModel 对象
     *
     * @param id 编号
     * @return ImageModel 对象
     */
    ImageModel getImageModel(Long id);

    /**
     * 获得 MidjourneyApi 对象
     *
     * @param id 编号
     * @return MidjourneyApi 对象
     */
    MidjourneyApi getMidjourneyApi(Long id);

    /**
     * 获得 SunoApi 对象
     *
     * @return SunoApi 对象
     */
    SunoApi getSunoApi();

    /**
     * 获得 VectorStore 对象
     *
     * @param id 编号
     * @param metadataFields 元数据的定义
     * @return VectorStore 对象
     */
    VectorStore getOrCreateVectorStore(Long id, Map<String, Class<?>> metadataFields);

    /**
     * 获得聊天模型列表
     * @param platform
     * @return
     */
    List<AiYyModelDO> getChatModelByPlatform(String platform);
}

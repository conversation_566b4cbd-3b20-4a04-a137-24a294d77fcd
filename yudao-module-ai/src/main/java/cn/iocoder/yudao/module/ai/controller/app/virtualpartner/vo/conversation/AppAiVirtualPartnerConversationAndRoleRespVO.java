package cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.conversation;

import cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.virtualpartnerrole.AppVirtualPartnerRoleRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户APP - AI 聊天消息 Response VO")
@Data
public class AppAiVirtualPartnerConversationAndRoleRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户编号", example = "4096")
    private Long userId;

    @Schema(description = "角色编号", example = "888")
    private Long roleId;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED, example = "gpt-3.5-turbo")
    private String model;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    private Long modelId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-05-12 12:51")
    private LocalDateTime createTime;

    @Schema(description = "对话标题", example = "小黄")
    private String title;

    @Schema(description = "单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "角色信息")
    private AppVirtualPartnerRoleRespVO role;

}

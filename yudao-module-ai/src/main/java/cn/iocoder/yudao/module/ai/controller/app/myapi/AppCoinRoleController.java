package cn.iocoder.yudao.module.ai.controller.app.myapi;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.config.coinrole.vo.CoinRoleConfigRespVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.CoinRoleConfigDO;
import cn.iocoder.yudao.module.ai.service.config.coinrole.CoinRoleConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * @Description: TODO
 * @Date: 2025/4/2 23:03
 * @Author: zhangq
 * @Version: 1.0
 */
@Tag(name = "用户APP - AI算力规则配置")
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/coin-role-config")
@Validated
public class AppCoinRoleController {

    @Resource
    private CoinRoleConfigService coinRoleConfigService;

    @GetMapping("/get")
    @Operation(summary = "获得AI算力规则配置")
    public CommonResult<CoinRoleConfigRespVO> getSimpleBaseInfoConfig() {
        CoinRoleConfigDO roleConfig = coinRoleConfigService.getSimpleCoinRoleConfig();
        return success(BeanUtils.toBean(roleConfig, CoinRoleConfigRespVO.class));
    }
}

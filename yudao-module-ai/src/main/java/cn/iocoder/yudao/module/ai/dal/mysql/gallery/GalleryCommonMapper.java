package cn.iocoder.yudao.module.ai.dal.mysql.gallery;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.controller.app.gallery.vo.AppGalleryCommonPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryCommonDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 公开画廊 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GalleryCommonMapper extends BaseMapperX<GalleryCommonDO> {

    default PageResult<GalleryCommonDO> selectPage(GalleryCommonPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GalleryCommonDO>()
                .betweenIfPresent(GalleryCommonDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(GalleryCommonDO::getCoverUrl, reqVO.getCoverUrl())
                .eqIfPresent(GalleryCommonDO::getImgUrls, reqVO.getImgUrls())
                .eqIfPresent(GalleryCommonDO::getPrompt, reqVO.getPrompt())
                .eqIfPresent(GalleryCommonDO::getSort, reqVO.getSort())
                .eqIfPresent(GalleryCommonDO::getStatus, reqVO.getStatus())
                .orderByDesc(GalleryCommonDO::getId));
    }

    default PageResult<GalleryCommonDO> selectAppPage(AppGalleryCommonPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GalleryCommonDO>()
                .select(GalleryCommonDO::getId, GalleryCommonDO::getCoverUrl, GalleryCommonDO::getSort)
                .eq(GalleryCommonDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .orderByAsc(GalleryCommonDO::getSort)
                .orderByDesc(GalleryCommonDO::getCreateTime)
        );
    }

}
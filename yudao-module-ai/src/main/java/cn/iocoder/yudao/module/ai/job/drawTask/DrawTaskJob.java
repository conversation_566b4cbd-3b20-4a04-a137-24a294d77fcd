package cn.iocoder.yudao.module.ai.job.drawTask;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.ai.service.image.draw.TaskDrawService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 绘画任务 Job
 * 通过不断扫描待通知的 AiImageDO 记录，回调绘画状态
 *
 */
@Component
@Slf4j
public class DrawTaskJob implements JobHandler {

    @Resource
    private TaskDrawService taskDrawService;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        int notifyCount = taskDrawService.executeDrawTask();
        return String.format("执行绘画任务回调 %s 个", notifyCount);
    }

}

package cn.iocoder.yudao.module.ai.dal.mysql.model;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.model.vo.model.AiYyModelPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import org.apache.ibatis.annotations.Mapper;

import javax.annotation.Nullable;
import java.util.List;

/**
 * API 聊天模型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiYyModelMapper extends BaseMapperX<AiYyModelDO> {

    default AiYyModelDO selectFirstByStatus(Integer status) {
        return selectOne(new QueryWrapperX<AiYyModelDO>()
                .eq("status", status)
                .limitN(1)
                .orderByAsc("sort"));
    }

    default PageResult<AiYyModelDO> selectPage(AiYyModelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiYyModelDO>()
                .likeIfPresent(AiYyModelDO::getName, reqVO.getName())
                .eqIfPresent(AiYyModelDO::getModel, reqVO.getModel())
                .eqIfPresent(AiYyModelDO::getPlatform, reqVO.getPlatform())
                .orderByAsc(AiYyModelDO::getSort));
    }

    default List<AiYyModelDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<AiYyModelDO>()
                .eq(AiYyModelDO::getStatus, status)
                .orderByAsc(AiYyModelDO::getSort));
    }

    default List<AiYyModelDO> selectListByStatusAndType(Integer status, @Nullable Integer type,
                                                        @Nullable String platform) {
        return selectList(new LambdaQueryWrapperX<AiYyModelDO>()
                .eq(AiYyModelDO::getStatus, status)
                .eqIfPresent(AiYyModelDO::getType, type)
                .eqIfPresent(AiYyModelDO::getPlatform, platform)
                .orderByAsc(AiYyModelDO::getSort));
    }
}
package cn.iocoder.yudao.module.ai.service.image.config;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppAiImageLabRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.AppResolutionPresetRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.DrawImageConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.app.image.vo.promptwords.AppPromptWordsRespVO;
import cn.iocoder.yudao.module.ai.convert.image.DrawImageConvert;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiImageConfigDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.image.AiModelStyleDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.promptwords.PromptWordsDO;
import cn.iocoder.yudao.module.ai.dal.mysql.image.config.AiImageConfigMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.image.modelstyle.AiModelStyleMapper;
import cn.iocoder.yudao.module.ai.dal.mysql.promptwords.PromptWordsMapper;
import cn.iocoder.yudao.module.ai.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.ai.enums.image.ResolutionPresetEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: TODO
 * @Date: 2024/8/4 21:58
 * @Author: zhangq
 * @Version: 1.0
 */
@Slf4j
@Service
public class DrawImageConfigServiceImpl implements DrawImageConfigService{

    @Resource
    private AiImageConfigMapper imageConfigMapper;

    @Resource
    private AiModelStyleMapper modelStyleMapper;

    @Resource
    private PromptWordsMapper imagePromptWordsMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final int LOCK_TIMEOUT = 5; // 锁超时时间（秒）
    private static final int PAGE_SIZE = 5; // 每页大小

    @Override
    public DrawImageConfigRespVO getDrawImageConfig() {

        //获取绘画风格
        List<AiModelStyleDO> modelStyleDOList = modelStyleMapper.selectList(new LambdaQueryWrapper<AiModelStyleDO>()
                .eq(AiModelStyleDO::getStatus, 0)
                .orderByAsc(AiModelStyleDO::getSort)
        );

        //获取绘画尺寸
        AiImageConfigDO aiImageConfigDO = imageConfigMapper.selectLast();
        //AppAspectRatioPresetRespVO aspectRatioPresetRespVO = DrawImageConvert.INSTANCE.convertAspectRatioPreset(aiImageConfigDO);
        //List<Map<String, String>> aspectRatioPresetRespVO = AspectRatioPresetEnum.getAllCodeList();

       //获取绘画清晰度
        List<AppResolutionPresetRespVO> resolutionPresetDOList = new ArrayList<>();
        if (aiImageConfigDO != null){
            for(ResolutionPresetEnum resolution : ResolutionPresetEnum.values()){
                if (!resolution.getCode().equals(ResolutionPresetEnum.EXTREME.getCode()) && !resolution.getCode().equals(ResolutionPresetEnum.ULTRA.getCode())){
                    AppResolutionPresetRespVO appResolution = getAppResolutionPresetRespVO(resolution, aiImageConfigDO);
                    resolutionPresetDOList.add(appResolution);
                }
            }
         }

       DrawImageConfigRespVO drawImageConfigRespVO = new DrawImageConfigRespVO();
       if (aiImageConfigDO != null){
          drawImageConfigRespVO = DrawImageConvert.INSTANCE.convertDrawImageConfig(aiImageConfigDO);
       }
       drawImageConfigRespVO.setModelStyleList(modelStyleDOList == null ? new ArrayList<>() :DrawImageConvert.INSTANCE.convertModelStyleList(modelStyleDOList));
       //drawImageConfigRespVO.setAspectRatioPreset(aspectRatioPresetRespVO);
       drawImageConfigRespVO.setResolutionPresetList(resolutionPresetDOList);
        return drawImageConfigRespVO;
    }

    @NotNull
    private static AppResolutionPresetRespVO getAppResolutionPresetRespVO(ResolutionPresetEnum resolution, AiImageConfigDO aiImageConfigDO) {
        AppResolutionPresetRespVO appResolution = new AppResolutionPresetRespVO();
        appResolution.setName(resolution.getName());
        appResolution.setResolutionPreset(resolution.getCode());
        if (resolution.getCode().equals(ResolutionPresetEnum.DEFAULT.getCode())){
            appResolution.setAiCoin(aiImageConfigDO.getDrawBaseConsume());
        }else if (resolution.getCode().equals(ResolutionPresetEnum.NORMAL.getCode())){
            appResolution.setAiCoin(aiImageConfigDO.getHighFixConsume());
        }else if (resolution.getCode().equals(ResolutionPresetEnum.HIGH.getCode())){
            appResolution.setAiCoin(aiImageConfigDO.getHighFixSuperConsume());
        }
        return appResolution;
    }

    @Override
    public AppPromptWordsRespVO getInspiration(Long userId, Integer type) {
        //int type = PromptWordsTypeEnum.IMAGE_PROMPT_WORDS.getType();
        RLock lock = redissonClient.getLock(formatLockKey(userId, type));
        try {
            String formatPageKey = formatPageKey(userId, type);
            String formatDataKey = formatDataKey(userId, type);
            if (lock.tryLock(LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                // 从 Redis 获取当前页码
                String currentIndex = stringRedisTemplate.opsForValue().get(formatPageKey);
                Integer index = currentIndex == null ? 1 : Integer.parseInt(currentIndex);

                // 从 Redis 获取当前页的数据
                List<Long> promptWordsList = JsonUtils.parseArray(stringRedisTemplate.opsForValue().get(formatDataKey + ":" + index), Long.class);

                // 如果缓存中没有数据，查询当前页的数据并缓存
                if (promptWordsList == null || promptWordsList.isEmpty()) {
                    promptWordsList = imagePromptWordsMapper.selectList(new LambdaQueryWrapper<PromptWordsDO>().select(PromptWordsDO::getId)
                            .eq(PromptWordsDO::getPromptWordsType, type)
                            .eq(PromptWordsDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                            .orderByAsc(PromptWordsDO::getSort)
                            .last("limit " + (index - 1) * PAGE_SIZE + "," + PAGE_SIZE)
                    ).stream().map(PromptWordsDO::getId).toList();
                    // 如果查询结果为空，说明没有更多数据了，将页码重置为1
                    if (promptWordsList.isEmpty()){
                        stringRedisTemplate.delete(formatPageKey);
                        index = 1;
                        promptWordsList = imagePromptWordsMapper.selectList(new LambdaQueryWrapper<PromptWordsDO>().select(PromptWordsDO::getId)
                                .eq(PromptWordsDO::getPromptWordsType, type)
                                .eq(PromptWordsDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                                .orderByAsc(PromptWordsDO::getSort)
                                .last("limit " + (index - 1) * PAGE_SIZE + "," + PAGE_SIZE)
                        ).stream().map(PromptWordsDO::getId).toList();
                        stringRedisTemplate.opsForValue().set(formatPageKey, String.valueOf(index));
                        // 如果查询结果为空，说明没有数据，返回null
                        if (promptWordsList.isEmpty()){
                            return null;
                        }
                    }
                    stringRedisTemplate.opsForValue().set(formatDataKey + ":" + index, JsonUtils.toJsonString(promptWordsList));
                }

                // 从当前页的数据中取一条记录
                if (promptWordsList.isEmpty()) {
                    // 如果当前页没有数据，切换到下一页
                    index++;
                    stringRedisTemplate.opsForValue().set(formatPageKey, String.valueOf(index));
                    stringRedisTemplate.delete(formatDataKey + ":" + (index - 1)); // 删除上一页的缓存数据
                    return getInspiration(userId, type); // 递归调用以获取下一页的数据
                }

                // 获取当前索引并更新为下一条记录
                List<Long> promptIds = new ArrayList<>(promptWordsList);
                Long promptWordsDO = promptIds.remove(0); // 取出并移除第一条数据
                if (promptIds.isEmpty()) {
                    // 如果当前页数据已经取完，清空缓存
                    stringRedisTemplate.delete(formatDataKey + ":" + index);
                    index++;
                    stringRedisTemplate.opsForValue().set(formatPageKey, String.valueOf(index));
                } else {
                    // 更新缓存中的数据
                    stringRedisTemplate.opsForValue().set(formatDataKey + ":" + index, JsonUtils.toJsonString(promptIds));
                }
                PromptWordsDO promptWords = imagePromptWordsMapper.selectById(promptWordsDO);
                // 将 PromptWordsDO 转换为 AppPromptWordsRespVO
                return BeanUtils.toBean(promptWords, AppPromptWordsRespVO.class);
            } else {
                throw new RuntimeException("Failed to acquire lock");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Lock acquisition interrupted", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<AppPromptWordsRespVO> getQuality(Long userId,Integer type) {
        //int type = PromptWordsTypeEnum.IMAGE_QUALITY_PROMPT_WORDS.getType();
        RLock lock = redissonClient.getLock(formatLockKey(userId, type));
        try {
            String formatPageKey = formatPageKey(userId, type);
            if (lock.tryLock(LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                // 从 Redis 获取当前页码
                String currentIndex = stringRedisTemplate.opsForValue().get(formatPageKey);
                Integer index = currentIndex == null ? 1 : Integer.parseInt(currentIndex);

                List<PromptWordsDO> promptWords = imagePromptWordsMapper.selectList(new LambdaQueryWrapper<PromptWordsDO>()
                        .eq(PromptWordsDO::getPromptWordsType, type)
                        .eq(PromptWordsDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                        .orderByAsc(PromptWordsDO::getSort)
                        .last("limit " + (index - 1) * PAGE_SIZE + "," + PAGE_SIZE)
                );

                // 如果查询结果为空，说明没有更多数据了，将页码重置为1
                if (promptWords.isEmpty()) {
                    stringRedisTemplate.delete(formatPageKey);
                    index = 1;
                    promptWords = imagePromptWordsMapper.selectList(new LambdaQueryWrapper<PromptWordsDO>()
                            .eq(PromptWordsDO::getPromptWordsType, type)
                            .eq(PromptWordsDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                            .orderByAsc(PromptWordsDO::getSort)
                            .last("limit " + (index - 1) * PAGE_SIZE + "," + PAGE_SIZE)
                    );
                    stringRedisTemplate.opsForValue().set(formatPageKey, String.valueOf(index));
                    // 如果查询结果为空，说明没有数据，返回null
                    if (promptWords.isEmpty()){
                        return null;
                    }
                }

                if (!promptWords.isEmpty()) {
                    index++;
                    stringRedisTemplate.opsForValue().set(formatPageKey, String.valueOf(index));
                }

                // 将 PromptWordsDO 转换为 AppPromptWordsRespVO
                return BeanUtils.toBean(promptWords, AppPromptWordsRespVO.class);
            } else {
                throw new RuntimeException("Failed to acquire lock");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Lock acquisition interrupted", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<AppAiImageLabRespVO> getAiImageLab() {
        List<AppAiImageLabRespVO> respVOS = new ArrayList<>();
        //绘画配置
        AiImageConfigDO aiImageConfigDO = imageConfigMapper.selectLast();
        if (aiImageConfigDO != null) {
            respVOS.add(new AppAiImageLabRespVO()
                    .setCoverImage(aiImageConfigDO.getCoverImage())
                    .setName(aiImageConfigDO.getName())
                    .setDescription(aiImageConfigDO.getDescription())
            );
        }
        return respVOS;
    }


    private static String formatDataKey(Long userId, Integer type) {
        return String.format(RedisKeyConstants.PROMPT_WORDS_DATA, userId, type);
    }

    public static String formatPageKey(Long userId, Integer type) {
        return String.format(RedisKeyConstants.PROMPT_WORDS_PAGE, userId,type);
    }

    public static String formatLockKey(Long userId, Integer type) {
        return String.format(RedisKeyConstants.PROMPT_WORDS_LOCK, userId, type);
    }
}

package cn.iocoder.yudao.module.ai.controller.app.image.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/8/4 21:35
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class AppResolutionPresetRespVO {

    @Schema(description = "消耗算力")
    private Integer aiCoin;

    @Schema(description = "清晰度名称")
    private String name;

    @Schema(description = "normal high ultra extreme 说明 一般清晰度(快速出图) 高清(一般耗时) 超清(耗时")
    private String resolutionPreset;
}

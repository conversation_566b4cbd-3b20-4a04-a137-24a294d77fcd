package cn.iocoder.yudao.module.ai.dal.mysql.invite;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteUserDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AiInviteUserMapper extends BaseMapperX<AiInviteUserDO> {

    default AiInviteUserDO selectByInviteCode(String inviteCode) {
        return selectOne(AiInviteUserDO::getInviteCode, inviteCode, AiInviteUserDO::getInviteEnabled,Boolean.TRUE);
    }
}

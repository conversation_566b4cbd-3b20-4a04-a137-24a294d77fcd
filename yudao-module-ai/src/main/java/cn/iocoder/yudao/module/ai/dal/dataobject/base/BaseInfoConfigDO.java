package cn.iocoder.yudao.module.ai.dal.dataobject.base;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * 基础配置 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_base_info_config")
@Comment(value = "AI基础配置")
@Entity
@TableName("ai_base_info_config")
@KeySequence("ai_base_info_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseInfoConfigDO extends TenantBaseDO {

    /**
     * 编号，唯一自增
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;
    /**
     * 被好友成功邀请，赠送算力
     */
    @Column(columnDefinition = "int COMMENT '被好友成功邀请，赠送算力'")
    private Integer inviteFriendGive;
    /**
     * 成功邀请好友，赠送算力
     */
    @Column(columnDefinition = "int COMMENT '成功邀请好友，赠送算力'")
    private Integer inviteGive;
    /**
     * 新用户注册赠送算力
     */
    @Column(columnDefinition = "int COMMENT '新用户注册赠送算力'")
    private Integer registerGive;

    /**
     * 苹果应用商店ID
     */
    @Column(columnDefinition = "varchar(255) COMMENT '苹果应用商店ID'")
    private String appStoreId;

    /**
     * 联系客服配置
     */
    @Column(length = 500,columnDefinition = "varchar(500) COMMENT '联系客服配置'")
    private String contactCustomer;
}
package cn.iocoder.yudao.module.ai.controller.app.virtualpartner.vo.message;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2025/1/4 22:32
 * @Author: zhangq
 * @Version: 1.0
 */
@Schema(description = "用户APP - AI 聊天消息点赞/点踩 Request VO")
@Data
public class AppVirtualPartnerMessagePraiseReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "点赞类型 0取消 点赞1 点踩2", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "点赞类型不能为空")
    private Integer praiseType;

}

package cn.iocoder.yudao.module.ai.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * AI 错误码枚举类
 * <p>
 * ai 系统，使用 1-040-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== API 密钥 1-040-000-000 ==========
    ErrorCode API_KEY_NOT_EXISTS = new ErrorCode(1_040_000_000, "API 密钥不存在");
    ErrorCode API_KEY_DISABLE = new ErrorCode(1_040_000_001, "API 密钥已禁用！");

    // ========== API 模型 1-040-001-000 ==========
    ErrorCode MODEL_NOT_EXISTS = new ErrorCode(1_040_001_000, "模型不存在!");
    ErrorCode MODEL_DISABLE = new ErrorCode(1_040_001_001, "模型({})已禁用!");
    ErrorCode MODEL_DEFAULT_NOT_EXISTS = new ErrorCode(1_040_001_002, "操作失败，找不到默认模型");
    ErrorCode MODEL_USE_TYPE_ERROR = new ErrorCode(1_040_001_003, "操作失败，该模型的模型类型不正确");

    // ========== API 聊天角色 1-040-002-000 ==========
    ErrorCode CHAT_ROLE_NOT_EXISTS = new ErrorCode(1_040_002_000, "聊天角色不存在");
    ErrorCode CHAT_ROLE_DISABLE = new ErrorCode(1_040_001_001, "聊天角色({})已禁用!");

    // ========== API 聊天会话 1-040-003-000 ==========
    ErrorCode CHAT_CONVERSATION_NOT_EXISTS = new ErrorCode(1_040_003_000, "对话不存在!");
    ErrorCode CHAT_CONVERSATION_MODEL_ERROR = new ErrorCode(1_040_003_001, "操作失败，该聊天模型的配置不完整");

    // ========== API 聊天消息 1-040-004-000 ==========
    ErrorCode CHAT_MESSAGE_NOT_EXIST = new ErrorCode(1_040_004_000, "消息不存在!");
    ErrorCode CHAT_STREAM_ERROR = new ErrorCode(1_040_004_001, "对话生成异常!");

    // ========== API 绘画 1-040-005-000 ==========
    ErrorCode IMAGE_NOT_EXISTS = new ErrorCode(1_040_005_000, "图片不存在!");
    ErrorCode IMAGE_MIDJOURNEY_SUBMIT_FAIL = new ErrorCode(1_040_005_001, "Midjourney 提交失败!原因：{}");
    ErrorCode IMAGE_CUSTOM_ID_NOT_EXISTS = new ErrorCode(1_040_005_002, "Midjourney 按钮 customId 不存在! {}");

    // ========== API 音乐 1-040-006-000 ==========
    ErrorCode MUSIC_NOT_EXISTS = new ErrorCode(1_040_006_000, "音乐不存在!");

    // ========== API 写作 1-040-007-000 ==========
    ErrorCode WRITE_NOT_EXISTS = new ErrorCode(1_040_007_000, "作文不存在!");
    ErrorCode WRITE_STREAM_ERROR = new ErrorCode(1_040_07_001, "写作生成异常!");

    // ========== API 思维导图 1-040-008-000 ==========
    ErrorCode MIND_MAP_NOT_EXISTS = new ErrorCode(1_040_008_000, "思维导图不存在!");

    // ========== API 知识库 1-040-009-000 ==========
    ErrorCode KNOWLEDGE_NOT_EXISTS = new ErrorCode(1_040_009_000, "知识库不存在!");

    ErrorCode KNOWLEDGE_DOCUMENT_NOT_EXISTS = new ErrorCode(1_040_009_101, "文档不存在!");
    ErrorCode KNOWLEDGE_DOCUMENT_FILE_EMPTY = new ErrorCode(1_040_009_102, "文档内容为空!");
    ErrorCode KNOWLEDGE_DOCUMENT_FILE_DOWNLOAD_FAIL = new ErrorCode(1_040_009_102, "文件下载失败!");
    ErrorCode KNOWLEDGE_DOCUMENT_FILE_READ_FAIL = new ErrorCode(1_040_009_102, "文档加载失败!");

    ErrorCode KNOWLEDGE_SEGMENT_NOT_EXISTS = new ErrorCode(1_040_009_202, "段落不存在!");
    ErrorCode KNOWLEDGE_SEGMENT_CONTENT_TOO_LONG = new ErrorCode(1_040_009_203, "内容 Token 数为 {}，超过最大限制 {}");

    // ========== AI 工具 1-040-010-000 ==========
    ErrorCode TOOL_NOT_EXISTS = new ErrorCode(1_040_010_000, "工具不存在");
    ErrorCode TOOL_NAME_NOT_EXISTS = new ErrorCode(1_040_010_001, "工具({})找不到 Bean");

    // ========== AI 工作流 1-040-011-000 ==========
    ErrorCode WORKFLOW_NOT_EXISTS = new ErrorCode(1_040_011_000, "工作流不存在");
    ErrorCode WORKFLOW_CODE_EXISTS = new ErrorCode(1_040_011_001, "工作流标识已存在");


    //================================新增==================================================================
    // ========== 系统敏感词 1-002-019-000 =========
    ErrorCode SENSITIVE_WORD_NOT_EXISTS = new ErrorCode(1_002_019_000, "系统敏感词在所有标签中都不存在");
    ErrorCode SENSITIVE_WORD_EXISTS = new ErrorCode(1_002_019_001, "系统敏感词已在标签中存在");
    ErrorCode SENSITIVE_WORD_IS_EXISTS = new ErrorCode(1_002_019_002, "包含敏感词");
    ErrorCode SENSITIVE_WORD_IS_EXISTS_APP = new ErrorCode(1_002_019_003, "发送内容违规啦！请文明使用哦～");
    //用户未登录
    ErrorCode USER_NOT_LOGIN = new ErrorCode(1_002_020_002, "用户未登录");
    //========== 提示词 9_003_000 ==========
    ErrorCode IMAGE_PROMPT_WORDS_NOT_EXISTS = new ErrorCode(9_003_001, "提示词不存在");

    // ========== 基础配置 9_004_0000 ==========
    ErrorCode BASE_INFO_CONFIG_NOT_EXISTS = new ErrorCode(9_004_0001, "基础配置不存在");

    // ========== 绘画基础配置 9_005_0000 ==========
    ErrorCode IMAGE_CONFIG_NOT_EXISTS = new ErrorCode(9_005_0001, "绘画基础配置不存在");

    // ========== 风格模型 9_006_000 ==========
    ErrorCode MODEL_STYLE_NOT_EXISTS = new ErrorCode(9_006_001, "风格模型不存在");

    //当前排队处理任务数达到最大数~请等待任务执行完成
    //======触站请求返回错误码============
    //服务器内部错误
    ErrorCode SERVER_ERROR = new ErrorCode(9_999_9999, "服务器内部错误");
    //参数错误
    ErrorCode PARAM_ERROR = new ErrorCode(9_999_9998, "参数错误");
    //词条中涉及敏感词汇，禁止提交任务
    ErrorCode SENSITIVE_WORD_ERROR = new ErrorCode(9_999_9997, "词条中涉及敏感词汇，禁止提交任务");
    //任务数达到上限，请等在运行中的任务完成后再进行提交
    ErrorCode TASK_NUM_LIMIT_ERROR = new ErrorCode(9_999_9996, "任务数达到上限，请等在运行中的任务完成后再进行提交");
    ErrorCode AI_DRAW_TASK_IS_QUEUE_FULL = new ErrorCode(9_000_0011, "当前排队处理任务数达到最大数:3 ~请等待任务执行完成");
    ErrorCode AI_IMAGE_CONFIG_NOT_EXISTS = new ErrorCode(9_000_0012, "绘画基础配置不存在");
    //绘图任务不存在
    ErrorCode AI_DRAW_TASK_NOT_EXISTS = new ErrorCode(9_000_0012, "绘图任务不存在");
    //
    ErrorCode IMAGE_REVOKE_ERROR = new ErrorCode(1_022_005_003, "撤回失败!原因：{}");
    //清晰度预设不存在
    ErrorCode AI_IMAGE_RESOLUTION_PRESET_NOT_EXISTS = new ErrorCode(9_000_0013, "清晰度预设不存在");
    //脸部修复无默认值
    ErrorCode AI_FACE_FIX_NOT_EXISTS = new ErrorCode(9_000_0014, "脸部修复无默认值");
    //单次生成数量不能小于1
    ErrorCode AI_IMAGE_BATCH_SIZE_NOT_EXISTS = new ErrorCode(9_000_0015, "单次生成数量不能小于1");

    // ========== Banner 相关 9_001_0001 ============
    ErrorCode BANNER_NOT_EXISTS = new ErrorCode(9_001_0001, "Banner 不存在");

    // ========== 投诉/反馈 TODO 补充编号 ==========
    ErrorCode COMPLAINT_FEEDBACK_NOT_EXISTS = new ErrorCode(9_002_0001, "投诉/反馈不存在");

    ErrorCode GALLERY_COMMON_NOT_EXISTS = new ErrorCode(9_003_0001, "绘画广场信息不存在");
    ErrorCode GALLERY_CONFIG_NOT_EXISTS = new ErrorCode(9_003_0002, "绘画广场配置不存在");

    // ========== 绘画广场相关 ==========
    ErrorCode GALLERY_IMAGE_NOT_EXISTS = new ErrorCode(9_003_0003, "绘画失败！️");

    //文本信息不存在
    ErrorCode TEXT_NOT_EXISTS = new ErrorCode(1_500_010_001, "文本信息不存在");
    // ========== 音色模型 1-500-009-000 ==========
    ErrorCode AUDIO_MODEL_NOT_EXISTS = new ErrorCode(1_500_009_000, "音色模型不存在");
    // ========== 语音配置 1_500_010_000 ==========
    ErrorCode AUDIO_CONFIG_NOT_EXISTS = new ErrorCode(1_500_010_000, "语音配置不存在");
    // ========== 虚拟陪伴角色 1_500_013_000 ==========
    ErrorCode VIRTUAL_PARTNER_ROLE_NOT_EXISTS = new ErrorCode(1_500_013_000, "虚拟陪伴角色不存在");
    ErrorCode VIRTUAL_PARTNER_ROLE_NOT_EXISTS_BY_ID = new ErrorCode(1_500_013_001, "虚拟陪伴角色Id不能为空");
    // ========== 虚拟陪伴对话 1_500_014_000 ==========

    // ========== 内容创作配置 相关 1_500_040_000 ============
    //内容创作配置不存在
    ErrorCode CONTENT_WRITE_CONFIG_NOT_EXISTS = new ErrorCode(1_500_040_000, "内容创作配置不存在");

    // ========== 内容创作角色 相关 1_500_041_000 ============
    //内容创作角色不存在
    ErrorCode CONTENT_WRITE_ROLE_NOT_EXISTS = new ErrorCode(1_500_041_000, "内容创作角色不存在");

    // ========== 内容创作分类相关 1_500_042_000 ==========
    //内容创作分类不存在
    ErrorCode CONTENT_WRITE_CATEGORY_NOT_EXISTS = new ErrorCode(1_500_042_000, "内容创作分类不存在");

    // ========== 内容创作对话 相关 1_500_043_000 ==========
    ErrorCode CONTENT_WRITE_CONVERSATION_NOT_EXISTS = new ErrorCode(1_500_043_000, "内容创作对话不存在");
    ErrorCode CONTENT_WRITE_CONVERSATION_NOT_EXISTS_BY_ID = new ErrorCode(1_500_043_001, "内容创作对话Id不能为空");

    // ========== 内容创作 消息 相关 1_500_044_000 ==========
    ErrorCode CONTENT_WRITE_MESSAGE_NOT_EXISTS = new ErrorCode(1_500_044_000, "内容创作消息不存在");

    // ========== 算力规则配置 9_100_0000 ==========
    ErrorCode COIN_ROLE_CONFIG_NOT_EXISTS = new ErrorCode(9_100_0000, "算力规则配置不存在");
    // ========== 邀请设置 9_101_0000  ==========
    ErrorCode INVITE_CONFIG_NOT_EXISTS = new ErrorCode(9_101_0000, "邀请设置不存在");
}

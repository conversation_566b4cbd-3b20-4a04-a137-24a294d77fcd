package cn.iocoder.yudao.module.ai.controller.app.image.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/8/4 21:51
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class AppAspectRatioPresetRespVO {

    @Schema(description = "竖图宽度")
    private Integer portraitWidth;

    @Schema(description = "竖图高度")
    private Integer portraitHeight;

    @Schema(description = "方图宽度")
    private Integer squareWidth;

    @Schema(description = "方图高度")
    private Integer squareHeight;

    @Schema(description = "横图宽度")
    private Integer landscapeWidth;

    @Schema(description = "横图高度")
    private Integer landscapeHeight;
}

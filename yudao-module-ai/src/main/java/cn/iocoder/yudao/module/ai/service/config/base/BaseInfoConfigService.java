package cn.iocoder.yudao.module.ai.service.config.base;

import cn.iocoder.yudao.module.ai.controller.admin.config.base.vo.BaseInfoConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.base.BaseInfoConfigDO;
import jakarta.validation.Valid;

/**
 * 基础配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseInfoConfigService {

    /**
     * 创建基础配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveOrUpdateBaseInfoConfig(@Valid BaseInfoConfigSaveReqVO createReqVO);

    /**
     * 获得基础配置
     *
     * @return 基础配置
     */
    BaseInfoConfigDO getSimpleBaseInfoConfig();

    /**
     * 【系统】给用户发送算力
     *
     * @param userId 用户编号
     */
    void sendCoinByRegister(Long userId);
}
package cn.iocoder.yudao.module.ai.service.gallery;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.gallery.vo.GalleryConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.gallery.GalleryConfigDO;
import cn.iocoder.yudao.module.ai.dal.mysql.gallery.GalleryConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.GALLERY_CONFIG_NOT_EXISTS;

/**
 * AI 画廊配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GalleryConfigServiceImpl implements GalleryConfigService {

    @Resource
    private GalleryConfigMapper galleryConfigMapper;

    @Override
    public Long saveOrUpdateGalleryConfig(GalleryConfigSaveReqVO createReqVO) {
        // 插入
        GalleryConfigDO galleryConfig = BeanUtils.toBean(createReqVO, GalleryConfigDO.class);
        if (null == createReqVO.getId() || createReqVO.getId() == 0){
            galleryConfigMapper.insert(galleryConfig);
        }else{
            // 校验存在
            validateGalleryConfigExists(createReqVO.getId());
            galleryConfigMapper.updateById(galleryConfig);
        }
        // 返回
        return galleryConfig.getId();
    }

    @Override
    public void deleteGalleryConfig(Long id) {
        // 校验存在
        validateGalleryConfigExists(id);
        // 删除
        galleryConfigMapper.deleteById(id);
    }

    private void validateGalleryConfigExists(Long id) {
        if (galleryConfigMapper.selectById(id) == null) {
            throw exception(GALLERY_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public GalleryConfigDO getGalleryConfig() {
        return galleryConfigMapper.selectDefaultConfig();
    }

    @Override
    public PageResult<GalleryConfigDO> getGalleryConfigPage(GalleryConfigPageReqVO pageReqVO) {
        return galleryConfigMapper.selectPage(pageReqVO);
    }

}
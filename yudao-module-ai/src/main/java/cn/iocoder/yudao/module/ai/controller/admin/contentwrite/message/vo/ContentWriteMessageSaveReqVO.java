package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - AI 内容创作 消息新增/修改 Request VO")
@Data
public class ContentWriteMessageSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "30654")
    private Long id;

    @Schema(description = "算力")
    private Integer aiCoin;

    @Schema(description = "聊天内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "聊天内容不能为空")
    private String content;

    @Schema(description = "对话编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3386")
    @NotNull(message = "对话编号不能为空")
    private Long conversationId;

    @Schema(description = "输入命中敏感词")
    private Boolean inputSensitive;

    @Schema(description = "输入敏感词类型", example = "2")
    private Integer inputSensitiveType;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "模型标志不能为空")
    private String model;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12500")
    @NotNull(message = "模型编号不能为空")
    private Long modelId;

    @Schema(description = "输出命中敏感词")
    private Boolean outputSensitive;

    @Schema(description = "输出敏感词类型", example = "2")
    private Integer outputSensitiveType;

    @Schema(description = "点赞类型 点赞1 点踩2", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "点赞类型 点赞1 点踩2不能为空")
    private Integer praiseType;

    @Schema(description = "回复消息编号", example = "26860")
    private Long replyId;

    @Schema(description = "请求完整参数")
    private String requestParams;

    @Schema(description = "响应完整参数")
    private String responseParams;

    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "消息类型不能为空")
    private String type;

    @Schema(description = "是否携带上下文", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否携带上下文不能为空")
    private Boolean useContext;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18130")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

}
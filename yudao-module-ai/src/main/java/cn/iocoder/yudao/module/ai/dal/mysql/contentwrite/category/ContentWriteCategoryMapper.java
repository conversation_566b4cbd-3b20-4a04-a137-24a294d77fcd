package cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.category;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.category.vo.ContentWriteCategoryPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI 内容创作分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentWriteCategoryMapper extends BaseMapperX<AiContentWriteCategoryDO> {

    default PageResult<AiContentWriteCategoryDO> selectPage(ContentWriteCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiContentWriteCategoryDO>()
                .betweenIfPresent(AiContentWriteCategoryDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(AiContentWriteCategoryDO::getName, reqVO.getName())
                .eqIfPresent(AiContentWriteCategoryDO::getSort, reqVO.getSort())
                .eqIfPresent(AiContentWriteCategoryDO::getStatus, reqVO.getStatus())
                .orderByDesc(AiContentWriteCategoryDO::getId));
    }

    default List<AiContentWriteCategoryDO> selectList(Integer status) {
        return selectList(new LambdaQueryWrapperX<AiContentWriteCategoryDO>()
                .eq(AiContentWriteCategoryDO::getStatus, status)
                .orderByAsc(AiContentWriteCategoryDO::getId));
    }
}
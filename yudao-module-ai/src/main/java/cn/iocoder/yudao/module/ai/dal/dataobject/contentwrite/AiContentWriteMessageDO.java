package cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.assistant.AiAssistantConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyModelDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.springframework.ai.chat.messages.MessageType;

/**
 * AI 内容创作 消息 DO
 *
 */
@Table(name = "ai_content_write_message")
@Comment(value = "AI 内容创作 消息")
@Entity
@TableName("ai_content_write_message")
@KeySequence("ai_content_write_message_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiContentWriteMessageDO extends TenantBaseDO {

    /**
     * 编号，作为每条聊天记录的唯一标识符
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 对话编号
     *
     * 关联 {@link AiAssistantConversationDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '对话编号'")
    private Long conversationId;
    /**
     * 回复消息编号
     *
     * 关联 {@link #id} 字段
     *
     * 大模型回复的消息编号，用于“问答”的关联
     */
    @Column(columnDefinition = "bigint COMMENT '回复消息编号'")
    private Long replyId;

    /**
     * 消息类型
     *
     * 也等价于 OpenAPI 的 role 字段
     *
     * 枚举 {@link MessageType}
     */
    @Column(length = 16, columnDefinition = "varchar(16) NOT NULL COMMENT '消息类型'")
    private String type;
    /**
     * 用户编号
     *
     * 关联 AdminUserDO 的 userId 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '用户编号'")
    private Long userId;

    /**
     * 模型标志
     */
    @Column(length = 32, columnDefinition = "varchar(32) NOT NULL COMMENT '模型标志'")
    private String model;
    /**
     * 模型编号
     *
     * 关联 {@link AiYyModelDO#getId()} 字段
     */
    @Column(columnDefinition = "bigint NOT NULL COMMENT '模型编号'")
    private Long modelId;

    /**
     * 聊天内容
     */
    @Column(columnDefinition = "text NOT NULL COMMENT '聊天内容'")
    private String content;

    /**
     * 是否携带上下文
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '是否携带上下文'")
    private Boolean useContext;

    /**
     * 点赞类型 点赞类型 点赞1 点踩2
     *
     */
    @Column(columnDefinition = "int NOT NULL DEFAULT '0' COMMENT '点赞类型 点赞1 点踩2'")
    private Integer praiseType;

    /**
     * 算力
     */
    @Column(columnDefinition = "int COMMENT '算力'")
    private Integer aiCoin;

    /**
     * 输入命中敏感词
     */
    @Column(columnDefinition = "bit(1) DEFAULT b'0' COMMENT '输入命中敏感词'")
    private Boolean inputSensitive;

    /**
     * 输出命中敏感词
     *
     */
    @Column(columnDefinition = "bit(1) DEFAULT b'0' COMMENT '输出命中敏感词'")
    private Boolean outputSensitive;

    /**
     * 输入敏感词类型
     * 输入命中敏感词类型，当input_sensitive为true时返回。取值为以下其一：1 严重违规；2 色情；3 广告；4 违禁；5 谩骂；6 暴恐；7 其他。
     */
    @Column(columnDefinition = "int COMMENT '输入敏感词类型'")
    private Integer inputSensitiveType;

    /**
     * 输出敏感词类型
     * 输出命中敏感词类型，当output_sensitive为true时返回。取值为以下其一：1 严重违规；2 色情；3 广告；4 违禁；5 谩骂；6 暴恐；7 其他。
     */
    @Column(columnDefinition = "int COMMENT '输出敏感词类型'")
    private Integer outputSensitiveType;

    /**
     * 请求消耗tokens
     */

    /**
     * 请求完整参数
     */
    @Column(columnDefinition = "text COMMENT '请求完整参数'")
    private String requestParams;

    @Column(columnDefinition = "text COMMENT '响应完整参数'")
    private String responseParams;
}

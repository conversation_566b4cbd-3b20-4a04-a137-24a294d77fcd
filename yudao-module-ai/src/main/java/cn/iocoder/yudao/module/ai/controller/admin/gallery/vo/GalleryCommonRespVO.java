package cn.iocoder.yudao.module.ai.controller.admin.gallery.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - AI 公开画廊 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GalleryCommonRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "11971")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "封面", example = "https://www.iocoder.cn")
    @ExcelProperty("封面")
    private String coverUrl;

    @Schema(description = "同款图")
    @ExcelProperty("同款图")
    private List<String> imgUrls;

    @Schema(description = "提示词")
    @ExcelProperty("提示词")
    private String prompt;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private Integer status;

}
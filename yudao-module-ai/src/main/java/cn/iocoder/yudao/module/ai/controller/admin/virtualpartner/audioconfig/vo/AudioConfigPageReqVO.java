package cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 语音配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AudioConfigPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "语音文件")
    private String audioFile;

    @Schema(description = "音频时长")
    private Long audioLength;

    @Schema(description = "音频采样率")
    private Long audioSampleRate;

    @Schema(description = "音频大小")
    private Long audioSize;

    @Schema(description = "音频比特率")
    private Long bitrate;

    @Schema(description = "文字内容")
    private String content;

    @Schema(description = "生成错误信息")
    private String errorMessage;

    @Schema(description = "完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] finishTime;

    @Schema(description = "非法字符占比")
    private Double invisibleCharacterRatio;

    @Schema(description = "模型标志")
    private String model;

    @Schema(description = "模型编号", example = "3256")
    private Long modelId;

    @Schema(description = "输出格式")
    private String outputFormat;

    @Schema(description = "模型平台")
    private String platform;

    @Schema(description = "生成状态", example = "2")
    private Integer status;

    @Schema(description = "字幕文件")
    private String subtitleFile;

    @Schema(description = "消费字符数")
    private Long usageCharacters;

    @Schema(description = "音色编号", example = "22446")
    private String voiceId;

    @Schema(description = "多音色列表")
    private List<Map<String,String>> voiceList;

    @Schema(description = "音色名称", example = "张三")
    private String voiceName;

    @Schema(description = "可读字数", example = "23758")
    private Long wordCount;

    @Schema(description = "是否启用")
    private Integer enable;

}
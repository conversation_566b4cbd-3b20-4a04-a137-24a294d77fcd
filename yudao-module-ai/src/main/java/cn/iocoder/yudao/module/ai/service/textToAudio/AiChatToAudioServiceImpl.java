package cn.iocoder.yudao.module.ai.service.textToAudio;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServerException;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.virtualpartner.audioconfig.vo.AudioConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.controller.app.textToAudio.message.AppAiTextToAudioMessageRespVO;
import cn.iocoder.yudao.module.ai.controller.app.textToAudio.message.AppAiTextToAudioReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.model.AiYyApiKeyDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.textToAudio.AiTextToAudioMessageDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiAudioConfigDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerMessageDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.virtualpartner.AiVirtualPartnerRoleDO;
import cn.iocoder.yudao.module.ai.dal.mysql.textToAudio.AiTextToAudioMapper;
import cn.iocoder.yudao.module.ai.enums.image.AiImageStatusEnum;
import cn.iocoder.yudao.module.ai.enums.model.AiPlatformEnum;
import cn.iocoder.yudao.module.ai.service.model.AiYyApiKeyService;
import cn.iocoder.yudao.module.ai.service.virtualpartner.audioconfig.AudioConfigService;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.AiVirtualPartnerConversationService;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.AiVirtualPartnerMessageService;
import cn.iocoder.yudao.module.ai.service.virtualpartner.virtualpartner.VirtualPartnerRoleService;
import cn.iocoder.yudao.module.ai.util.HEX;
import cn.iocoder.yudao.module.ai.util.MiniMaxT2AUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.minimaxi.platform.pro.api.MinMaxT2AProApi;
import com.minimaxi.platform.t2a.api.MinMaxT2AApi;
import com.minimaxi.platform.t2a.api.MiniMaxT2AModel;
import com.minimaxi.platform.t2a.api.MiniMaxT2AOptions;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.CHAT_MESSAGE_NOT_EXIST;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.MODEL_NOT_EXISTS;

/**
 * AI 聊天信息转语音信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiChatToAudioServiceImpl implements AiChatToAudioService {

    @Resource
    private AiTextToAudioMapper textToAudioMapper;

    @Resource
    private AiYyApiKeyService yyApiKeyService;

    @Resource
    private FileApi fileApi;

    @Resource
    private AiVirtualPartnerMessageService virtualPartnerMessageService;

    @Resource
    private VirtualPartnerRoleService virtualPartnerRoleService;

    @Resource
    private AiVirtualPartnerConversationService virtualPartnerConversationService;

    @Resource
    private AudioConfigService audioConfigService;

    /**
     * @param reqVO
     * @param userId 用户ID
     * @return
     */
    @Override
    public AppAiTextToAudioMessageRespVO textToVoice(AppAiTextToAudioReqVO reqVO, Long userId) {
        // 1. 校验消息是否存在
        AiVirtualPartnerMessageDO virtualPartnerMessage = virtualPartnerMessageService.validateVirtualPartnerMessageExists(reqVO.getMessageId(), userId);
        AiVirtualPartnerConversationDO virtualPartnerConversation =virtualPartnerConversationService.getVirtualPartnerConversation(virtualPartnerMessage.getConversationId());
        AiVirtualPartnerRoleDO virtualPartnerRole = virtualPartnerRoleService.getVirtualPartnerRole(virtualPartnerConversation.getRoleId());
        AiAudioConfigDO audioConfig = audioConfigService.getAudioConfig(virtualPartnerRole.getAudioId());
        //获取APIKEY
        AiYyApiKeyDO aiApiKeyDO = yyApiKeyService.getApiKeyDOImageModel(AiPlatformEnum.validatePlatform(audioConfig.getPlatform()));
        if (aiApiKeyDO == null || !aiApiKeyDO.getStatus().equals(CommonStatusEnum.ENABLE.getStatus())){
            throw exception(MODEL_NOT_EXISTS);
        }
        if (StrUtil.isEmpty(reqVO.getText())){
            throw exception(CHAT_MESSAGE_NOT_EXIST);
        }

        AiTextToAudioMessageDO textToAudioMessageDO = textToAudioMapper.selectOne( new LambdaQueryWrapperX<AiTextToAudioMessageDO>()
                .eq(AiTextToAudioMessageDO::getMessageId,reqVO.getMessageId())
                .eq(AiTextToAudioMessageDO::getUserId,userId)
                .eq(AiTextToAudioMessageDO::getStatus,AiImageStatusEnum.SUCCESS.getStatus())
                .orderByDesc(AiTextToAudioMessageDO::getId)
                .last("limit 1")
        );
        // 判断是否已经生成过语音
        if (textToAudioMessageDO != null){
            AppAiTextToAudioMessageRespVO appAiTextToAudioMessageRespVO = BeanUtils.toBean(textToAudioMessageDO,AppAiTextToAudioMessageRespVO.class);
            appAiTextToAudioMessageRespVO.setAudioUrl(textToAudioMessageDO.getAudioFile());
            return appAiTextToAudioMessageRespVO;
        }

        MinMaxT2AApi api;
        if (StrUtil.isEmpty(aiApiKeyDO.getUrl())){
            api = new MinMaxT2AApi(aiApiKeyDO.getApiKey());
        }else{
            api = new MinMaxT2AApi( aiApiKeyDO.getUrl() ,aiApiKeyDO.getApiKey());
        }

        AudioConfigSaveReqVO audioConfigSaveReqVO = BeanUtils.toBean(audioConfig,AudioConfigSaveReqVO.class);
        audioConfigSaveReqVO.setContent(removeBracketContent(reqVO.getText()));
        List<Map<String, String>> listOfMaps =  audioConfig.getVoiceList().stream().map(voice -> {
            Map<String, String> map = new HashMap<>();
            map.put("voiceId", voice.getVoiceId());
            map.put("weight", String.valueOf(voice.getWeight()));
            return map;
        }).collect(Collectors.toList());
        audioConfigSaveReqVO.setVoiceList(listOfMaps);
        MiniMaxT2AOptions option = MiniMaxT2AUtils.buildMiniMaxT2AOptions(audioConfigSaveReqVO,false);

        MiniMaxT2AModel t2AModel = new MiniMaxT2AModel(api, option);
        // 2. 插入 user 发送消息
        AiTextToAudioMessageDO textToAudioMessage = createTextToAudioMessage(reqVO, userId);

        MinMaxT2AApi.T2ACompletion chatResponse = null;
        try {
            chatResponse = t2AModel.call(aiApiKeyDO.getSign());
        } catch (Exception e) {
            throw new ServerException(500,e.getMessage());
        }

        //响应处理
        LocalDateTime finishTime = null;
        AppAiTextToAudioMessageRespVO respVO = null;
        if (Objects.nonNull(chatResponse)){
            log.info("[chatResponse] {}", chatResponse.baseResponse());
            if (chatResponse.baseResponse() != null && chatResponse.baseResponse().statusCode() != 0) {
                textToAudioMapper.updateById(new AiTextToAudioMessageDO().setId(textToAudioMessage.getId())
                        .setStatus(AiImageStatusEnum.FAIL.getStatus())
                        .setErrorMessage(chatResponse.baseResponse().message()).setFinishTime(LocalDateTime.now()));
                log.warn("[chatResponse] {}", chatResponse.baseResponse().message());
            }
            finishTime = LocalDateTime.now();
            MinMaxT2AApi.T2ACompletion.ExtraInfo extraInfo=  chatResponse.extraInfo();

            // 2. 上传语音文件和字幕文件
            String audioUrl = null;
            String subtitleUrl = null;
            String errorMessage = null;
            if (StrUtil.isNotBlank(chatResponse.data().audio())) {
                byte[] data = HEX.decode(chatResponse.data().audio());
                try {
                    audioUrl = fileApi.createFile(data);
                } catch (Exception ex) {
                    errorMessage = ex.getMessage();
                    log.warn("[textToVoice][语音({})) 上传失败]", chatResponse.traceId(), ex);
                }
            }

            // 3. 更新 message 状态
            if (StrUtil.isNotBlank(errorMessage)){
                textToAudioMessage.setStatus(AiImageStatusEnum.FAIL.getStatus()).setErrorMessage(errorMessage);
            }else {
                textToAudioMessage.setStatus(AiImageStatusEnum.SUCCESS.getStatus());
            }
            textToAudioMessage.setAudioFile(audioUrl).setSubtitleFile(subtitleUrl).setErrorMessage(errorMessage)
                    .setTraceId(chatResponse.traceId()).setAudioLength(extraInfo.audioLength())
                    .setAudioSampleRate(extraInfo.audioSampleRate()).setAudioSize(extraInfo.audioSize())
                    .setBitrate(extraInfo.bitrate()).setWordCount(extraInfo.wordCount()).setInvisibleCharacterRatio(extraInfo.invisibleCharacterRatio())
                    .setUsageCharacters(extraInfo.usageCharacters())
                    .setFinishTime(finishTime);
            textToAudioMapper.updateById(textToAudioMessage);

            respVO = new AppAiTextToAudioMessageRespVO()
                    .setAudioLength(msToSeconds(extraInfo.audioLength()))
                    .setAudioUrl(audioUrl).setAudioSize(extraInfo.audioSize())
                    .setMessageId(textToAudioMessage.getMessageId())
                    .setInvisibleCharacterRatio(extraInfo.invisibleCharacterRatio())
                    .setContent(textToAudioMessage.getContent())
                    .setCreateTime(textToAudioMessage.getCreateTime()).setId(textToAudioMessage.getId());
        }

        return respVO;
    }

    private AiTextToAudioMessageDO createTextToAudioMessage(AppAiTextToAudioReqVO reqVO, Long userId) {
        AiTextToAudioMessageDO message = new AiTextToAudioMessageDO().setMessageId(reqVO.getMessageId())
                //.setPlatform(reqVO.getPlatform())
                .setStatus(AiImageStatusEnum.WAITING.getStatus())
                .setModel(MinMaxT2AProApi.DEFAULT_T2A_MODEL).setUserId(userId)
                .setContent(reqVO.getText())
                //.setOutputFormat(reqVO.getOutputFormat())
                ;
        message.setCreateTime(LocalDateTime.now());
        textToAudioMapper.insert(message);
        return message;
    }

    /**
     * 毫秒转化未秒 精确到小数点后四位
     */
    public static Double msToSeconds(Long milliseconds) {
        // 将毫秒转换为秒
        Double seconds = milliseconds / 1000.0;

        // 使用 DecimalFormat 格式化到小数点后两位
        DecimalFormat df = new DecimalFormat("#.0000");
        return Double.parseDouble(df.format(seconds));
    }

    /**
     * 去除文本中的括号和括号内的内容
     * @param text
     * @return
     */
    public static String removeBracketContent(String text) {
        // 使用正则编译匹配括号
        return Pattern.compile("[（(][^）)]*?[）)]")
                .matcher(text)
                .replaceAll("") // 替换匹配的内容为空
                .trim(); // 去除前后空格
    }

}

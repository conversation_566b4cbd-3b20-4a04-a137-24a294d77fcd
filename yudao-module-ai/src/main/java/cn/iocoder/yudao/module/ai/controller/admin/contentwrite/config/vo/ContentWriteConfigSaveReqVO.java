package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - AI 内容创作配置新增/修改 Request VO")
@Data
public class ContentWriteConfigSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "14057")
    private Long id;

    @Schema(description = "消耗算力/次")
    private Integer useCoin;

}
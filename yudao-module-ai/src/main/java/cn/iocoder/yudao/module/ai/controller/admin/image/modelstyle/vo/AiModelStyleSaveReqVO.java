package cn.iocoder.yudao.module.ai.controller.admin.image.modelstyle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 风格模型新增/修改 Request VO")
@Data
public class AiModelStyleSaveReqVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "8361")
    private Long id;

    @Schema(description = "风格描述")
    private String modelStyleDesc;

    @Schema(description = "触站Model ID", example = "17062")
    private Integer modelStyleId;

    @Schema(description = "效果图")
    private String modelStyleImg;

    @Schema(description = "风格名称", example = "赵六")
    private String modelStyleName;

    @Schema(description = "使用次数", example = "29343")
    private Integer modelStyleUseCount;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
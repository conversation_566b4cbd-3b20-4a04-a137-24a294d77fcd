package cn.iocoder.yudao.module.ai.controller.app.contentwrite.message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.app.contentwrite.message.vo.*;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConversationDO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteMessageDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.conversation.ContentWriteConversationService;
import cn.iocoder.yudao.module.ai.service.contentwrite.message.ContentWriteMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户APP - AI内容创作消息")
@RestController
@RequestMapping("/ai/contentwrite/message")
@Validated
public class AppContentWriteMessageController {

    @Resource
    private ContentWriteMessageService contentWriteMessageService;

    @Resource
    private ContentWriteConversationService contentWriteConversationService;

    @Operation(summary = "发送消息（段式）", description = "一次性返回，响应较慢")
    @PostMapping("/send")
    public CommonResult<AppContentWriteMessageSendRespVO> appSendContentWriteMessage(@Valid @RequestBody AppContentWriteMessageSendReqVO sendReqVO) {
        return success(contentWriteMessageService.sendMessage(sendReqVO, getLoginUserId()));
    }

    @Operation(summary = "发送消息（流式）", description = "流式返回，响应较快")
    @PostMapping(value = "/send-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PermitAll // 解决 SSE 最终响应的时候，会被 Access Denied 拦截的问题
    public Flux<CommonResult<AppContentWriteMessageSendRespVO>> senddContentWriteMessageStream(@Valid @RequestBody AppContentWriteMessageSendReqVO sendReqVO) {
        return contentWriteMessageService.senddContentWriteMessageStream(sendReqVO, getLoginUserId());
    }

    @Operation(summary = "获得指定对话的消息列表-分页")
    @GetMapping("/list-by-conversation-id")
    @Parameter(name = "conversationId", required = true, description = "对话编号", example = "1024")
    public CommonResult<PageResult<AppContentWriteMessageRespVO>> getContentWriteMessageListByConversationId(AppContentWriteMessagePageReqVO pageReqVO) {
        AiContentWriteConversationDO conversation = contentWriteConversationService.getContentWriteConversation(pageReqVO.getConversationId());
        if (conversation == null || ObjUtil.notEqual(conversation.getUserId(), getLoginUserId())) {
            return success(PageResult.empty());
        }
        PageResult<AiContentWriteMessageDO> messagePage = contentWriteMessageService.getContentWriteMessageListByConversationId(pageReqVO);
        if (CollUtil.isEmpty(messagePage.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(messagePage, AppContentWriteMessageRespVO.class));
    }

    /**
     * 消息点赞/点踩
     */
    @Operation(summary = "内容创作点赞/点踩")
    @PostMapping("/praise")
    public CommonResult<Boolean> praise(@Valid @RequestBody AppContentWriteMessagePraiseReqVO reqVO) {
        contentWriteMessageService.praiseContentWriteMessage(reqVO);
        return success(true);
    }

}
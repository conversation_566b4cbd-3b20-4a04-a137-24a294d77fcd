package cn.iocoder.yudao.module.ai.service.promptwords;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.promptwords.vo.PromptWordsSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.promptwords.PromptWordsDO;
import cn.iocoder.yudao.module.ai.dal.mysql.promptwords.PromptWordsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.IMAGE_PROMPT_WORDS_NOT_EXISTS;

/**
 * 提示词 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PromptWordsServiceImpl implements PromptWordsService {

    @Resource
    private PromptWordsMapper promptWordsMapper;

    @Override
    public Long createPromptWords(PromptWordsSaveReqVO createReqVO) {
        // 插入
        PromptWordsDO promptWords = BeanUtils.toBean(createReqVO, PromptWordsDO.class);
        promptWordsMapper.insert(promptWords);
        // 返回
        return promptWords.getId();
    }

    @Override
    public void updatePromptWords(PromptWordsSaveReqVO updateReqVO) {
        // 校验存在
        validatePromptWordsExists(updateReqVO.getId());
        // 更新
        PromptWordsDO updateObj = BeanUtils.toBean(updateReqVO, PromptWordsDO.class);
        promptWordsMapper.updateById(updateObj);
    }

    @Override
    public void deletePromptWords(Long id) {
        // 校验存在
        validatePromptWordsExists(id);
        // 删除
        promptWordsMapper.deleteById(id);
    }

    private void validatePromptWordsExists(Long id) {
        if (promptWordsMapper.selectById(id) == null) {
            throw exception(IMAGE_PROMPT_WORDS_NOT_EXISTS);
        }
    }

    @Override
    public PromptWordsDO getPromptWords(Long id) {
        return promptWordsMapper.selectById(id);
    }

    @Override
    public PageResult<PromptWordsDO> getPromptWordsPage(PromptWordsPageReqVO pageReqVO) {
        return promptWordsMapper.selectPage(pageReqVO);
    }

}
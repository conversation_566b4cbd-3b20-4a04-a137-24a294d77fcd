package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.conversation.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - AI 内容写作 对话 DO分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContentWriteConversationPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "模型标志")
    private String model;

    @Schema(description = "模型编号", example = "31192")
    private Long modelId;

    @Schema(description = "是否置顶")
    private Boolean pinned;

    @Schema(description = "置顶时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] pinnedTime;

    @Schema(description = "角色编号", example = "5227")
    private Long roleId;

    @Schema(description = "角色设定")
    private String systemMessage;

    @Schema(description = "温度参数")
    private Double temperature;

    @Schema(description = "对话标题")
    private String title;

    @Schema(description = "采样方法")
    private Double topP;

    @Schema(description = "用户编号", example = "16872")
    private Long userId;

}
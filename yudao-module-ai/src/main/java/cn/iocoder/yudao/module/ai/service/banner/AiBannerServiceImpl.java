package cn.iocoder.yudao.module.ai.service.banner;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerCreateReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.banner.vo.BannerUpdateReqVO;
import cn.iocoder.yudao.module.ai.convert.banner.BannerConvert;
import cn.iocoder.yudao.module.ai.dal.dataobject.banner.AiBannerDO;
import cn.iocoder.yudao.module.ai.dal.mysql.banner.AiBannerMapper;
import cn.iocoder.yudao.module.ai.service.CommonService;
import cn.iocoder.yudao.module.pay.api.coin.MemberCoinApi;
import cn.iocoder.yudao.module.pay.api.coin.dto.MemberCoinRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.ai.enums.ErrorCodeConstants.BANNER_NOT_EXISTS;

/**
 * 首页 banner 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiBannerServiceImpl implements AiBannerService {

    @Resource
    private AiBannerMapper aiBannerMapper;

    @Resource
    private CommonService commonService;

    @Resource
    private MemberCoinApi memberCoinApi;

    @Override
    public Long createBanner(BannerCreateReqVO createReqVO) {
        // 插入
        AiBannerDO banner = BannerConvert.INSTANCE.convert(createReqVO);
        aiBannerMapper.insert(banner);
        // 返回
        return banner.getId();
    }

    @Override
    public void updateBanner(BannerUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateBannerExists(updateReqVO.getId());
        // 更新
        AiBannerDO updateObj = BannerConvert.INSTANCE.convert(updateReqVO);
        aiBannerMapper.updateById(updateObj);
    }

    @Override
    public void deleteBanner(Long id) {
        // 校验存在
        this.validateBannerExists(id);
        // 删除
        aiBannerMapper.deleteById(id);
    }

    private void validateBannerExists(Long id) {
        if (aiBannerMapper.selectById(id) == null) {
            throw exception(BANNER_NOT_EXISTS);
        }
    }

    @Override
    public AiBannerDO getBanner(Long id) {
        return aiBannerMapper.selectById(id);
    }

    @Override
    public PageResult<AiBannerDO> getBannerPage(BannerPageReqVO pageReqVO) {
        return aiBannerMapper.selectPage(pageReqVO);
    }

    @Override
    public void addBannerBrowseCount(Long id) {
        // 校验 Banner 是否存在
        validateBannerExists(id);
        // 增加点击次数
        aiBannerMapper.updateBrowseCount(id);
    }

    @Override
    public List<AiBannerDO> getBannerListByPosition(Integer position) {
        MemberCoinRespDTO payCoin =  memberCoinApi.getMemberBlanceCoin(getLoginUserId());
        Integer coin = payCoin.getBalanceCoin();
        if (payCoin.getBalanceCoin() == null || payCoin.getBalanceCoin() <= 0) {
            coin = 0;
        }
        commonService.sendAsyncMessageToMember(getLoginUserId(), "banner", coin);
        return aiBannerMapper.selectBannerListByPosition(position);
    }
}

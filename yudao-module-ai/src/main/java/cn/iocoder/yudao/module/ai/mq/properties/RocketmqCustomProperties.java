package cn.iocoder.yudao.module.ai.mq.properties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/10/30
 **/
@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ConfigurationProperties(prefix = "yudao.rocketmq")
public class RocketmqCustomProperties {

    private boolean enabled = true;

    private String orderTopic;

    private String orderGroup;

    private String goodsTopic;

    private String goodsGroup;

    private String memberTopic;

    private String memberGroup;

}

package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigRespVO;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigSaveReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConfigDO;
import cn.iocoder.yudao.module.ai.service.contentwrite.config.ContentWriteConfigService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI 内容创作配置")
@RestController
@RequestMapping("/ai/content-write-config")
@Validated
public class ContentWriteConfigController {

    @Resource
    private ContentWriteConfigService contentWriteConfigService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "创建AI 内容创作配置")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:create')")
    public CommonResult<Long> saveOrUpdateContentWriteConfig(@Valid @RequestBody ContentWriteConfigSaveReqVO createReqVO) {
        return success(contentWriteConfigService.saveOrUpdateContentWriteConfig(createReqVO));
    }

    @GetMapping("/getLastOne")
    @Operation(summary = "获得AI 内容创作配置")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:query')")
    public CommonResult<ContentWriteConfigRespVO> getLastOneContentWriteConfig() {
        AiContentWriteConfigDO contentWriteConfig = contentWriteConfigService.getLastOneContentWriteConfig();
        return success(BeanUtils.toBean(contentWriteConfig, ContentWriteConfigRespVO.class));
    }

    @Hidden
    @PostMapping("/create")
    @Operation(summary = "创建AI 内容创作配置")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:create')")
    public CommonResult<Long> createContentWriteConfig(@Valid @RequestBody ContentWriteConfigSaveReqVO createReqVO) {
        return success(contentWriteConfigService.createContentWriteConfig(createReqVO));
    }

    @Hidden
    @PutMapping("/update")
    @Operation(summary = "更新AI 内容创作配置")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:update')")
    public CommonResult<Boolean> updateContentWriteConfig(@Valid @RequestBody ContentWriteConfigSaveReqVO updateReqVO) {
        contentWriteConfigService.updateContentWriteConfig(updateReqVO);
        return success(true);
    }

    @Hidden
    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 内容创作配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:delete')")
    public CommonResult<Boolean> deleteContentWriteConfig(@RequestParam("id") Long id) {
        contentWriteConfigService.deleteContentWriteConfig(id);
        return success(true);
    }

    @Hidden
    @GetMapping("/get")
    @Operation(summary = "获得AI 内容创作配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:query')")
    public CommonResult<ContentWriteConfigRespVO> getContentWriteConfig(@RequestParam("id") Long id) {
        AiContentWriteConfigDO contentWriteConfig = contentWriteConfigService.getContentWriteConfig(id);
        return success(BeanUtils.toBean(contentWriteConfig, ContentWriteConfigRespVO.class));
    }

    @Hidden
    @GetMapping("/page")
    @Operation(summary = "获得AI 内容创作配置分页")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:query')")
    public CommonResult<PageResult<ContentWriteConfigRespVO>> getContentWriteConfigPage(@Valid ContentWriteConfigPageReqVO pageReqVO) {
        PageResult<AiContentWriteConfigDO> pageResult = contentWriteConfigService.getContentWriteConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContentWriteConfigRespVO.class));
    }

    @Hidden
    @GetMapping("/export-excel")
    @Operation(summary = "导出AI 内容创作配置 Excel")
    @PreAuthorize("@ss.hasPermission('ai:content-write-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContentWriteConfigExcel(@Valid ContentWriteConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiContentWriteConfigDO> list = contentWriteConfigService.getContentWriteConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI 内容创作配置.xls", "数据", ContentWriteConfigRespVO.class,
                        BeanUtils.toBean(list, ContentWriteConfigRespVO.class));
    }

}
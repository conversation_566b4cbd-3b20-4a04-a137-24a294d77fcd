package cn.iocoder.yudao.module.ai.controller.admin.contentwrite.message.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 内容创作 消息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContentWriteMessageRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "30654")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "算力")
    @ExcelProperty("算力")
    private Integer aiCoin;

    @Schema(description = "聊天内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("聊天内容")
    private String content;

    @Schema(description = "对话编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3386")
    @ExcelProperty("对话编号")
    private Long conversationId;

    @Schema(description = "输入命中敏感词")
    @ExcelProperty("输入命中敏感词")
    private Boolean inputSensitive;

    @Schema(description = "输入敏感词类型", example = "2")
    @ExcelProperty("输入敏感词类型")
    private Integer inputSensitiveType;

    @Schema(description = "模型标志", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模型标志")
    private String model;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12500")
    @ExcelProperty("模型编号")
    private Long modelId;

    @Schema(description = "输出命中敏感词")
    @ExcelProperty("输出命中敏感词")
    private Boolean outputSensitive;

    @Schema(description = "输出敏感词类型", example = "2")
    @ExcelProperty("输出敏感词类型")
    private Integer outputSensitiveType;

    @Schema(description = "点赞类型 点赞1 点踩2", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("点赞类型 点赞1 点踩2")
    private Integer praiseType;

    @Schema(description = "回复消息编号", example = "26860")
    @ExcelProperty("回复消息编号")
    private Long replyId;

    @Schema(description = "请求完整参数")
    @ExcelProperty("请求完整参数")
    private String requestParams;

    @Schema(description = "响应完整参数")
    @ExcelProperty("响应完整参数")
    private String responseParams;

    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("消息类型")
    private String type;

    @Schema(description = "是否携带上下文", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否携带上下文")
    private Boolean useContext;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18130")
    @ExcelProperty("用户编号")
    private Long userId;

}
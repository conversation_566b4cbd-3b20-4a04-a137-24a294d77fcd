package cn.iocoder.yudao.module.ai.dal.dataobject.base;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * @Description: 算力规则配置
 * @Date: 2025/4/2 22:21
 * @Author: zhangq
 * @Version: 1.0
 */

@Table(name = "ai_coin_role_config")
@Comment(value = "AI算力规则配置")
@Entity
@TableName("ai_coin_role_config")
@KeySequence("ai_coin_role_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoinRoleConfigDO extends TenantBaseDO {

    /**
     * 编号，唯一自增
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 算力细则
     */
    @Column(length = 2048, columnDefinition = "varchar(2048) COMMENT '算力细则'")
    private String coinRole;

    /**
     * 算力说明
     */
    @Column(length = 2048,columnDefinition = "varchar(2048) COMMENT '算力说明'")
    private String coinRoleDesc;
}
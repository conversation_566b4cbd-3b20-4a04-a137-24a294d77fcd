package cn.iocoder.yudao.module.ai.dal.mysql.contentwrite.config;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.ai.controller.admin.contentwrite.config.vo.ContentWriteConfigPageReqVO;
import cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite.AiContentWriteConfigDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 内容创作配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentWriteConfigMapper extends BaseMapperX<AiContentWriteConfigDO> {

    default PageResult<AiContentWriteConfigDO> selectPage(ContentWriteConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiContentWriteConfigDO>()
                .betweenIfPresent(AiContentWriteConfigDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AiContentWriteConfigDO::getUseCoin, reqVO.getUseCoin())
                .orderByDesc(AiContentWriteConfigDO::getId));
    }

   default AiContentWriteConfigDO selectDefaultConfig(){
       // 构建查询条件，按id降序排列并限制只查询一条记录
       LambdaQueryWrapper<AiContentWriteConfigDO> lambdaQuery =  new LambdaQueryWrapper<>();
       lambdaQuery.orderByDesc(AiContentWriteConfigDO::getId).last("LIMIT 1");
       // 执行查询
       return selectOne(lambdaQuery);
   }
}
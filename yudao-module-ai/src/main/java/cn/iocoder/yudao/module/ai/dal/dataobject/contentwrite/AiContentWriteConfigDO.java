package cn.iocoder.yudao.module.ai.dal.dataobject.contentwrite;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * AI 内容创作配置 DO
 *
 * <AUTHOR>
 */
@Table(name = "ai_content_write_config")
@Comment(value = "AI 内容创作配置")
@Entity
@TableName(value = "ai_content_write_config", autoResultMap = true)
@KeySequence("ai_content_write_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiContentWriteConfigDO extends TenantBaseDO {

    /**
     * 编号
     */
    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 算力
     */
    @Column(columnDefinition = "int COMMENT '消耗算力/次'")
    private Integer useCoin;

}
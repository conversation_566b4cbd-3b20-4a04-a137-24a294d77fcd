package cn.iocoder.yudao.module.ai.dal.dataobject.promptwords;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.ai.enums.promptwords.PromptWordsTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.glassfish.jaxb.core.v2.TODO;
import org.hibernate.annotations.Comment;

/**
 * @Description: 绘画提示词
 * 提示词
 * @Date: 2024/6/19 20:32
 * @Author: zhangq
 * @Version: 1.0
 */
@Table(name = "ai_prompt_words")
@Comment(value = "绘画提示词")
@Entity
@TableName(value = "ai_prompt_words", autoResultMap = true)
@KeySequence("ai_prompt_words_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromptWordsDO extends TenantBaseDO {

    private static final long serialVersionUID = 1L;

    @Id
    @Comment(value = "编号，唯一自增")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    private Long id;

    /**
     * 提示词
     */
    @Column(columnDefinition = "varchar(255) COMMENT '提示词'")
    private String promptWords;

    /**
     * 提示词类型
     * 枚举 {@link PromptWordsTypeEnum}
     */
    @Column(columnDefinition = "varchar(255) COMMENT '提示词类型'")
    private String promptWordsType;

    /**
     * 排序
     */
    @Column(columnDefinition = "int COMMENT '排序'")
    private Integer sort;

    /**
     * 状态
     * 枚举 {@link TODO common_status 对应的类}
     */
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '状态'")
    private Integer status;

}

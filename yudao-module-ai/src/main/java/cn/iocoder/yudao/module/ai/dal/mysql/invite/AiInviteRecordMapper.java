package cn.iocoder.yudao.module.ai.dal.mysql.invite;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ai.dal.dataobject.invite.AiInviteRecordDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AiInviteRecordMapper extends BaseMapperX<AiInviteRecordDO> {

    default AiInviteRecordDO selectBySourceId(Long userId) {
        return selectOne(AiInviteRecordDO::getSourceUserId, userId);
    }

    default AiInviteRecordDO findByInviteeUserId(Long inviteeUserId){
        return selectOne(AiInviteRecordDO::getUserId, inviteeUserId);
    }
}

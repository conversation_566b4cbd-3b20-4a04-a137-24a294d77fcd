package com.minimaxi.platform.t2a.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.minimaxi.platform.pro.api.MinMaxT2AProApi;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * MiniMaxChatOptions represents the options for performing chat completion using the
 * MiniMax API. It provides methods to set and retrieve various options like model,
 * frequency penalty, max tokens, etc.
 *
 * @see ChatOptions
 * <AUTHOR> Rong
 * @since 1.0.0 M1
 */
@JsonInclude(Include.NON_NULL)
public class MiniMaxT2AOptions {

	private @JsonProperty("model") String model;
	private @JsonProperty("text") String text;
	private @JsonProperty("voice_setting") MinMaxT2AApi.VoiceSetting voiceSetting;
	private @JsonProperty("audio_setting") MinMaxT2AApi.AudioSettings audioSetting;
	private @JsonProperty("pronunciation_dict") MinMaxT2AApi.PronunciationDict pronunciateDict;
	private @JsonProperty("timber_weights") List<MinMaxT2AApi.TimberWeights> timberWeights;
	private @JsonProperty("stream") Boolean stream;
	private @JsonProperty("language_boost") String languageBoost;

	public static Builder builder() {
		return new Builder();
	}

	public static class Builder {

		protected MiniMaxT2AOptions options;

		public Builder() {
			this.options = new MiniMaxT2AOptions();
		}

		public Builder(MiniMaxT2AOptions options) {
			this.options = options;
		}

		public Builder withModel(String model) {
			if(model == null || ObjectUtils.isEmpty(model)){
				model = MinMaxT2AProApi.DEFAULT_T2A_MODEL;
			}
			this.options.model = model;
			return this;
		}

		public Builder withText(String text) {
			Assert.notNull(text, "text must not be null");
			this.options.text = text;
			return this;
		}

		public Builder withTimberWeights(List<MinMaxT2AApi.TimberWeights> timberWeights) {
			this.options.timberWeights = timberWeights;
			return this;
		}

		public Builder withVoiceSetting(MinMaxT2AApi.VoiceSetting voiceSetting) {
			this.options.voiceSetting = voiceSetting;
			return this;
		}

		public Builder withAudioSetting(MinMaxT2AApi.AudioSettings audioSetting) {
			this.options.audioSetting = audioSetting;
			return this;
		}

		public Builder withPronunciateDict(MinMaxT2AApi.PronunciationDict pronunciateDict) {
			this.options.pronunciateDict = pronunciateDict;
			return this;
		}

		public Builder withStream(Boolean stream) {
			this.options.stream = stream;
			return this;
		}

		public Builder withLanguageBoost(String languageBoost) {
			this.options.languageBoost = languageBoost;
			return this;
		}

		public MiniMaxT2AOptions build() {
			return this.options;
		}
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public MinMaxT2AApi.VoiceSetting getVoiceSetting() {
		return voiceSetting;
	}

	public void setVoiceSetting(MinMaxT2AApi.VoiceSetting voiceSetting) {
		this.voiceSetting = voiceSetting;
	}

	public MinMaxT2AApi.AudioSettings getAudioSetting() {
		return audioSetting;
	}

	public void setAudioSetting(MinMaxT2AApi.AudioSettings audioSetting) {
		this.audioSetting = audioSetting;
	}

	public MinMaxT2AApi.PronunciationDict getPronunciateDict() {
		return pronunciateDict;
	}

	public void setPronunciateDict(MinMaxT2AApi.PronunciationDict pronunciateDict) {
		this.pronunciateDict = pronunciateDict;
	}

	public List<MinMaxT2AApi.TimberWeights> getTimberWeights() {
		return timberWeights;
	}

	public void setTimberWeights(List<MinMaxT2AApi.TimberWeights> timberWeights) {
		this.timberWeights = timberWeights;
	}

	public Boolean getStream() {
		return stream;
	}

	public void setStream(Boolean stream) {
		this.stream = stream;
	}

	public String getLanguageBoost() {
		return languageBoost;
	}

	public void setLanguageBoost(String languageBoost) {
		this.languageBoost = languageBoost;
	}

	public static MiniMaxT2AOptions fromOptions(MiniMaxT2AOptions fromOptions) {
		return builder().withModel(fromOptions.getModel())
				.withText(fromOptions.getText())
				.withTimberWeights(fromOptions.getTimberWeights())
				.withVoiceSetting(fromOptions.getVoiceSetting())
				.withAudioSetting(fromOptions.getAudioSetting())
				.withPronunciateDict(fromOptions.getPronunciateDict())
				.withStream(fromOptions.getStream())
				.withLanguageBoost(fromOptions.getLanguageBoost())
			.build();
	}

}

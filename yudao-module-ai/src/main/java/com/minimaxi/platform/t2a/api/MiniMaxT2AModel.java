package com.minimaxi.platform.t2a.api;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.minimax.MiniMaxChatModel;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;

/**
 */
public class MiniMaxT2AModel {

	private static final Logger logger = LoggerFactory.getLogger(MiniMaxChatModel.class);


	public final RetryTemplate retryTemplate;

	private final MiniMaxT2AOptions defaultOptions;

	private final MinMaxT2AApi minMaxT2AApi;


	public MiniMaxT2AModel(MinMaxT2AApi minMaxT2AApi, MiniMaxT2AOptions options, RetryTemplate retryTemplate) {
		Assert.notNull(minMaxT2AApi, "minMaxT2AApi must not be null");
		Assert.notNull(options, "Options must not be null");
		Assert.notNull(retryTemplate, "RetryTemplate must not be null");
		this.minMaxT2AApi = minMaxT2AApi;
		this.defaultOptions = options;
		this.retryTemplate = retryTemplate;
	}

	public MiniMaxT2AModel(MinMaxT2AApi minMaxT2AApi, MiniMaxT2AOptions options) {
		this(minMaxT2AApi, options, RetryUtils.DEFAULT_RETRY_TEMPLATE);
	}

	public MinMaxT2AApi.T2ACompletion call(String groupId){

		if (defaultOptions != null) {
			ObjectMapper mapper = new ObjectMapper();
            String json = null;
            try {
                json = mapper.writeValueAsString(defaultOptions);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            logger.info("T2A call: {}", json);
		}

		MinMaxT2AApi.T2ACompletionRequest request = new MinMaxT2AApi.T2ACompletionRequest(defaultOptions, groupId);

		return this.retryTemplate.execute(ctx -> {

			ResponseEntity<MinMaxT2AApi.T2ACompletion> completionEntity = minMaxT2AApi.chatCompletionEntity(request);

			var chatCompletion = completionEntity.getBody();
			if (chatCompletion == null) {
				logger.warn("No chat completion returned for request: {}", JSON.toJSON(request));
				return null;
			}

            logger.info("T2A completion: {}", JSON.toJSON(chatCompletion));

			return chatCompletion;
		});
	}

	public Flux<MinMaxT2AApi.T2ACompletion> stream(String groupId) {

		MinMaxT2AApi.T2ACompletionRequest request = new MinMaxT2AApi.T2ACompletionRequest(defaultOptions, groupId);

		return this.retryTemplate.execute(ctx -> {

			Flux<MinMaxT2AApi.T2ACompletion> completion = this.minMaxT2AApi.audioCompletionStream(request);

			return completion;
		});
	}
}

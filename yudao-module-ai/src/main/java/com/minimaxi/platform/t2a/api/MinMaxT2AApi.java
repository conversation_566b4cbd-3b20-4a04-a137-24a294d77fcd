package com.minimaxi.platform.t2a.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.ai.model.ModelDescription;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/7/25 15:27
 * @Author: zhangq
 * @Version: 1.0
 */
public class MinMaxT2AApi {

    public static final String DEFAULT_T2A_MODEL = T2AModel.SPEECH_01_240228.getValue();

    private final RestClient restClient;

    private final WebClient webClient;

    public MinMaxT2AApi(String miniMaxToken) {
        this(ApiUtils.DEFAULT_BASE_URL, miniMaxToken);
    }

    public MinMaxT2AApi(String baseUrl, String miniMaxToken) {
        this(baseUrl, miniMaxToken, RestClient.builder());
    }

    /**
     * Create a new chat completion api.
     *
     * @param baseUrl api base URL.
     * @param miniMaxToken MiniMax apiKey.
     * @param restClientBuilder RestClient builder.
     */
    public MinMaxT2AApi(String baseUrl, String miniMaxToken, RestClient.Builder restClientBuilder) {
        this(baseUrl, miniMaxToken, restClientBuilder, RetryUtils.DEFAULT_RESPONSE_ERROR_HANDLER);
    }

    /**
     * Create a new chat completion api.
     *
     * @param baseUrl api base URL.
     * @param miniMaxToken MiniMax apiKey.
     * @param restClientBuilder RestClient builder.
     * @param responseErrorHandler Response error handler.
     */
    public MinMaxT2AApi(String baseUrl, String miniMaxToken, RestClient.Builder restClientBuilder, ResponseErrorHandler responseErrorHandler) {

        this.restClient = restClientBuilder
                .baseUrl(baseUrl)
                .defaultHeaders(ApiUtils.getJsonContentHeaders(miniMaxToken))
                .defaultStatusHandler(responseErrorHandler)
                .build();

        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeaders(ApiUtils.getJsonContentHeaders(miniMaxToken))
                .build();
    }


    public enum T2AModel implements ModelDescription {
        SPEECH_01_TURBO("speech-01-turbo"),//最新的模型，拥有出色的效果与时延表现。此模型将不定期迭代优化，以提供更优的体验

        SPEECH_01_240228("speech-01-240228"),//稳定版本的模型，效果出色。稳定版本适合对可能出现的细微音色变化敏感的场景。

        SPEECH_01_TURBO_240228("speech-01-turbo-240228"),//稳定版本的模型，时延更低。稳定版本适合对可能出现的细微音色变化敏感的场景。
        ;

        public final String  value;

        T2AModel(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        @Override
        public String getName() {
            return this.value;
        }
    }

    /**
     *
     * @param model 请求的模型版本：speech-01-turbo、speech-01-240228、speech-01-turbo-240228
     * @param text 待合成的文本，长度限制<500字符，段落切换用换行符替代。
     *（如需要控制语音中间隔时间，在字间增加<#x#>,x单位为秒，支持0.01-99.99，最多两位小数）。
     * 支持自定义文本与文本之间的语音时间间隔，以实现自定义文本语音停顿时间的效果。
     *  需要注意的是文本间隔时间需设置在两个可以语音发音的文本之间，且不能设置多个连续的时间间隔。
     * @param voiceSetting
     * @param audioSetting
     * @param timberWeights 与voice_id二选一必填
     * @param stream 是否流式。默认false，即不开启流式。
     * @param languageBoost 增强对指定的小语种的识别能力，设置后可以提升在指定小语种场景下的语音表现。
     * 请注意，开启此参数可能会减弱多语种混读的能力。
     * 支持以下取值： 'Spanish'、'French'、'Portuguese'、'Korean'、'Indonesian'、'German'、'Japanese'、'Italian'、'auto'
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record T2ACompletionRequest (
            @JsonProperty("model") String model,
            @JsonProperty("text") String text,
            @JsonProperty("voice_setting") VoiceSetting voiceSetting,
            @JsonProperty("audio_setting") AudioSettings audioSetting,
            @JsonProperty("pronunciation_dict") PronunciationDict pronunciateDict,
            @JsonProperty("timber_weights") List<TimberWeights> timberWeights,
            @JsonProperty("stream") Boolean stream,
            @JsonProperty("language_boost") String languageBoost
            ) {

        private static String groupId;

        public T2ACompletionRequest(String model, String text, VoiceSetting voiceSetting, AudioSettings audioSetting, List<TimberWeights> timberWeights, Boolean stream) {
            this(model, text, voiceSetting, audioSetting, null,timberWeights, stream, null);
        }

        public T2ACompletionRequest(MiniMaxT2AOptions options, String groupId
        ) {
            this(options.getModel(), options.getText(), options.getVoiceSetting(), options.getAudioSetting(),
                    options.getPronunciateDict(),options.getTimberWeights(), options.getStream(), options.getLanguageBoost());
            this.groupId = groupId;
        }

        /**
         * An object specifying the format that the model must output.
         * @param type Must be one of 'text' or 'json_object'.
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record ResponseFormat(
                @JsonProperty("type") String type) {
        }
    }

    /**
     *
     * @param speed 生成声音的语速，可选，取值越大，语速越快。 范围[0.5,2]，默认值为1.0
     * @param vol 生成声音的音量，可选，取值越大，音量越高。 范围（0,10]，默认值为1.0
     * @param pitch 生成声音的语调，可选，（0为原音色输出，取值需为整数）。 范围[-12,12]，默认值为0
     * @param voiceId 请求的音色编号。与timber_weights二选一“必填”。
     * 支持系统音色(id)以及复刻音色（id）两种类型，其中系统音色（ID）如下：
     * 青涩青年音色：male-qn-qingse
     * 精英青年音色：male-qn-jingying
     * 霸道青年音色：male-qn-badao
     * 青年大学生音色：male-qn-daxuesheng
     * 少女音色：female-shaonv
     * 御姐音色：female-yujie
     * 成熟女性音色：female-chengshu
     * 甜美女性音色：female-tianmei
     * 男性主持人：presenter_male
     * 女性主持人：presenter_female
     * 男性有声书1：audiobook_male_1
     * 男性有声书2：audiobook_male_2
     * 女性有声书1：audiobook_female_1
     * 女性有声书2：audiobook_female_2
     * 青涩青年音色-beta：male-qn-qingse-jingpin
     * 精英青年音色-beta：male-qn-jingying-jingpin
     * 霸道青年音色-beta：male-qn-badao-jingpin
     * 青年大学生音色-beta：male-qn-daxuesheng-jingpin
     * 少女音色-beta：female-shaonv-jingpin
     * 御姐音色-beta：female-yujie-jingpin
     * 成熟女性音色-beta：female-chengshu-jingpin
     * 甜美女性音色-beta：female-tianmei-jingpin
     * 聪明男童：clever_boy
     * 可爱男童：cute_boy
     * 萌萌女童：lovely_girl
     * 卡通猪小琪：cartoon_pig
     * 病娇弟弟：bingjiao_didi
     * 俊朗男友：junlang_nanyou
     * 纯真学弟：chunzhen_xuedi
     * 冷淡学长：lengdan_xiongzhang
     * 霸道少爷：badao_shaoye
     * 甜心小玲：tianxin_xiaoling
     * 俏皮萌妹：qiaopi_mengmei
     * 妩媚御姐：wumei_yujie
     * 嗲嗲学妹：diadia_xuemei
     * 淡雅学姐：danya_xuejie
     * Santa Claus：Santa_Claus
     * Grinch：Grinch
     * Rudolph：Rudolph
     * Arnold：Arnold
     * Charming Santa：Charming_Santa
     * Charming Lady：Charming_Lady
     * Sweet Girl：Sweet_Girl
     * Cute Elf：Cute_Elf
     * Attractive Girl：Attractive_Girl
     * Serene Woman：Serene_Woman
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record VoiceSetting(
            @JsonProperty("speed") Float speed,
            @JsonProperty("vol") Float vol,
            @JsonProperty("pitch") Integer pitch,
            @JsonProperty("voice_id") String voiceId
    ) {}

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record VoiceSettingEmo(
            @JsonProperty("speed") Float speed,
            @JsonProperty("vol") Float vol,
            @JsonProperty("pitch") Integer pitch,
            @JsonProperty("voice_id") String voiceId,
            @JsonProperty("emotion") String emotion
    ) {}

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record VoiceSettingEmoLatexRead(
            @JsonProperty("speed") Float speed,
            @JsonProperty("vol") Float vol,
            @JsonProperty("pitch") Integer pitch,
            @JsonProperty("voice_id") String voiceId,
            @JsonProperty("emotion") String emotion,
            @JsonProperty("latex_read") Boolean latexRead
    ) {}


    /**
     *
     * @param sampleRate 生成声音的采样率。可选，默认为32000。 范围[8000,16000,22050,24000,32000]
     * @param bitrate  生成声音的比特率。可选，默认值为128000。该参数仅对mps格式的音频生效。范围[32000,64000,128000]
     * @param format 生成的音频格式。默认mp3，范围[mp3,pcm,flac,wav]。wav仅在非流式输出下支持。
     * @param channel 生成音频的声道数.默认1：单声道，可选：
     * 1：单声道
     * 2：双声道
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record AudioSettings(
            @JsonProperty("sample_rate") Integer sampleRate,
            @JsonProperty("bitrate") Integer bitrate,
            @JsonProperty("format") String format,
            @JsonProperty("channel") Integer channel
    ) {}

    /**
     *
     * @param tone 替换需要特殊标注的文字、符号及对应的注音。
     * 替换发音（调整声调/替换其他字符发音），格式如下：
     * ["燕少飞/(yan4)(shao3)(fei1)","达菲/(da2)(fei1)"，"omg/oh my god"，]
     * 声调用数字代替，一声（阴平）为1，二声（阳平）为2，三声（上声）为3，四声（去声）为4），轻声为5。
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record PronunciationDict(
            @JsonProperty("tone") List<String> tone
    ) {}


    /**
     *
     * @param voiceId 请求的音色编号。须和weight参数同步填写。暂时只支持系统音色(id)：参考上方voice_id的说明。
     * @param weight 权重，须与voice_id同步填写。最多支持4种音色混合，取值为整数，单一音色取值占比越高，合成音色越像。 范围[1,100]
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record TimberWeights(
            @JsonProperty("voice_id") String voiceId,
            @JsonProperty("weight") Integer weight){}


    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record T2ACompletion(
            @JsonProperty("trace_id") String traceId,
            @JsonProperty("data") RespData data,
            @JsonProperty("extra_info") ExtraInfo extraInfo,
            @JsonProperty("base_resp") BaseResponse baseResponse) {

        /**
         *
         * @param audio 合成后的音频片段，采用hex编码，按照输入定义的格式进行生成（mp3/pcm/flac）。
         * @param status 当前音频流状态，1表示合成中，2表示合成结束。
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record RespData(
                @JsonProperty("audio") String audio,
                @JsonProperty("status") Integer status
        ){}
        /**
         * @param audioLength  音频时长。精确到毫秒。
         * @param audioSampleRate 音频采样率。默认为24000，如客户请求参数进行调整，会根据请求参数生成。
         * @param audioSize 音频大小。单位为字节。
         * @param bitrate 比特率。默认为168000，如客户请求参数进行调整，会根据请求参数生成。
         * @param audioFormat 生成音频文件的格式。取值范围mp3/pcm/flac。
         * @param wordCount 可读字数。已经发音的字数统计（不算标点等其他符号，包含汉字数字字母）
         * @param audioChannel 生成音频声道数。1：单声道，2：双声道。
         * @param invisibleCharacterRatio 非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
         * @param usageCharacters 消费字符数。本次语音生成的计费字符数。
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record ExtraInfo(
                @JsonProperty("audio_length") Long audioLength,
                @JsonProperty("audio_sample_rate") Long audioSampleRate,
                @JsonProperty("audio_size") Long audioSize,
                @JsonProperty("bitrate") Long bitrate,
                @JsonProperty("audio_format") String audioFormat,
                @JsonProperty("word_count") Long wordCount,
                @JsonProperty("audio_channel") Long audioChannel,
                @JsonProperty("invisible_character_ratio") Double invisibleCharacterRatio,
                @JsonProperty("usage_characters") Long usageCharacters
        ){}


        /**
         *
         * @param statusCode 状态码。1000，未知错误；1001，超时；1002，触发限流；1004，鉴权失败；1039，触发TPM限流；1042，非法字符超过10%；2013，输入格式信息不正常。
         * @param message 状态详情。
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record BaseResponse(
                @JsonProperty("status_code") Long statusCode,
                @JsonProperty("status_msg") String message
        ){}

    }

    public ResponseEntity<T2ACompletion> chatCompletionEntity(T2ACompletionRequest t2aRequest) {

        Assert.notNull(t2aRequest, "The request body can not be null.");

        return this.restClient.post()
                .uri("v1/t2a_v2?GroupId="+ T2ACompletionRequest.groupId)
                //.uri("v1/text/chatcompletion_pro")
                .body(t2aRequest)
                .retrieve()
                .toEntity(T2ACompletion.class);
    }

    public Flux<T2ACompletion> audioCompletionStream(T2ACompletionRequest audioRequest) {

        Assert.notNull(audioRequest, "The request body can not be null.");
        Assert.isTrue(audioRequest.stream(), "Request must set the steam property to true.");

        return this.webClient.post()
                .uri("/v1/t2a_v2?GroupId="+ T2ACompletionRequest.groupId)
                .body(Mono.just(audioRequest), T2ACompletionRequest.class)
                .retrieve()
                .bodyToFlux(T2ACompletion.class)
                ;
    }

}

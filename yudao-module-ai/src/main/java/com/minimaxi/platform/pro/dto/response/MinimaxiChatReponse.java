package com.minimaxi.platform.pro.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 触站AI
 *提交绘画的参数，此接口为异步提交任务接口，并不直接返回最终的绘画结果，请调用获取任务详情接口获取绘画的进度或者结果
 */
@NoArgsConstructor
@Data
public class MinimaxiChatReponse implements Serializable {

    /**
     * 本次请求的唯一标识，用于排查问题。
     */
    @Schema(description = "本次请求的唯一标识，用于排查问题。")
    private String id;

    /**
     * 请求发起时间。Unixtime, Nanosecond。
     */
    @Schema(description = "请求发起时间。Unixtime, Nanosecond。")
    private Long created;

    /**
     * 请求指定的模型名称。
     */
    @Schema(description = "请求指定的模型名称")
    private String model;

    /**
     * 回复的内容
     */
    @Schema(description = "回复的内容")
    private String reply;

    /**
     * 输入命中敏感词
     */
    @Schema(description = "输入命中敏感词")
    @JsonProperty("input_sensitive")
    private Boolean inputSensitive;

    /**
     * 错误状态码和详情
     */
    @Schema(description = "错误状态码和详情")
    @JsonProperty("base_resp")
    private BaseResp baseResp;


    /**
     * glyph语法的返回结果
     */
    @Schema(description = "glyph语法的返回结果")
    @JsonProperty("glyph_result")
    private String glyphResult;

    /**
     * 模型回复要求.
     */
    @Schema(description = "根据query内容生成的functions接口name和arguments，functions为请求中定义的函数.")
    @JsonProperty("function_call")
    private FunctionCallRep functionCall;

    /**
     * 知识库参数
     */
    @Schema(description = "知识库参数")
    @JsonProperty("knowledge_base")
    private KnowledgeBase knowledgeBase;

    /**
     * 片段
     */
    @Schema(description = "片段")
    private Object[] chunks;

    /**
     * 片段内容。
     */
    @Schema(description = "片段内容。")
    @JsonProperty("content")
    private Long content;

    /**
     * 片段index。
     */
    @Schema(description = "片段index。")
    @JsonProperty("index")
    private Integer index;

    /**
     * 片段所属的文件ID
     */
    @Schema(description = "片段所属的文件ID")
    private String document;
}
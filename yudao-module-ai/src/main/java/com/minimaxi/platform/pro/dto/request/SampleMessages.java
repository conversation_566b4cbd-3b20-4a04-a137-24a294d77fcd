package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 22:54
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class SampleMessages {

    /**
     * 示例对话参数。发送者的类型。string 需要为以下三个合法值之一：
     * USER用户发送的内容；
     * BOT模型生成的内容；
     * FUNCTION详见下文中的函数调用部分。
     */
    @Schema(description = "示例对话参数。")
    @JsonProperty("sender_type")
    private String senderType;

    @Schema(description = "示例对话参数。发送者的名字。")
    @JsonProperty("sender_name")
    private String senderName;

    @Schema(description = "示例对话参数。消息的内容。")
    private String text;
}

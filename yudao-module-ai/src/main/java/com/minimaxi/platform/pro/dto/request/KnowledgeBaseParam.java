package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 23:08
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class KnowledgeBaseParam {

    @Schema(description = "知识库的ID。")
    @JsonProperty("knowledge_base_id")
    private Long knowledgeBaseId;

    /**
     * 默认值5。
     */
    @JsonProperty("top_k")
    private Integer topK;

    @Schema(description = "默认为0。0,1) ")
    private Double thresh;

    /**
     * 提供 {{context}} {{question}} ，可由用户自己定义。默认使用，：
     * 请根据以下参考内容回答提问
     * 参考内容:
     * >>>
     * {{context}}
     * >>>
     * 提问：{{question}}
     */
    @Schema(description = "知识库状态")
    private String pattern;

    /**
     * 默认为0。
     * 从最后一句话开始倒数，>= 0，适用于多步问答的场景
     * 应用场景：
     *
     * user: 你印象最深的比赛是什么
     * bot: 2018年 RNG vs G2的比赛
     * user：那场比赛RNG为什么被淘汰了？
     *
     * 当 num_prev_messages为1时，会将第2、3行的内容一起用来作为query。
     */
    @JsonProperty("num_prev_messages")
    private Integer numPrevMessages;
}

package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 22:26
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class ReplyConstraints {

    /**
     * 指定回复的角色类型,当前只支持 BOT机器人。
     */
    @Schema(description = "指定回复的角色类型,当前只支持 BOT机器人。")
    @JsonProperty("sender_type")
    private String senderType;

    @Schema(description = "指定回复的机器人名称。")
    @JsonProperty("sender_name")
    private String senderName;

    //private Glyph glyph;
}

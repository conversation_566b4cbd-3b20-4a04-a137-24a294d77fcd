package com.minimaxi.platform.pro;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.minimaxi.platform.pro.api.MinMaxT2AProApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.minimax.MiniMaxChatModel;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;

/**
 */
public class MiniMaxT2AProModel {

	private static final Logger logger = LoggerFactory.getLogger(MiniMaxChatModel.class);


	public final RetryTemplate retryTemplate;

	private final MiniMaxT2AProOptions defaultOptions;

	private final MinMaxT2AProApi minMaxT2AProApi;


	public MiniMaxT2AProModel(MinMaxT2AProApi minMaxT2AProApi,MiniMaxT2AProOptions options, RetryTemplate retryTemplate) {
		Assert.notNull(minMaxT2AProApi, "MinMaxT2APro must not be null");
		Assert.notNull(options, "Options must not be null");
		Assert.notNull(retryTemplate, "RetryTemplate must not be null");
		this.minMaxT2AProApi = minMaxT2AProApi;
		this.defaultOptions = options;
		this.retryTemplate = retryTemplate;
	}

	public MiniMaxT2AProModel(MinMaxT2AProApi minMaxT2AProApi, MiniMaxT2AProOptions options) {
		this(minMaxT2AProApi, options, RetryUtils.DEFAULT_RETRY_TEMPLATE);
	}

	public MinMaxT2AProApi.T2AProCompletion call(){

		if (defaultOptions != null) {
			ObjectMapper mapper = new ObjectMapper();
            String json = null;
            try {
                json = mapper.writeValueAsString(defaultOptions);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            logger.info("T2APro call: {}", json);
		}

		MinMaxT2AProApi.T2AProCompletionRequest request = new MinMaxT2AProApi.T2AProCompletionRequest(defaultOptions);

		return this.retryTemplate.execute(ctx -> {

			ResponseEntity<MinMaxT2AProApi.T2AProCompletion> completionEntity = minMaxT2AProApi.chatCompletionEntity(request);

			var chatCompletion = completionEntity.getBody();
			if (chatCompletion == null) {
				logger.warn("No chat completion returned for request: {}", JSON.toJSON(request));
				return null;
			}

            logger.info("T2APro completion: {}", JSON.toJSON(chatCompletion));

			return chatCompletion;
		});
	}
}

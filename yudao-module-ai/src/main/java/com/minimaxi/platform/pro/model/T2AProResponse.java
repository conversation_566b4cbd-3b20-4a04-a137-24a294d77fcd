package com.minimaxi.platform.pro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class T2AProResponse {

    private  @JsonProperty("trace_id") String traceId;
    private  @JsonProperty("audio_file") String audioFile;
    private  @JsonProperty("subtitle_file") String subtitleFile;
    private  @JsonProperty("extra_info") ExtraInfo extraInfo;
    private  @JsonProperty("base_resp") BaseResponse baseResponse;

        /**
         * @param audioLength  音频时长。精确到毫秒。
         * @param audioSampleRate 音频采样率。默认为24000，如客户请求参数进行调整，会根据请求参数生成。
         * @param audioSize 音频大小。单位为字节。
         * @param bitrate 比特率。默认为168000，如客户请求参数进行调整，会根据请求参数生成。
         * @param wordCount 可读字数。已经发音的字数统计（不算标点等其他符号，包含汉字数字字母）。
         * @param invisibleCharacterRatio 非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
         * @param usageCharacters 消费字符数。本次语音生成的计费字符数。
         */
        public record ExtraInfo(
                @JsonProperty("audio_length") Long audioLength,
                @JsonProperty("audio_sample_rate") Long audioSampleRate,
                @JsonProperty("audio_size") Long audioSize,
                @JsonProperty("bitrate") Long bitrate,
                @JsonProperty("word_count") Long wordCount,
                @JsonProperty("invisible_character_ratio") Double invisibleCharacterRatio,
                @JsonProperty("usage_characters") Long usageCharacters
        ){}


        public record BaseResponse(
                @JsonProperty("status_code") Long statusCode,
                @JsonProperty("status_msg") String message
        ){}
}

package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 22:29
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class Glyph {

    /**
     * 使用什么模板功能当前仅支持1、raw2、json_value。
     */
    @Schema(description = "使用什么模板功能当前仅支持1、raw ;2、json_value。")
    private String type;

    /**
     * 需要限制的格式要求，使用 glpyh 语法，详见下文 glyph 语法说明章节。
     */
    @Schema(description = "需要限制的格式要求，使用 glpyh 语法，详见下文 glyph 语法说明章节。")
    @JsonProperty("raw_glyph")
    private String rawGlyph;
}

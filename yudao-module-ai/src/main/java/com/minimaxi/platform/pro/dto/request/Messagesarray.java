package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 22:17
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class Messagesarray{
    /**
     * 发送者的类型。取值需要为以下4个合法值之一：
     * USER：用户发送的内容；
     * BOT：模型生成的内容；
     * assistant：模型生成的内容；
     * FUNCTION：详见下文中的函数调用部分。
     */
    @Schema(description = "发送者的类型。")
    @JsonProperty("sender_type")
    private String senderType;

    /**
     * 发送者的名称。
     */
    @Schema(description = "发送者的名称。")
    @JsonProperty("sender_name")
    private String senderName;

    /**
     * 消息内容，长度影响接口性能。
     */
    @Schema(description = "消息内容，长度影响接口性能。")
    private String text;

}

package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 22:23
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class BotSetting {

    /**
     *具体机器人的名字。
     */
    @Schema(description = "具体机器人的名字。")
    @JsonProperty("bot_name")
    private String bot_name;

    /**
     *
     */
    @Schema(description = "具体机器人的设定,长度影响接口性能。")
    @JsonProperty("content")
    private String content;
}

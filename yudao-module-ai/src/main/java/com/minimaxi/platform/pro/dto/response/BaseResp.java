package com.minimaxi.platform.pro.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 23:33
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class BaseResp {

    /**
     * 状态码。1000，
     * 未知错误；1001，
     * 超时；1002，
     * 触发RPM限流；1004，
     * 鉴权失败；1008，
     * 余额不足；1013，
     * 服务内部错误；1027，
     * 输出内容错误；1039，
     * 触发TPM限流；2013，
     * 输入格式信息不正常。
     */
    @Schema(description = "状态码")
    @JsonProperty("status_code")
    private Long statusCode;

    @Schema(description = "错误详情")
    @JsonProperty("status_msg")
    private String statusMsg;
}

package com.minimaxi.platform.pro.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 23:04
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class KnowledgeBase {

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    @JsonProperty("knowledge_base_id")
    private Long knowledgeBaseId;

    /**
     * 片段
     */
    @Schema(description = "片段")
    private Object[] chunks;

}

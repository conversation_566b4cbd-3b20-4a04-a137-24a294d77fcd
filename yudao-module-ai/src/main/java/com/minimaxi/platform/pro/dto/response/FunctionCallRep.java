package com.minimaxi.platform.pro.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 23:04
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class FunctionCallRep {

    /**
     * function名称
     */
    @Schema(description = "function名称")
    private String name;

    /**
     * 调用该function时应传入的参数。json格式.
     */
    @Schema(description = "调用该function时应传入的参数。json格式")
    private String arguments;

}

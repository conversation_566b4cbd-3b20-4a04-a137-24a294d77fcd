package com.minimaxi.platform.pro.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 23:04
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class FunctionCall {

    /**
     * function calling的参数。函数调用模式。functions 使用模式，有三个枚举值可选：
     * auto：模型自动选择使用哪个 function；
     * specific：用户指定使用哪个 function；
     * none：不使用 function，同时忽略 functions 字段的输入。
     *
     * auto模式是相对通用的功能，大模型会结合query和functions的定义自主判断是否给出function call返回；
     * specific模式会在每次请求时，大模型都会强制给出function call返回，因此用户在注入自有function输出得到大模型生成结果时，一定要关闭该模式，否则会再次给出function call返回；
     * none模式则是会让大模型忽略functions的定义，直面query进行回答。
     */
    @Schema(description = "function calling的参数。")
    private String type;

    /**
     * function calling的参数。强制调用的函数命名。通过该字段指定要使用的 function，仅当 function_call.type = specific 时生效。
     */
    @Schema(description = "function calling的参数。强制调用的函数命名。")
    private String name;

}

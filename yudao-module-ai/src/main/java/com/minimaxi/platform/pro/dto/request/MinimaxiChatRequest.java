package com.minimaxi.platform.pro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 触站AI
 *提交绘画的参数，此接口为异步提交任务接口，并不直接返回最终的绘画结果，请调用获取任务详情接口获取绘画的进度或者结果
 */
@NoArgsConstructor
@Data
public class MinimaxiChatRequest implements Serializable {

    @Schema(description = "调用的模型名称。目前支持取以下值：abab6.5-chat、abab6.5s-chat、abab5.5s-chat、abab5.5-chat、您的fine_tuned_model")
    @JsonProperty("model")
    private String model;

    /**
     * 是否通过流式分批返回结果。如果设置为true，结果分批返回，两个换行分割分批返回结果。默认为false。
     */
    @Schema(description = "是否通过流式分批返回结果。如果设置为true，结果分批返回，两个换行分割分批返回结果。默认为false")
    private Boolean stream;

    /**
     * 最大生成token数，需要注意的是，这个参数并不会影响模型本身的生成效果，而是仅仅通过以截断超出的token的方式来实现功能.取值可选，
     *  abab6.5、abab5.5s取值区间为(0,8192]，默认取值256；
     *  abab6.5s取值区间为(0,245760]，默认取值256；
     *  abab5.5取值区间为(0,16384]，默认取值256。
     */
    @Schema(description = "最大生成token数")
    @JsonProperty("tokens_to_generate")
    private Integer tokensToGenerate;

    /**
     * 较高的值将使输出更加随机，而较低的值将使输出更加集中和确定。可选：abab6.5、abab6.5s 默认取值0.1；
     * abab5.5s、abab5.5 默认取值0.9。
     * 低（0.01~0.2）：适合答案较明确的场景（如：知识问答、总结说明、情感分析、文本分类、大纲生成、作文批改）、信息提取；
     * ⾼（0.7〜1）：适合答案较开放发散的场景 （如：营销文案生成、人设对话）。
     */
    @Schema(description = "较高的值将使输出更加随机，而较低的值将使输出更加集中和确定")
    private Double temperaturefloat;

    /**
     * 采样方法，数值越小结果确定性越强；数值越大，结果越随机。可选:
     * 各模型默认取值0.95
     */
    @Schema(description = "较高的值将使输出更加随机，而较低的值将使输出更加集中和确定")
    @JsonProperty("top_pfloat")
    private Double topPfloat;

    /**
     * 对输出中易涉及隐私问题的文本信息进行打码，目前包括但不限于邮箱、域名、链接、证件号、家庭住址等，默认true，即开启打码。
     */
    @Schema(description = "较高的值将使输出更加随机，而较低的值将使输出更加集中和确定")
    @JsonProperty("mask_sensitive_infobool")
    private Boolean mask_sensitive_infobool;

    /**
     * 对话的内容。
     */
    @Schema(description = "对话的内容。")
    private Messagesarray messagesarray;

    /**
     * 模型参数
     */
    @Schema(description = "模型参数")
    @JsonProperty("bot_setting")
    private List<BotSetting> botSetting;

    /**
     * 模型回复要求.
     */
    @Schema(description = "模型回复要求.")
    @JsonProperty("reply_constraints")
    private ReplyConstraints replyConstraints;

    /**
     * 示例对话参数。示例对话内容
     */
    @Schema(description = "示例对话参数。示例对话内容")
    @JsonProperty("sample_messages")
    private List<SampleMessages>  sample_messages;

    /**
     * function calling的参数。函数的定义。模型可以为其生成 JSON 输入的函数列表
     * 支持多个函数，当出现多个函数时尽量将函数命名和描述区分开，如果2个函数在命名和功能说明上过于相似的话，会造成交叉调用的问题。
     */
    @Schema(description = "function calling的参数。函数的定义。模型可以为其生成 JSON 输入的函数列表")
    @JsonProperty("functions")
    private List<Functions> functions;

    /**
     * function calling的参数。函数调用，指定 functions 的使用模式。支持多种函数调用模式，用户可选择自动、强制和none模式。
     */
    @Schema(description = "function calling的参数。函数调用，指定 functions 的使用模式。支持多种函数调用模式，用户可选择自动、强制和none模式。")
    @JsonProperty("function_call")
    private FunctionCall functionCall;

    /**
     * 知识库的参数。
     */
    @Schema(description = "知识库的参数。")
    @JsonProperty("knowledge_base_param")
    private KnowledgeBaseParam knowledgeBaseParam;
}
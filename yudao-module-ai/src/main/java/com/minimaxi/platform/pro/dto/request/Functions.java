package com.minimaxi.platform.pro.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/7/11 22:57
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class Functions {

    /**
     * function calling的参数。函数命名。需要满足 [a-zA-Z0-9-_]{1,64} 格式并且不能以 plugin_ 开头。
     * 因为模型会根据query和函数命名以及功能说明匹配，所以函数命名一定要尽可能贴合query场景。上述使用场景示例中，函数命名可以定义为“mention_something”。
     */
    @Schema(description = "function calling的参数。")
    private String name;

    /**
     * function calling的参数。函数的功能说明。务必在说明里明确函数的功能，会作为模型是否调用function的参考.
     * 因为模型会根据query和函数命名以及功能说明匹配，所以函数功能说明一定要尽可能贴合query场景。上述使用场景示例中，函数功能说明可以定义为“当问到会议中提及事项时，用于根据会议号和发言人进行历史会议记录的检索”。
     */
    @Schema(description = "函数描述")
    private String description;

    /**
     * function calling的参数。该函数的参数及其说明。包括properties以及required，properties包括参数及每个参数的类型type和描述，required代表必须要返回的参数。
     * 对每个参数的命名和参数说明也需要描述精确，才能更好的生成。比如： " parameters" : { " type" : " object" , " properties" : { " meeting_id" : { " type" : " string" , " description" : " 会议id" }, " participants" : { " type" : " string" , " description" : " 参会人" } }, " required" : [ " meetingid" , " participants" ] }。
     */
    @Schema(description = "函数参数")
    private Object parameters;
}

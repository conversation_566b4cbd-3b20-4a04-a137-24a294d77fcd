package com.minimaxi.platform.pro;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.minimaxi.platform.pro.api.MinMaxT2AProApi;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * MiniMaxChatOptions represents the options for performing chat completion using the
 * MiniMax API. It provides methods to set and retrieve various options like model,
 * frequency penalty, max tokens, etc.
 *
 * @see ChatOptions
 * <AUTHOR> Rong
 * @since 1.0.0 M1
 */
@JsonInclude(Include.NON_NULL)
public class MiniMaxT2AProOptions{

	private @JsonProperty("model") String model;

	private @JsonProperty("voice_id") String voiceId;

	private @JsonProperty("timber_weights") List<MinMaxT2AProApi.TimberWeights> timberWeights;

	private @JsonProperty("speed") Float speed;

	private @JsonProperty("vol") Float vol;

    private @JsonProperty("output_format") String outputFormat;

    private @JsonProperty("pitch") Integer pitch;

	private @JsonProperty("text") String text;

    private @JsonProperty("audio_sample_rate") Integer audioSampleRate;

    private @JsonProperty("bitrate") Integer bitrate;

    private @JsonProperty("char_to_pitch") List<String> characterToPitch;

	public static Builder builder() {
		return new Builder();
	}

	public static class Builder {

		protected MiniMaxT2AProOptions options;

		public Builder() {
			this.options = new MiniMaxT2AProOptions();
		}

		public Builder(MiniMaxT2AProOptions options) {
			this.options = options;
		}

		public Builder withModel(String model) {
			if(model == null || ObjectUtils.isEmpty(model)){
				model = MinMaxT2AProApi.DEFAULT_T2A_MODEL;
			}
			this.options.model = model;
			return this;
		}

		public Builder withVoiceId(String voiceId) {
			this.options.voiceId = voiceId;
			return this;
		}

		public Builder withTimberWeights(List<MinMaxT2AProApi.TimberWeights> timberWeights) {
			this.options.timberWeights = timberWeights;
			return this;
		}

		public Builder withSpeed(Float speed) {
			this.options.speed = speed;
			return this;
		}

		public Builder withVol(Float vol) {
			this.options.vol = vol;
			return this;
		}

		public Builder withOutputFormat(String outputFormat) {
			this.options.outputFormat = outputFormat;
			return this;
		}

		public Builder withPitch(Integer pitch) {
			this.options.pitch = pitch;
			return this;
		}

		public Builder withText(String text) {
			Assert.notNull(text, "text must not be null");
			this.options.text = text;
			return this;
		}

		public Builder withAudioSampleRate(Integer audioSampleRate) {
			this.options.audioSampleRate = audioSampleRate;
			return this;
		}

		public Builder withBitrate(Integer bitrate) {
			this.options.bitrate = bitrate;
			return this;
		}

		public Builder withCharacterToPitch(List<String> characterToPitch) {
			this.options.characterToPitch = characterToPitch;
			return this;
		}

		public MiniMaxT2AProOptions build() {
			return this.options;
		}

	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getVoiceId() {
		return voiceId;
	}

	public void setVoiceId(String voiceId) {
		this.voiceId = voiceId;
	}

	public List<MinMaxT2AProApi.TimberWeights> getTimberWeights() {
		return timberWeights;
	}

	public void setTimberWeights(List<MinMaxT2AProApi.TimberWeights> timberWeights) {
		this.timberWeights = timberWeights;
	}

	public Float getSpeed() {
		return speed;
	}

	public void setSpeed(Float speed) {
		this.speed = speed;
	}

	public Float getVol() {
		return vol;
	}

	public void setVol(Float vol) {
		this.vol = vol;
	}

	public String getOutputFormat() {
		return outputFormat;
	}

	public void setOutputFormat(String outputFormat) {
		this.outputFormat = outputFormat;
	}

	public Integer getPitch() {
		return pitch;
	}

	public void setPitch(Integer pitch) {
		this.pitch = pitch;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public Integer getAudioSampleRate() {
		return audioSampleRate;
	}

	public void setAudioSampleRate(Integer audioSampleRate) {
		this.audioSampleRate = audioSampleRate;
	}

	public Integer getBitrate() {
		return bitrate;
	}

	public void setBitrate(Integer bitrate) {
		this.bitrate = bitrate;
	}

	public List<String> getCharacterToPitch() {
		return characterToPitch;
	}

	public void setCharacterToPitch(List<String> characterToPitch) {
		this.characterToPitch = characterToPitch;
	}

	public static MiniMaxT2AProOptions fromOptions(MiniMaxT2AProOptions fromOptions) {
		return builder().withModel(fromOptions.getModel())
				.withVoiceId(fromOptions.getVoiceId())
				.withTimberWeights(fromOptions.getTimberWeights())
				.withSpeed(fromOptions.getSpeed())
				.withVol(fromOptions.getVol())
				.withOutputFormat(fromOptions.getOutputFormat())
				.withPitch(fromOptions.getPitch())
				.withText(fromOptions.getText())
				.withAudioSampleRate(fromOptions.getAudioSampleRate())
				.withBitrate(fromOptions.getBitrate())
				.withCharacterToPitch(fromOptions.getCharacterToPitch())
			.build();
	}

}

package com.minimaxi.platform.pro.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.minimaxi.platform.pro.MiniMaxT2AProOptions;
import org.springframework.ai.model.ModelDescription;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/7/25 15:27
 * @Author: zhangq
 * @Version: 1.0
 */
public class MinMaxT2AProApi {

    public static final String DEFAULT_T2A_MODEL = T2AProModel.SPEECH_01.getValue();

    private final RestClient restClient;

    public MinMaxT2AProApi(String miniMaxToken) {
        this(ApiProUtils.DEFAULT_BASE_URL, miniMaxToken);
    }

    public MinMaxT2AProApi(String baseUrl, String miniMaxToken) {
        this(baseUrl, miniMaxToken, RestClient.builder());
    }

    /**
     * Create a new chat completion api.
     *
     * @param baseUrl api base URL.
     * @param miniMaxToken MiniMax apiKey.
     * @param restClientBuilder RestClient builder.
     */
    public MinMaxT2AProApi(String baseUrl, String miniMaxToken, RestClient.Builder restClientBuilder) {
        this(baseUrl, miniMaxToken, restClientBuilder, RetryUtils.DEFAULT_RESPONSE_ERROR_HANDLER);
    }

    /**
     * Create a new chat completion api.
     *
     * @param baseUrl api base URL.
     * @param miniMaxToken MiniMax apiKey.
     * @param restClientBuilder RestClient builder.
     * @param responseErrorHandler Response error handler.
     */
    public MinMaxT2AProApi(String baseUrl, String miniMaxToken, RestClient.Builder restClientBuilder, ResponseErrorHandler responseErrorHandler) {

        this.restClient = restClientBuilder
                .baseUrl(baseUrl)
                .defaultHeaders(ApiProUtils.getJsonContentHeaders(miniMaxToken))
                .defaultStatusHandler(responseErrorHandler)
                .build();
    }


    public enum T2AProModel implements ModelDescription {
        SPEECH_01("speech-01"),//中文
        /**
         * abab6.5s-chat
         */
        SPEECH_02("speech-02"),//中文、英文、中英混合、日文、韩文
        ;

        public final String  value;

        T2AProModel(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        @Override
        public String getName() {
            return this.value;
        }
    }

    /**
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record T2AProCompletionRequest (
            @JsonProperty("model") String model,
            @JsonProperty("voice_id") String voiceId,
            @JsonProperty("timber_weights") List<TimberWeights> timberWeights,
            @JsonProperty("speed") Float speed,
            @JsonProperty("vol") Float vol,
            @JsonProperty("output_format") String outputFormat,
            @JsonProperty("pitch") Integer pitch,
            @JsonProperty("text") String text,
            @JsonProperty("audio_sample_rate") Integer audioSampleRate,
            @JsonProperty("bitrate") Integer bitrate,
            @JsonProperty("char_to_pitch") List<String> characterToPitch) {


        public T2AProCompletionRequest(String model,String voiceId,String text,Integer audioSampleRate,Integer bitrate) {
            this(model, voiceId,  null, null, null,null,
                    null, text, audioSampleRate, bitrate, null);
        }

        public T2AProCompletionRequest(MiniMaxT2AProOptions options
        ) {
            this(options.getModel(), options.getVoiceId(),  options.getTimberWeights(), options.getSpeed(), options.getVol(),
                    options.getOutputFormat(),options.getPitch(), options.getText(), options.getAudioSampleRate(), options.getBitrate(),
                    options.getCharacterToPitch());
        }

        /**
         * An object specifying the format that the model must output.
         * @param type Must be one of 'text' or 'json_object'.
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public record ResponseFormat(
                @JsonProperty("type") String type) {
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record TimberWeights(
            @JsonProperty("voice_id") String voiceId,
            @JsonProperty("weight") Integer weight){}


    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record T2AProCompletion(
            @JsonProperty("trace_id") String traceId,
            @JsonProperty("audio_file") String audioFile,
            @JsonProperty("subtitle_file") String subtitleFile,
            @JsonProperty("extra_info") ExtraInfo extraInfo,
            @JsonProperty("base_resp") BaseResponse baseResponse) {

        /**
         * @param audioLength  音频时长。精确到毫秒。
         * @param audioSampleRate 音频采样率。默认为24000，如客户请求参数进行调整，会根据请求参数生成。
         * @param audioSize 音频大小。单位为字节。
         * @param bitrate 比特率。默认为168000，如客户请求参数进行调整，会根据请求参数生成。
         * @param wordCount 可读字数。已经发音的字数统计（不算标点等其他符号，包含汉字数字字母）。
         * @param invisibleCharacterRatio 非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
         * @param usageCharacters 消费字符数。本次语音生成的计费字符数。
         */
        public record ExtraInfo(
                @JsonProperty("audio_length") Long audioLength,
                @JsonProperty("audio_sample_rate") Long audioSampleRate,
                @JsonProperty("audio_size") Long audioSize,
                @JsonProperty("bitrate") Long bitrate,
                @JsonProperty("word_count") Long wordCount,
                @JsonProperty("invisible_character_ratio") Double invisibleCharacterRatio,
                @JsonProperty("usage_characters") Long usageCharacters
        ){}


        public record BaseResponse(
                @JsonProperty("status_code") Long statusCode,
                @JsonProperty("status_msg") String message
        ){}

    }


    public ResponseEntity<T2AProCompletion> chatCompletionEntity(T2AProCompletionRequest t2aRequest) {

        Assert.notNull(t2aRequest, "The request body can not be null.");

        return this.restClient.post()
                .uri("/v1/t2a_pro")
                //.uri("v1/text/chatcompletion_pro")
                .body(t2aRequest)
                .retrieve()
                .toEntity(T2AProCompletion.class);
    }

}

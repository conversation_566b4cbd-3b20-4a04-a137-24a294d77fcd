package com.minimaxi.platform.pro.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.minimaxi.platform.pro.dto.request.*;
import org.springframework.ai.model.ModelDescription;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.boot.context.properties.bind.ConstructorBinding;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

public class MiniMaxProApi {

	public static final String DEFAULT_CHAT_MODEL = ChatModel.ABAB_6_Chat.getValue();
	private static final Predicate<String> SSE_DONE_PREDICATE = "[DONE]"::equals;

	private final RestClient restClient;

	private final WebClient webClient;

	public MiniMaxProApi(String miniMaxToken) {
		this(ApiProUtils.DEFAULT_BASE_URL, miniMaxToken);
	}

	public MiniMaxProApi(String baseUrl, String miniMaxToken) {
		this(baseUrl, miniMaxToken, RestClient.builder());
	}

	/**
	 * Create a new chat completion api.
	 *
	 * @param baseUrl api base URL.
	 * @param miniMaxToken MiniMax apiKey.
	 * @param restClientBuilder RestClient builder.
	 */
	public MiniMaxProApi(String baseUrl, String miniMaxToken, RestClient.Builder restClientBuilder) {
		this(baseUrl, miniMaxToken, restClientBuilder, RetryUtils.DEFAULT_RESPONSE_ERROR_HANDLER);
	}

	/**
	 * Create a new chat completion api.
	 *
	 * @param baseUrl api base URL.
	 * @param miniMaxToken MiniMax apiKey.
	 * @param restClientBuilder RestClient builder.
	 * @param responseErrorHandler Response error handler.
	 */
	public MiniMaxProApi(String baseUrl, String miniMaxToken, RestClient.Builder restClientBuilder, ResponseErrorHandler responseErrorHandler) {

		this.restClient = restClientBuilder
				.baseUrl(baseUrl)
				.defaultHeaders(ApiProUtils.getJsonContentHeaders(miniMaxToken))
				.defaultStatusHandler(responseErrorHandler)
				.build();

		this.webClient = WebClient.builder()
				.baseUrl(baseUrl)
				.defaultHeaders(ApiProUtils.getJsonContentHeaders(miniMaxToken))
				.build();
	}


	public enum ChatModel implements ModelDescription{
		ABAB_6_Chat("abab6-chat"),
		/**
		 * abab6.5s-chat
		 */
		ABAB_6_5_S_Chat("abab6.5s-chat"),
		ABAB_5_5_Chat("abab5.5-chat"),
		ABAB_5_5_S_Chat("abab5.5s-chat"),

		;

		public final String  value;

		ChatModel(String value) {
			this.value = value;
		}

		public String getValue() {
			return value;
		}

		@Override
		public String getName() {
			return this.value;
		}
	}

	public record Functions(
			@JsonProperty("description") String description,
			@JsonProperty("name") String name,
			@JsonProperty("parameters") String parameters) {

		@ConstructorBinding
		public Functions(String description, String name, Map<String, Object> parameters) {
			this(description, name, ModelOptionsUtils.toJsonString(parameters));
		}
	}

	/**
	 */
	@JsonInclude(Include.NON_NULL)
	public record ChatCompletionRequest (
			@JsonProperty("messages") List<ChatCompletionMessage> messages,
			@JsonProperty("model") String model,
			@JsonProperty("temperature") Float temperature,
			@JsonProperty("stream") Boolean stream,
			@JsonProperty("tokens_to_generate") Integer maxTokens,
			@JsonProperty("top_p") Float topP,
			@JsonProperty("mask_sensitive_info") Boolean maskSensitiveInfo,
			@JsonProperty("bot_setting") List<BotSetting> botSetting,
			@JsonProperty("reply_constraints") List<ReplyConstraints> replyConstraints,
			@JsonProperty("sample_messages") List<SampleMessages> sampleMessages,
			@JsonProperty("functions") List<Functions>  functions,
			@JsonProperty("function_call") FunctionCall functionCall,
			@JsonProperty("knowledge_base_param") KnowledgeBaseParam knowledgeBaseParam) {

		/**
		 * Shortcut constructor for a chat completion request with the given messages and model.
		 *
		 * @param messages A list of messages comprising the conversation so far.
		 * @param model ID of the model to use.
		 * @param temperature What sampling temperature to use, between 0 and 1.
		 */
		public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model,
					Float temperature,List<BotSetting> botSetting,List<ReplyConstraints> replyConstraints) {
			this(messages, model, temperature,  false, null, null,
					null, botSetting, replyConstraints, null, null, null, null);
		}

		/**
		 * Shortcut constructor for a chat completion request with the given messages, model and control for streaming.
		 *
		 * @param messages A list of messages comprising the conversation so far.
		 * @param model ID of the model to use.
		 * @param temperature What sampling temperature to use, between 0 and 1.
		 * @param stream If set, partial message deltas will be sent.Tokens will be sent as data-only server-sent events
		 * as they become available, with the stream terminated by a data: [DONE] message.
		 */
		public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model,boolean stream,
				Float temperature,List<BotSetting> botSetting,List<ReplyConstraints> replyConstraints) {
			this(messages, model, temperature,  stream, null, null,
					null, botSetting, replyConstraints, null, null, null, null);
		}

		/**
		 * Shortcut constructor for a chat completion request with the given messages, model, tools and tool choice.
		 * Streaming is set to false, temperature to 0.8 and all other parameters are null.
		 *
		 * @param messages A list of messages comprising the conversation so far.
		 * @param model ID of the model to use.
		 * @param functions
		 * @param functionCall
		 */
		public ChatCompletionRequest(List<ChatCompletionMessage> messages, String model,boolean stream,
				Float temperature,List<BotSetting> botSetting,List<ReplyConstraints> replyConstraints,
		         List<Functions> functions, FunctionCall functionCall) {
			this(messages, model, temperature,  stream, null, null,
					null, botSetting, replyConstraints, null, functions, functionCall, null);
		}

		/**
		 * Shortcut constructor for a chat completion request with the given messages, model, tools and tool choice.
		 * Streaming is set to false, temperature to 0.8 and all other parameters are null.
		 *
		 * @param messages A list of messages comprising the conversation so far.
		 * @param stream If set, partial message deltas will be sent.Tokens will be sent as data-only server-sent events
		 * as they become available, with the stream terminated by a data: [DONE] message.
		 */
		public ChatCompletionRequest(List<ChatCompletionMessage> messages,boolean stream,
									 Float temperature,List<BotSetting> botSetting,List<ReplyConstraints> replyConstraints) {
			this(messages, null, temperature,  stream, null, null,
					null, botSetting, replyConstraints, null, null, null, null);
		}

		/**
		 * An object specifying the format that the model must output.
		 * @param type Must be one of 'text' or 'json_object'.
		 */
		@JsonInclude(Include.NON_NULL)
		public record ResponseFormat(
				@JsonProperty("type") String type) {
		}
	}

	/**
	 * Message comprising the conversation.
	 *
	 * @param rawContent The contents of the message. Can be either a {@link MediaContent} or a {@link String}.
	 * The response message content is always a {@link String}.
	 * @param role The role of the messages author. Could be one of the {@link Role} types.
	 * @param name An optional name for the participant. Provides the model information to differentiate between
	 * participants of the same role. In case of Function calling, the name is the function name that the message is
	 * responding to.
	 * {@link Role#ASSISTANT} role and null otherwise.
	 */
	@JsonInclude(Include.NON_NULL)
	public record ChatCompletionMessage(
			@JsonProperty("text") Object rawContent,
			@JsonProperty("sender_type") Role role,
			@JsonProperty("sender_name") String name) {

		/**
		 * Get message content as String.
		 */
		public String content() {
			if (this.rawContent == null) {
				return null;
			}
			if (this.rawContent instanceof String text) {
				return text;
			}
			throw new IllegalStateException("The text is not a string!");
		}

		/**
		 * Create a chat completion message with the given content and role. All other fields are null.
		 * @param content The contents of the message.
		 * @param role The role of the author of this message.
		 */
		public ChatCompletionMessage(Object content, Role role) {
			this(content, role, null);
		}

		/**
		 * The role of the author of this message.
		 */
		public enum Role {
			/**
			 * BOT message.
			 */
			@JsonProperty("BOT") BOT,
			/**
			 * User message.
			 */
			@JsonProperty("USER") USER,
			/**
			 * Assistant message.
			 */
			@JsonProperty("assistant") ASSISTANT,
			/**
			 * FUNCTION message.
			 */
			@JsonProperty("FUNCTION") FUNCTION
		}

		/**
		 * An array of content parts with a defined type.
		 * Each MediaContent can be of either "text" or "image_url" type. Not both.
		 *
		 * @param type Content  type, each can be of type text or image_url.
		 * @param text The text content of the message.
		 * @param imageUrl The image content of the message. You can pass multiple
		 * images by adding multiple image_url content parts. Image input is only
		 * supported when using the glm-4v model.
		 */
		@JsonInclude(Include.NON_NULL)
		public record MediaContent(
			@JsonProperty("type") String type,
			@JsonProperty("text") String text,
			@JsonProperty("image_url") ImageUrl imageUrl) {

			/**
			 * @param url Either a URL of the image or the base64 encoded image data.
			 * The base64 encoded image data must have a special prefix in the following format:
			 * "data:{mimetype};base64,{base64-encoded-image-data}".
			 * @param detail Specifies the detail level of the image.
			 */
			@JsonInclude(Include.NON_NULL)
			public record ImageUrl(
				@JsonProperty("url") String url,
				@JsonProperty("detail") String detail) {

				public ImageUrl(String url) {
					this(url, null);
				}
			}

			/**
			 * Shortcut constructor for a text content.
			 * @param text The text content of the message.
			 */
			public MediaContent(String text) {
				this("text", text, null);
			}

			/**
			 * Shortcut constructor for an image content.
			 * @param imageUrl The image content of the message.
			 */
			public MediaContent(ImageUrl imageUrl) {
				this("image_url", null, imageUrl);
			}
		}
	}

	public static  String getTextContent(List<ChatCompletionMessage.MediaContent> content) {
		return content.stream()
				.filter(c -> "text".equals(c.type()))
				.map(ChatCompletionMessage.MediaContent::text)
				.reduce("", (a, b) -> a + b);
	}

	/**
	 * The reason the model stopped generating tokens.
	 */
	public enum ChatCompletionFinishReason {
		/**
		 * 接口返回了模型生成完整结果
		 */
		@JsonProperty("stop") STOP,
		/**
		 * 模型生成结果超过配置的tokens_to_generate长度，内容被截断；
		 */
		@JsonProperty("length") LENGTH,
		/**
		 * 输入+模型输出内容超过模型最大能力限制.
		 */
		@JsonProperty("max_output") MAX_OUTPUT
	}

	@JsonInclude(Include.NON_NULL)
	public record ChatCompletion(
			@JsonProperty("id") String id,
			@JsonProperty("choices") List<Choice> choices,
			@JsonProperty("created") Long created,
			@JsonProperty("model") String model,
			@JsonProperty("reply") String reply,
			@JsonProperty("input_sensitive") Boolean inputSensitive,
			@JsonProperty("input_sensitive_type") String inputSensitiveType,
			@JsonProperty("output_sensitive") Boolean outputSensitive,
			@JsonProperty("output_sensitive_type") String outputSensitiveType,
			@JsonProperty("glyph_result") GlyphResult glyphResult,
			@JsonProperty("index") Integer index,
			@JsonProperty("base_resp") BaseResponse baseResponse,
			@JsonProperty("usage") Usage usage,
			@JsonProperty("content") Long content,
			@JsonProperty("document") String document) {

		/**
		 * Chat completion choice.
		 *
		 * @param finishReason The reason the model stopped generating tokens.
		 * @param index The index of the choice in the list of choices.
		 * @param message A chat completion message generated by the model.
		 */
		@JsonInclude(Include.NON_NULL)
		public record Choice(
				@JsonProperty("finish_reason") ChatCompletionFinishReason finishReason,
				@JsonProperty("index") Integer index,
				@JsonProperty("message") ChatCompletionMessage message) {
		}


		public record BaseResponse(
				@JsonProperty("status_code") Long statusCode,
				@JsonProperty("status_msg") String message
		){}

		@JsonInclude(Include.NON_NULL)
		public record GlyphResult(
				@JsonProperty("glyph_id") String glyphId,
				@JsonProperty("glyph_name") String glyphName,
				@JsonProperty("glyph_url") String glyphUrl
		){}
	}

	/**
	 * Log probability information for the choice.
	 *
	 * @param content A list of message content tokens with log probability information.
	 */
	@JsonInclude(Include.NON_NULL)
	public record LogProbs(
			@JsonProperty("content") List<Content> content) {

		/**
		 * Message content tokens with log probability information.
		 *
		 * @param token The token.
		 * @param logprob The log probability of the token.
		 * @param probBytes A list of integers representing the UTF-8 bytes representation
		 * of the token. Useful in instances where characters are represented by multiple
		 * tokens and their byte representations must be combined to generate the correct
		 * text representation. Can be null if there is no bytes representation for the token.
		 * @param topLogprobs List of the most likely tokens and their log probability,
		 * at this token position. In rare cases, there may be fewer than the number of
		 * requested top_logprobs returned.
		 */
		@JsonInclude(Include.NON_NULL)
		public record Content(
				@JsonProperty("token") String token,
				@JsonProperty("logprob") Float logprob,
				@JsonProperty("bytes") List<Integer> probBytes,
				@JsonProperty("top_logprobs") List<TopLogProbs> topLogprobs) {

			/**
			 * The most likely tokens and their log probability, at this token position.
			 *
			 * @param token The token.
			 * @param logprob The log probability of the token.
			 * @param probBytes A list of integers representing the UTF-8 bytes representation
			 * of the token. Useful in instances where characters are represented by multiple
			 * tokens and their byte representations must be combined to generate the correct
			 * text representation. Can be null if there is no bytes representation for the token.
			 */
			@JsonInclude(Include.NON_NULL)
			public record TopLogProbs(
					@JsonProperty("token") String token,
					@JsonProperty("logprob") Float logprob,
					@JsonProperty("bytes") List<Integer> probBytes) {
			}
		}
	}

	/**
	 * Usage statistics for the completion request.
	 *
	 * @param totalTokens Total number of tokens used in the request (prompt + completion).
	 */
	@JsonInclude(Include.NON_NULL)
	public record Usage(
			@JsonProperty("total_tokens") Integer totalTokens) {

	}

	/**
	 * Represents a streamed chunk of a chat completion response returned by model, based on the provided input.
	 *
	 * @param id A unique identifier for the chat completion. Each chunk has the same ID.
	 * @param choices A list of chat completion choices. Can be more than one if n is greater than 1.
	 * @param created The Unix timestamp (in seconds) of when the chat completion was created. Each chunk has the same
	 * timestamp.
	 * @param model The model used for the chat completion.
	 * @param systemFingerprint This fingerprint represents the backend configuration that the model runs with. Can be
	 * used in conjunction with the seed request parameter to understand when backend changes have been made that might
	 * impact determinism.
	 * @param object The object type, which is always 'chat.completion.chunk'.
	 */
	@JsonInclude(Include.NON_NULL)
	public record ChatCompletionChunk(
			@JsonProperty("id") String id,
			@JsonProperty("choices") List<ChunkChoice> choices,
			@JsonProperty("created") Long created,
			@JsonProperty("model") String model,
			@JsonProperty("system_fingerprint") String systemFingerprint,
			@JsonProperty("object") String object) {

		/**
		 * Chat completion choice.
		 *
		 * @param finishReason The reason the model stopped generating tokens.
		 * @param index The index of the choice in the list of choices.
		 * @param delta A chat completion delta generated by streamed model responses.
		 * @param logprobs Log probability information for the choice.
		 */
		@JsonInclude(Include.NON_NULL)
		public record ChunkChoice(
				@JsonProperty("finish_reason") ChatCompletionFinishReason finishReason,
				@JsonProperty("index") Integer index,
				@JsonProperty("delta") ChatCompletionMessage delta,
				@JsonProperty("logprobs") LogProbs logprobs) {
		}
	}

	/**
	 * Creates a model response for the given chat conversation.
	 *
	 * @param chatRequest The chat completion request.
	 * @return Entity response with {@link ChatCompletion} as a body and HTTP status code and headers.
	 */
	public ResponseEntity<ChatCompletion> chatCompletionEntity(ChatCompletionRequest chatRequest, String message) {

		Assert.notNull(chatRequest, "The request body can not be null.");
		Assert.isTrue(!chatRequest.stream(), "Request must set the steam property to false.");

		return this.restClient.post()
				.uri("v1/text/chatcompletion_pro")
				.body(chatRequest)
				.retrieve()
				.toEntity(ChatCompletion.class);
	}

}

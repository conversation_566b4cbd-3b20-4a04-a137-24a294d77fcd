package com.huashi6.ai.chuzhan.dto.request.taskDetail;

import lombok.Data;

@Data
public class ChuZhanTaskDetailRequest {
    /**
     * 结果图片格式，目前支持`jpeg`和`png`两种格式的图片结果返回
     * jpeg拥有更好的压缩比，图片大小可以明显压缩，但是可能会对图片质量造成损失
     */
    private String imgFormat = ImgFormatEnum.PNG.toValue();
    /**
     * 结果图片质量，1~100的值，值越小代表对图片压缩率越大
     * 注意，在jpeg格式下图片为有损压缩，jpeg图片quality值越小文件也会越小，请调整合适的值用于平衡您对图片大小和图片质量的需求
     */
    private Long imgQuality = 100L;
    /**
     * 是否返回预览图，如果不需要预览图，可以传false，用于减少响应体大小
     */
    private Boolean preview = true;
    /**
     * 任务ID，由提交任务成功返回的任务ID，通常是paintingSign
     */
    private String taskId;
    /**
     * 等待直到任务完成再返回，可以利用此功能实现`同步调用`，如果此字段为true则此接口会自动等待直到此任务执行完成（成功或者失败）后再返回
     */
    private Boolean waitUtilEnd = false;
}
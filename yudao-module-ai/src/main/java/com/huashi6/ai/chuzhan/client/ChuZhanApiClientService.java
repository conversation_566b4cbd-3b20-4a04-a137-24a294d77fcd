package com.huashi6.ai.chuzhan.client;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huashi6.ai.chuzhan.client.api.ChuZhanApi;
import com.huashi6.ai.chuzhan.dto.request.ChuZhanTxtImgRequest;
import com.huashi6.ai.chuzhan.dto.request.taskDetail.ChuZhanTaskDetailRequest;
import com.huashi6.ai.chuzhan.dto.response.ChuZhanApiResponse;
import com.huashi6.ai.chuzhan.dto.response.taskDetail.ChuZhanTaskDetailResponse;
import com.huashi6.ai.chuzhan.utils.okhttp.OkhttpClientUtil;
import lombok.extern.slf4j.Slf4j;

/**
 */
@Slf4j
public class ChuZhanApiClientService {

    private final ChuZhan<PERSON><PERSON> chuZhanApi;

    public ChuZhanApiClientService(ChuZhan<PERSON><PERSON> chuZhanApi) {
        this.chuZhanApi = chuZhanApi;
    }

    /**
     * 文生图api
     *
     * @param request
     * @return
     */
    public ChuZhanApiResponse txt2img(ChuZhanTxtImgRequest request) {
        log.info("[txt2img][request:{}]", JSON.toJSON(request));
        Object body = JSON.toJSON(request);
        Object resp = OkhttpClientUtil.execute(chuZhanApi.txt2img(body));
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(JSON.toJSONString(resp), ChuZhanApiResponse.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取绘画任务详情
     */
    public ChuZhanTaskDetailResponse getTaskDetail(ChuZhanTaskDetailRequest taskDetailReq) {
        log.info("[getTaskDetail][taskDetailReq:{}]", JSON.toJSONString(taskDetailReq));
        Object resp = OkhttpClientUtil.execute(chuZhanApi.getTaskDetail(JSON.toJSON(taskDetailReq)));
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(JSON.toJSONString(resp), ChuZhanTaskDetailResponse.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}

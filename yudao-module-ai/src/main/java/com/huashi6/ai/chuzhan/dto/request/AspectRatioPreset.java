package com.huashi6.ai.chuzhan.dto.request;

import java.io.IOException;

/**
 * 图片宽高比预设，此字段会在保持`总像素量`(width*height)不变的前提下，重设width和height的值，用于快速设置出特定宽高比的图片
 */
public enum AspectRatioPreset {
    THE_11, THE_12, THE_169, THE_34, THE_43, THE_916;

    public String toValue() {
        switch (this) {
            case THE_11: return "1:1";
            case THE_12: return "1:2";
            case THE_169: return "16:9";
            case THE_34: return "3:4";
            case THE_43: return "4:3";
            case THE_916: return "9:16";
        }
        return null;
    }

    public static AspectRatioPreset forValue(String value) throws IOException {
        if (value.equals("1:1")) {
            return THE_11;
        }
        if (value.equals("1:2")) {
            return THE_12;
        }
        if (value.equals("16:9")) {
            return THE_169;
        }
        if (value.equals("3:4")) {
            return THE_34;
        }
        if (value.equals("4:3")) {
            return THE_43;
        }
        if (value.equals("9:16")) {
            return THE_916;
        }
        throw new IOException("Cannot deserialize AspectRatioPreset");
    }
}

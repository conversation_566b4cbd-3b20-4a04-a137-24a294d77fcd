package com.huashi6.ai.chuzhan.client;

import com.huashi6.ai.chuzhan.client.api.ChuZhanApi;
import com.huashi6.ai.chuzhan.config.ChuZhanApiProperties;
import com.huashi6.ai.chuzhan.utils.okhttp.OkhttpClientUtil;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import retrofit2.Retrofit;

import java.util.concurrent.TimeUnit;

/**
 */
public class ChuZhanClientFactory {

    private static Long[] delayMillis;

    private static ChuZhanApiProperties chuZhanApiProperties;

    public ChuZhanClientFactory(ChuZhanApiProperties chuZhanApiProperties){
        ChuZhanClientFactory.chuZhanApiProperties = chuZhanApiProperties;
        delayMillis = chuZhanApiProperties.getDelayMillis();
    }

    public static OkHttpClient getClient(String sdApiKey) {
       String authToken;
        if (sdApiKey == null){
            authToken = chuZhanApiProperties.getApiKey();
        } else {
            authToken = sdApiKey;
        }

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.MINUTES)//设置连接超时时间
                .writeTimeout(15, TimeUnit.MINUTES)// 设置写入超时时间
                .readTimeout(15, TimeUnit.MINUTES)// 设置读取超时时间
                .addInterceptor(chain -> {
                    // 设置请求头
                    Request request = chain.request()
                            .newBuilder()
                            .header("Auth-Token" , authToken)
                            .build();
                    return chain.proceed(request);
                })
                .connectionPool(new ConnectionPool(20, 30L, TimeUnit.MINUTES)).build();

        return client;
    }

    public static ChuZhanApiClientService createService(String sdHostUrl, String sdApiKey) {
        OkHttpClient client = getClient(sdApiKey);

        Retrofit retrofit = OkhttpClientUtil.defaultRetrofit(client, sdHostUrl);

        return new ChuZhanApiClientService(retrofit.create(ChuZhanApi.class));

    }

    public static Long[] getDelayMillis() {
        return delayMillis;
    }

    public static ChuZhanApiProperties getChuZhanApiProperties() {
        return chuZhanApiProperties;
    }

}

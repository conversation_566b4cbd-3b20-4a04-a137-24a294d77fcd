package com.huashi6.ai.chuzhan.config;

import com.huashi6.ai.chuzhan.client.ChuZhanClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 芋道 AI 自动配置
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableConfigurationProperties(ChuZhanApiProperties.class)
@Slf4j
public class ChuZhanAutoConfiguration {

    @Bean
    @ConditionalOnProperty(value = "yudao.ai.chuzhan.enable", havingValue = "true")
    public ChuZhanClientFactory chuZhanClientFactory(ChuZhanApiProperties chuZhanApiProperties) {
        return new ChuZhanClientFactory(chuZhanApiProperties);
    }

}
package com.huashi6.ai.chuzhan.dto.response;

import lombok.Data;

/**
 * @Description: 响应
 * @Date: 2024/4/27 00:25
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class ChuZhanApiResponse {
    /**
     * API统一响应码，错误码请参考[此文档](https://app.apifox.com/project/3150904)
     * 响应码,0:成功,其他失败
     * 错误码	错误解释
     * 1	服务器内部错误
     * 3	业务相关错误
     * 1001	接口认证失败
     * 1002	积分不足
     * 1003	参数错误
     * 1004	词条中涉及敏感词汇，禁止提交任务
     * 1005	任务数达到上限，请等在运行中的任务完成后再进行提交
     */
    private long code;

    private ResponseData data;
    /**
     * 错误消息，如果本次接口请求错误，则会返回对应的错误描述
     */
    private String msg;
    /**
     * 是否处理成功，简易字段用于区分本次请求接口是否处理成功
     */
    private boolean success;
}
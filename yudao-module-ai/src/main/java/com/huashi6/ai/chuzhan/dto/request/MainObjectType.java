package com.huashi6.ai.chuzhan.dto.request;

import java.io.IOException;

/**
 * @Description: TODO
 * @Date: 2024/4/27 00:09
 * @Author: zhangq
 * @Version: 1.0
 */
public enum MainObjectType {
    GENERAL,//通用场景识别，用于自动识别画面中的主体
    HUMAN;//识别并保留画面中的人物

    public String toValue() {
        switch (this) {
            case GENERAL: return "general";
            case HUMAN: return "human";
        }
        return null;
    }

    public static MainObjectType forValue(String value) throws IOException {
        if (value.equals("general")) {
            return GENERAL;
        }
        if (value.equals("human")) {
            return HUMAN;
        }
        throw new IOException("Cannot deserialize MainObjectType");
    }
}

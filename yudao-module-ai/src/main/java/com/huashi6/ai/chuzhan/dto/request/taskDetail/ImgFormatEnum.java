package com.huashi6.ai.chuzhan.dto.request.taskDetail;

import java.io.IOException;

/**
 * 结果图片格式，目前支持`jpeg`和`png`两种格式的图片结果返回
 * jpeg拥有更好的压缩比，图片大小可以明显压缩，但是可能会对图片质量造成损失
 */
public enum ImgFormatEnum {

    JPEG, PNG;

    public String toValue() {
        switch (this) {
            case JPEG: return "jpeg";
            case PNG: return "png";
        }
        return null;
    }

    public static ImgFormatEnum forValue(String value) throws IOException {
        if (value.equals("jpeg")) return JPEG;
        if (value.equals("png")) return PNG;
        throw new IOException("Cannot deserialize ImgFormat");
    }
}

package com.huashi6.ai.chuzhan.dto.request;

import java.io.IOException;

/**
 * 清晰度预设，清晰度预设旨在通过一个简单的配置来自动设置多个参数来达到出特定清晰度图片效果，与web端`清晰度`选项功能对应。
 * 注意：设置此字段会覆盖`width`、`height`、`hrScale`、`upscale`字段的值
 */
public enum ResolutionPreset {
    EXTREME, HIGH, NORMAL, ULTRA;

    public String toValue() {
        switch (this) {
            case EXTREME: return "extreme";
            case HIGH: return "high";
            case NORMAL: return "normal";
            case ULTRA: return "ultra";
        }
        return null;
    }

    public static ResolutionPreset forValue(String value) throws IOException {
        if (value.equals("extreme")) {
            return EXTREME;
        }
        if (value.equals("high")) {
            return HIGH;
        }
        if (value.equals("normal")) {
            return NORMAL;
        }
        if (value.equals("ultra")) {
            return ULTRA;
        }
        throw new IOException("Cannot deserialize ResolutionPreset");
    }
}
package com.huashi6.ai.chuzhan.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/4/27 00:08
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class Controlnet {
    /**
     * 默认控制图，如果此字段传值，可以在controlnet的unit中没有传对应的image时候，用此image进行控制，可以认为是一个默认控制图
     * 支持base64与图片url（需公网url）
     */
    @Schema(description = "默认控制图，如果此字段传值，可以在controlnet的unit中没有传对应的image时候，用此image进行控制，可以认为是一个默认控制图")
    private String image;
    /**
     * controlnet控制单元列表，最多可以设置3个控制单元,最少需要设置一个
     */
    @Schema(description = "controlnet控制单元列表，最多可以设置3个控制单元,最少需要设置一个")
    private List<Unit> units;
}

package com.huashi6.ai.chuzhan.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class ChuZhanTask {

    /**
     * 任务状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum TaskStatusEnum {

        /**
         * 任务当前已经进入队列，等待执行中
         */
        IN_QUEUE("in_queue"),
        /**
         * 任务当前正在执行中，请继续轮询此接口获取最新状态
         */
        IN_RUNNING("running"),
        /**
         * 任务执行结束，并且结果为成功，可以获取任务结果
         */
        SUCCESS("success"),
        /**
         * 任务执行结束，但是结果为失败，可以获取失败原因
         */
        FAILURE("fail"),
        ;

        private final String state;

        public static TaskStatusEnum forValue(String value) throws IOException {
            if (value.equals("success")) return SUCCESS;
            if (value.equals("fail")) return FAILURE;
            if (value.equals("running")) return IN_RUNNING;
            if (value.equals("in_queue")) return IN_QUEUE;
            throw new IOException("Cannot deserialize TaskStatusEnum");
        }
    }
}

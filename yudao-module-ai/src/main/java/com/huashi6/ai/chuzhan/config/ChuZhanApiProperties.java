package com.huashi6.ai.chuzhan.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 **/
@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ConfigurationProperties(prefix = "yudao.ai.chu-zhan")
public class ChuZhanApiProperties {

    private String enable;
    private String baseUrl;

    private String apiKey;

    private Boolean notifyBack;
    private String notifyUrl;

    private Long[] delayMillis;
}

package com.huashi6.ai.chuzhan.dto.response.taskDetail;

import lombok.Data;

/**
 * @Description: 响应
 * @Date: 2024/4/27 00:25
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class ChuZhanTaskDetailResponse {
    /**
     * API统一响应码，错误码请参考[此文档](https://app.apifox.com/project/3150904)
     */
    private long code;

    private DetailData data;
    /**
     * 错误消息，如果本次接口请求错误，则会返回对应的错误描述
     */
    private String msg;
    /**
     * 是否处理成功，简易字段用于区分本次请求接口是否处理成功
     */
    private boolean success;
}
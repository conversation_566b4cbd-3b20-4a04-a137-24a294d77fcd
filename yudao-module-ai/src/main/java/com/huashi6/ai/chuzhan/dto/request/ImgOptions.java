package com.huashi6.ai.chuzhan.dto.request;

import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/4/27 00:09
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class ImgOptions {
    /**
     * 自动检测图片中的人脸，使生成后的人脸与原始人脸保持高度相似。
     * `注意，开启此功能需额外扣除2积分`
     */
    private Boolean facePreservation;
    /**
     * 开启人脸保持后，画面中最多处理的人脸数量，默认为1，最大值可以为5
     */
    private Long facePreservationCount;
    /**
     * 是否自动检测图片中的人物性别，尽量使生成前后的人物性别不发生变化，如果图片中有多个人物性别则效果会不准确。
     * `注意，开启此功能需额外扣除1积分`
     */
    private Boolean genderDetect;
    /**
     *
     * imgOptions参考图，在最外层`img`参数没有传的时候，如果有传imgOptions的其他参数，则此img为必传，否则使用最外层img作为参考图（注意：redrawBackground、removeBackground仍然只在图生图模式下生效）
     */
    private String img;
    /**
     * 自定识别画面中的主体，用于背景相关处理。取值：
     * - 当`removeBackground`或者`redrawBackground`为true的时候，画面中的主体类型，请根据您的画面中的实际情况传值
     */
    private MainObjectType mainObjectType;
    /**
     * 自动识别图片中的词条，用于生图的时候，自动检测图片中的词条生成（如果prompt有传，则会覆盖掉）
     * 如果`genderDetect` 字段为true，则此字段不会生效
     */
    private Boolean promptDetect;
    /**
     * 是否保留画面中的主体，仅绘制画面中的背景`注意：开启此功能需额外扣除2积分`
     */
    private Boolean redrawBackground;
    /**
     * 从原始背景中重绘，是否完全清除原始背景，不从原始背景中重绘。
     * 需要denoisingStrength设置为1，且搭配controlnet获得最好的效果
     */
    private Boolean redrawBackgroundFromOrigin;
    /**
     * 移除画面背景，是否自动移除图片中的人物背景，一般用于重绘背景有效，`注意：开启此功能需额外扣除2积分`
     */
    private Boolean removeBackground;
}
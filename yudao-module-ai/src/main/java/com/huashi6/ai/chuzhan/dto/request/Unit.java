package com.huashi6.ai.chuzhan.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2024/4/27 00:10
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class Unit {
    /**
     * 结束控制时机，在绘制过程中，controlnet在什么进度结束干预，取值0~1,默认为1
     */
    @Schema(description = "结束控制时机，在绘制过程中，controlnet在什么进度结束干预，取值0~1,默认为1")
    private Double controlEnd;
    /**
     * 控制模式，* `0`:平衡模式
     * * `1`: 关键字的效果更强，controlnet本身的控制效果会减弱
     * * `2`: controlnet的控制会加强，更忽略关键词的效果
     */
    @Schema(description = "控制模式，* `0`:平衡模式\n" +
            "* `1`: 关键字的效果更强，controlnet本身的控制效果会减弱\n" +
            "* `2`: controlnet的控制会加强，更忽略关键词的效果")
    private Long controlMode;
    /**
     * 控制开始时机，在绘制过程中，controlnet干预开始时机点，取值0~1,默认为0
     */
    @Schema(description = "控制开始时机，在绘制过程中，controlnet干预开始时机点，取值0~1,默认为0")
    private Double controlStart;
    /**
     * 参考图，在图生图模式非必传，不传默认为`img`参数，参数可以为`base64`图片或者图片url（请确保公网可以访问）
     */
    @Schema(description = "参考图，在图生图模式非必传，不传默认为`img`参数，参数可以为`base64`图片或者图片url（请确保公网可以访问）")
    private String image;
    /**
     * 不同控制类型特定参数
     */
    @Schema(description = "不同控制类型特定参数")
    private Double paramsA;
    /**
     * 不同控制类型特定参数
     */
    @Schema(description = "不同控制类型特定参数")
    private Double paramsB;
    /**
     * 是否对图片进行预处理，如果您的图片是原始图片则默认是需要对图片进行预处理提取成特定controlnet模型需要的图片，因此默认为true
     */
    @Schema(description = "是否对图片进行预处理，如果您的图片是原始图片则默认是需要对图片进行预处理提取成特定controlnet模型需要的图片，因此默认为true")
    private Boolean preprocess;
    /**
     *
     * 控制类型，控制类型用于指定特定的控制模型，可选值请参考[此文档](https://chuzhanai.apifox.cn/doc-3672565#%E5%85%A8%E9%83%A8%E5%8F%AF%E7%94%A8%E6%8E%A7%E5%88%B6%E5%99%A8)
     */
    @Schema(description = "控制类型，控制类型用于指定特定的控制模型，可选值请参考[此文档](https://chuzhanai.apifox.cn/doc-3672565#%E5%85%A8%E9%83%A8%E5%8F%AF%E7%94%A8%E6%8E%A7%E5%88%B6%E5%99%A8)")
    private String type;
    /**
     * 控制权重，取值范围0~2，控制权重越大，对画面影响效果越明显，默认为1
     */
    @Schema(description = "控制权重，取值范围0~2，控制权重越大，对画面影响效果越明显，默认为1")
    private Double weight;
}

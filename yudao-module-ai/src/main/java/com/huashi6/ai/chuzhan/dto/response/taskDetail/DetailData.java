package com.huashi6.ai.chuzhan.dto.response.taskDetail;

import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/7/20 13:21
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class DetailData {
    /**
     * 图片审核结果，仅在state为`success`才会返回。当生成单图的时候，单图的审核结果会直接在这里返回
     */
    private Long audit;
    /**
     * 绘画预览图，用于表现绘画过程的预览图，base64格式，仅在任务没有完成的时候返回
     */
    private String currentImage;
    /**
     * 本次生成的图片列表，仅在state为`success`才会返回。不管是单图还是多图，都会单独在这个数组里面返回每一个图片
     */
    private List<Image> images;
    /**
     * 单图URL，仅在state为`success`才会返回。图片URL的有效期为半个小时，请获取后自行进行保存
     */
    private String imgUrl;
    /**
     * 任务进度，取值范围0~1.0
     */
    private double progress;
    /**
     * 任务状态，任务状态共有4种：
     * 1. `in_queue`任务当前已经进入队列，等待执行中
     * 2. `running`任务当前正在执行中，请继续轮询此接口获取最新状态
     * 3. `success`任务执行结束，并且结果为成功，可以获取任务结果
     * 4. `fail` 任务执行结束，但是结果为失败，可以获取失败原因
     */
    private String state;
}

package com.huashi6.ai.chuzhan.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 触站AI
 *提交绘画的参数，此接口为异步提交任务接口，并不直接返回最终的绘画结果，请调用获取任务详情接口获取绘画的进度或者结果
 */
@NoArgsConstructor
@Data
public class ChuZhanTxtImgRequest implements Serializable {
    /**
     * 单次批量生成数量，单次生成图片数量不可以超过6个
     */
    @Schema(description = "单次批量生成数量，单次生成图片数量不可以超过6个")
    private Long batchSize;
    /**
     * 结果回调地址，支持IP等URL[请参考回调使用指南](https://chuzhanai.apifox.cn/doc-3556414)
     */
    @Schema(description = "结果回调地址，支持IP等URL[请参考回调使用指南](https://chuzhanai.apifox.cn/doc-3556414)")
    private String callback;
    /**
     *
     * 引导系数，用于引导画面与描述词符合程度，取值`1~30`，取值越大越符合描述词，但是会限制AI发挥空间，取值小越不像描述词。（不同风格模型对此值有不同的预设，一般情况下此值不需要传，用风格默认即可）
     */
    @Schema(description = "引导系数，用于引导画面与描述词符合程度，取值`1~30`，取值越大越符合描述词，但是会限制AI发挥空间，取值小越不像描述词。（不同风格模型对此值有不同的预设，一般情况下此值不需要传，用风格默认即可）")
    private Double cfgScale;
    /**
     * controlnet功能实现，详细使用请参考[此文档](https://chuzhanai.apifox.cn/doc-3672565)
     */
    @Schema(description = "controlnet功能实现，详细使用请参考[此文档](https://chuzhanai.apifox.cn/doc-3672565)")
    private Controlnet controlnet;
    /**
     * 重绘幅度，重绘幅度仅在`img字段`有传值的时候生效，重绘幅度越大代表生成图与原图越不相似，取值范围`0~1`
     * 取值为0时，代表不会对img进行修改，出来的图与img几乎一致
     * 取值为1是，代表对img进行完全修改，与img无任何相似
     */
    @Schema(description = "重绘幅度，重绘幅度仅在`img字段`有传值的时候生效，重绘幅度越大代表生成图与原图越不相似，取值范围`0~1`\n" +
            "取值为0时，代表不会对img进行修改，出来的图与img几乎一致\n" +
            "取值为1是，代表对img进行完全修改，与img无任何相似")
    private Double denoisingStrength;
    /**
     *
     * 细节倍率，取值范围为`1~9`小于5则画面偏向简单扁平话、草稿化大于5则画面偏向添加更多光影、服饰、头发等细节详细效果请参考[此文章](https://www.huashi6.com/article/detail-17448.html)
     */
    @Schema(description = "细节倍率，取值范围为`1~9`小于5则画面偏向简单扁平话、草稿化大于5则画面偏向添加更多光影、服饰、头发等细节详细效果请参考[此文章](https://www.huashi6.com/article/detail-17448.html)")
    private Long detailsLevel;
    /**
     * 脸部修复开关，开启后系统将会对生成图片人物脸部进行高级修复处理，大大改善人物脸部崩坏情况，开启需要单独`扣减2积分`
     */
    @Schema(description = "脸部修复开关，开启后系统将会对生成图片人物脸部进行高级修复处理，大大改善人物脸部崩坏情况，开启需要单独`扣减2积分`")
    private Boolean faceFix;
    /**
     * 生成图片高度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生
     */
    @Schema(description = "生成图片高度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生")
    private Integer height;
    /**
     * 高清化倍率，对生成的图片进行高清化处理，仅在`文生图`模式有效，取值范围为1~3之间
     */
    @Schema(description = "高清化倍率，对生成的图片进行高清化处理，仅在`文生图`模式有效，取值范围为1~3之间")
    private Double hrScale;
    /**
     * 高清处理步数，高清迭代步数，（不建议少于15，会严重影响画面生成效果）
     */
    @Schema(description = "高清处理步数，高清迭代步数，（不建议少于15，会严重影响画面生成效果）")
    private Integer hrSteps;
    /**
     * 参考图片，生成参考图，此字段传值则代表模式为`图生图`，同时支持base64数据格式和url（注意：url必须可以公网访问）
     */
    @Schema(description = "参考图片，生成参考图，此字段传值则代表模式为`图生图`，同时支持base64数据格式和url（注意：url必须可以公网访问）")
    private String img;
    /**
     * 图片高级功能参数，可以通过一系列参数实现高级效果
     */
    @Schema(description = "图片高级功能参数，可以通过一系列参数实现高级效果")
    private ImgOptions imgOptions;
    /**
     * 黑白二值遮罩蒙版图，用于局部重绘处理，可以是图片URL或者base64格式字符串。
     * 注意：遮罩图黑色区域代表需要重绘的部分
     * 局部重绘功能请参考[此文档](https://chuzhanai.apifox.cn/doc-3934922)
     */
    @Schema(description = "黑白二值遮罩蒙版图，用于局部重绘处理，可以是图片URL或者base64格式字符串。\n" +
            "注意：遮罩图黑色区域代表需要重绘的部分\n" +
            "局部重绘功能请参考[此文档](https://chuzhanai.apifox.cn/doc-3934922)")
    private String maskImg;
    /**
     *
     * 风格ID，指定要生成对应风格预设的图片，触站AI官方会预设大量可以直接进行生产使用的风格预设，部分可用ID请参考[此文档](https://chuzhanai.apifox.cn/doc-3699065)
     * 此字段也可以使用模型训练API自定义风格产出的结果，详见[模型训练](https://chuzhanai.apifox.cn/api-123833428)
     */
    @Schema(description = "风格ID，指定要生成对应风格预设的图片，触站AI官方会预设大量可以直接进行生产使用的风格预设，部分可用ID请参考[此文档](https://chuzhanai.apifox.cn/doc-3699065)")
    private Integer modelStyleId;
    /**
     * 负面词条，用于排除画面中要出现的内容描述，支持中英文描述
     */
    @Schema(description = "负面词条，用于排除画面中要出现的内容描述，支持中英文描述")
    private String negativePrompt;
    /**
     * 自定义请求回调标识，任意长度不超过32的字符串，具体[请参考回调使用指南](https://chuzhanai.apifox.cn/doc-3556414)
     */
    @Schema(description = "自定义请求回调标识，任意长度不超过32的字符串，具体[请参考回调使用指南](https://chuzhanai.apifox.cn/doc-3556414)")
    private String nonce;
    /**
     * 预测模拟积分消耗，如果传值为true则代表本次是测试模拟积分消耗量，不进行真正的绘画操作，仅用于计算同参数下积分消耗量，不扣减任何积分
     */
    @Schema(description = "预测模拟积分消耗，如果传值为true则代表本次是测试模拟积分消耗量，不进行真正的绘画操作，仅用于计算同参数下积分消耗量，不扣减任何积分")
    private Boolean predictConsume;
    /**
     * 描述词条，描述画面中需要出现的内容，支持中英文描述
     * 详细词条技巧请参考[此文章](https://www.huashi6.com/article/detail-13420.html)
     */
    @Schema(description = "描述词条，描述画面中需要出现的内容，支持中英文描述\n" +
            "详细词条技巧请参考[此文章](https://www.huashi6.com/article/detail-13420.html)")
    private String prompt;
    /**
     * 随机种子，随机种子可以用于进行画面重现，默认不传则为随机生成
     */
    @Schema(description = "随机种子，随机种子可以用于进行画面重现，默认不传则为随机生成")
    private Integer seed;
    /**
     *
     * 绘图步数，绘图步数与最终图片生成质量有关系，通常模型风格的默认步数为20步，如果有更高的需求或者能够对画面质量降低有一定容忍度，可以尝试提高或者减少步数。注意：部分风格步数为独立配置，为获得最佳风格效果，此参数可以使用默认配置不用传
     */
    @Schema(description = "绘图步数，绘图步数与最终图片生成质量有关系，通常模型风格的默认步数为20步，如果有更高的需求或者能够对画面质量降低有一定容忍度，可以尝试提高或者减少步数。注意：部分风格步数为独立配置，为获得最佳风格效果，此参数可以使用默认配置不用传")
    private Integer steps;
    /**
     * 花纹贴图，是否生成花纹贴图，一般用于纹理生成使用
     */
    @Schema(description = "花纹贴图，是否生成花纹贴图，一般用于纹理生成使用")
    private Boolean tiling;
    /**
     * ai算法放大倍率，AI算法放大倍数
     */
    @Schema(description = "ai算法放大倍率，AI算法放大倍数")
    private Integer upscale;
    /**
     * 生成图片宽度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生
     */
    @Schema(description = "生成图片宽度，在未使用高清修复的情况下，图片尺寸不建议设置过小，过小的尺寸会导致画面中的物体轮廓生成不完整，会造成比如人脸生成崩坏等问题的产生")
    private Integer width;

}
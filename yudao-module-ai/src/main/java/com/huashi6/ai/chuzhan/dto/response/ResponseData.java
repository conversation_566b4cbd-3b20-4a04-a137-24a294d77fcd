package com.huashi6.ai.chuzhan.dto.response;

import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/4/27 00:26
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class ResponseData {
    /**
     * 账户当前余额
     */
    private long balance;
    /**
     * 参数需要消耗积分明细，predictConsume为true时候返回此值
     */
    private List<ConsumeDetail> consumeDetail;
    /**
     * 预计消耗积分量，predictConsume为true时候返回此值
     */
    private Long estimateUsed;
    /**
     *
     * 绘画任务ID，可以用于[绘画详情接口](https://chuzhanai.apifox.cn/api-123807409)查询进度或者获取结果，如果`predictConsume`为true则接口不会返回此值
     */
    private String paintingSign;
    /**
     * 任务限制数，并发任务数
     */
    private Long taskLimitCount;
    /**
     * 本次扣减积分
     */
    private Integer used;
}

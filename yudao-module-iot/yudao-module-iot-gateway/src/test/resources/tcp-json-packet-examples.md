# TCP JSON 格式协议说明

## 1. 协议概述

TCP JSON 格式协议采用纯 JSON 格式进行数据传输，具有以下特点：

- **标准化**：使用标准 JSON 格式，易于解析和处理
- **可读性**：人类可读，便于调试和维护
- **扩展性**：可以轻松添加新字段，向后兼容
- **跨平台**：JSON 格式支持所有主流编程语言
- **安全优化**：移除冗余的 deviceId 字段，提高安全性

## 2. 消息格式

### 2.1 基础消息结构

```json
{
  "id": "消息唯一标识",
  "method": "消息方法",
  "params": {
    // 请求参数
  },
  "data": {
    // 响应数据
  },
  "code": 响应码,
  "msg": "响应消息",
  "timestamp": 时间戳
}
```

**⚠️ 重要说明**：
- **不包含 deviceId 字段**：由服务器通过 TCP 连接上下文自动确定设备 ID
- **避免伪造攻击**：防止设备伪造其他设备的 ID 发送消息

### 2.2 字段详细说明

| 字段名 | 类型 | 必填 | 用途 | 说明 |
|--------|------|------|------|------|
| id | String | 是 | 所有消息 | 消息唯一标识 |
| method | String | 是 | 所有消息 | 消息方法，如 `auth`、`thing.property.post` |
| params | Object | 否 | 请求消息 | 请求参数，具体内容根据method而定 |
| data | Object | 否 | 响应消息 | 响应数据，服务器返回的结果数据 |
| code | Integer | 否 | 响应消息 | 响应码，0=成功，其他=错误 |
| msg | String | 否 | 响应消息 | 响应提示信息 |
| timestamp | Long | 是 | 所有消息 | 时间戳（毫秒），编码时自动生成 |

### 2.3 消息分类

#### 2.3.1 请求消息（上行）
- **特征**：包含 `params` 字段，不包含 `code`、`msg` 字段
- **方向**：设备 → 服务器
- **用途**：设备认证、数据上报、状态更新等

#### 2.3.2 响应消息（下行）
- **特征**：包含 `code`、`msg` 字段，可能包含 `data` 字段
- **方向**：服务器 → 设备  
- **用途**：认证结果、指令响应、错误提示等

## 3. 消息示例

### 3.1 设备认证 (auth)

#### 认证请求格式
**消息方向**：设备 → 服务器

```json
{
  "id": "auth_1704067200000_123",
  "method": "auth",
  "params": {
    "clientId": "device_001",
    "username": "productKey_deviceName",
    "password": "设备密码"
  },
  "timestamp": 1704067200000
}
```

**认证参数说明：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| clientId | String | 是 | 客户端唯一标识，用于连接管理 |
| username | String | 是 | 设备用户名，格式为 `productKey_deviceName` |
| password | String | 是 | 设备密码，在设备管理平台配置 |

#### 认证响应格式
**消息方向**：服务器 → 设备

**认证成功响应：**
```json
{
  "id": "response_auth_1704067200000_123",
  "method": "auth",
  "data": {
    "success": true,
    "message": "认证成功"
  },
  "code": 0,
  "msg": "认证成功",
  "timestamp": 1704067200001
}
```

**认证失败响应：**
```json
{
  "id": "response_auth_1704067200000_123", 
  "method": "auth",
  "data": {
    "success": false,
    "message": "认证失败：用户名或密码错误"
  },
  "code": 401,
  "msg": "认证失败",
  "timestamp": 1704067200001
}
```

### 3.2 属性数据上报 (thing.property.post)

**消息方向**：设备 → 服务器

**示例：温度传感器数据上报**
```json
{
  "id": "property_1704067200000_456",
  "method": "thing.property.post",
  "params": {
    "temperature": 25.5,
    "humidity": 60.2,
    "pressure": 1013.25,
    "battery": 85,
    "signal_strength": -65
  },
  "timestamp": 1704067200000
}
```

### 3.3 设备状态更新 (thing.state.update)

**消息方向**：设备 → 服务器

**示例：心跳请求**
```json
{
  "id": "heartbeat_1704067200000_321",
  "method": "thing.state.update",
  "params": {
    "state": "online",
    "uptime": 86400,
    "memory_usage": 65.2,
    "cpu_usage": 12.8
  },
  "timestamp": 1704067200000
}
```

## 4. 编解码器标识

```java
public static final String TYPE = "TCP_JSON";
```

## 5. 协议优势

- **开发效率高**：JSON 格式，开发和调试简单
- **跨语言支持**：所有主流语言都支持 JSON
- **可读性优秀**：可以直接查看消息内容
- **扩展性强**：可以轻松添加新字段
- **安全性高**：移除 deviceId 字段，防止伪造攻击

## 6. 与二进制协议对比

| 特性 | JSON协议 | 二进制协议 |
|------|----------|------------|
| 开发难度 | 低 | 高 |
| 调试难度 | 低 | 高 |
| 可读性 | 优秀 | 差 |
| 数据大小 | 中等 | 小（节省30-50%） |
| 解析性能 | 中等 | 高 |
| 学习成本 | 低 | 高 |

**推荐场景**：
- ✅ **开发调试阶段**：调试友好，开发效率高
- ✅ **快速原型开发**：实现简单，快速迭代
- ✅ **多语言集成**：广泛的语言支持
- ❌ **高频数据传输**：建议使用二进制协议
- ❌ **带宽受限环境**：建议使用二进制协议
package cn.iocoder.yudao.module.pay.convert.coin;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pay.api.coin.dto.MemberCoinRespDTO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.transaction.PayCoinTransactionRespVO;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinTransactionDO;
import cn.iocoder.yudao.module.pay.service.coin.bo.CoinTransactionCreateReqBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PayCoinTransactionConvert {

    PayCoinTransactionConvert INSTANCE = Mappers.getMapper(PayCoinTransactionConvert.class);

    PageResult<PayCoinTransactionRespVO> convertPage2(PageResult<AiPayCoinTransactionDO> page);

    AiPayCoinTransactionDO convert(CoinTransactionCreateReqBO bean);

    List<MemberCoinRespDTO> convertList(List<AiPayCoinTransactionDO> list);

    default List<MemberCoinRespDTO> convertList(long userId,List<AiPayCoinTransactionDO> list) {
        List<MemberCoinRespDTO> result = convertList(list);
        result.forEach(item -> item.setUserId(userId));
        return result;
    }
}

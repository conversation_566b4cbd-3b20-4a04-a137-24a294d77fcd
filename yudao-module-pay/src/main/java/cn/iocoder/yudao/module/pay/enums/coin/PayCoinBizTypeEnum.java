package cn.iocoder.yudao.module.pay.enums.coin;

import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 算力交易业务分类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum PayCoinBizTypeEnum implements ArrayValuable<Integer> {

    RECHARGE(1, "充值","购买算力包-{}",true),//增加
    RECHARGE_REFUND(2, "充值退款","取消算力包-{}",false),
    PAYMENT(3, "订单算力抵扣","{}",false),//消耗
    PAYMENT_REFUND(4, "订单算力抵扣（整单取消）","{}-算力退回",true),//增加
    PAYMENT_REFUND_ITEM(5, "订单算力抵扣（单个退款）","{}-算力退回",true),//增加
    UPDATE_BALANCE(6, "更新余额","管理员修改算力",true),

    //注册赠送
    REGISTER_GIFT(7, "注册赠送","新用户福利赠送", true),
    //邀请好友
    INVITE_GIFT(8, "邀请赠送","邀请好友", true),
    //会员活动
    ACTIVITY_GIFT(9, "会员活动赠送","会员活动获得", true),
    //兑换码兑换
    EXCHANGE_GIFT(10, "兑换码兑换","兑换码兑换获得", true),
    //
    ;

    /**
     * 业务分类
     */
    private final Integer type;

    /**
     * 名字
     */
    private final String name;
    /**
     * 说明
     */
    private final String description;

    /**
     * 是否为扣减积分
     */
    private final boolean add;

    // 用于存储动态描述的 Map
    private static final Map<Integer, String> dynamicDescriptions = new HashMap<>();

    // 设置动态描述
    public static void setDescription(Integer type, String description) {
        dynamicDescriptions.put(type, description);
    }

    // 获取动态描述
    public static String getDynamicDescription(Integer type) {
        return dynamicDescriptions.getOrDefault(type, getDescription(type));
    }

    public static String getDescription(Integer type) {
        PayCoinBizTypeEnum bizTypeEnum = valueOf(type);
        if (bizTypeEnum == null) {
            return null;
        }
        return bizTypeEnum.getDescription();
    }

    // 静态数组用于枚举类型的查询
    public static final Integer[] ARRAYS = Arrays.stream(values()).map(PayCoinBizTypeEnum::getType).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static PayCoinBizTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

    // 获取当前枚举的描述（优先返回动态描述，如果没有则返回静态描述）
    /*public String getDescription() {
        return dynamicDescriptions.getOrDefault(this.type, this.description);
    }*/

}

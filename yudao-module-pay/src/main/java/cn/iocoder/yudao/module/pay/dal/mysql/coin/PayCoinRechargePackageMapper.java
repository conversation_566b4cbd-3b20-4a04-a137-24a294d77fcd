package cn.iocoder.yudao.module.pay.dal.mysql.coin;


import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.rechargepackage.CoinRechargePackagePageReqVO;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinRechargePackageDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PayCoinRechargePackageMapper extends BaseMapperX<AiPayCoinRechargePackageDO> {

    default PageResult<AiPayCoinRechargePackageDO> selectPage(CoinRechargePackagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiPayCoinRechargePackageDO>()
                .likeIfPresent(AiPayCoinRechargePackageDO::getName, reqVO.getName())
                .eqIfPresent(AiPayCoinRechargePackageDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AiPayCoinRechargePackageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AiPayCoinRechargePackageDO::getPayPrice));
    }

    default AiPayCoinRechargePackageDO selectByName(String name) {
        return selectOne(AiPayCoinRechargePackageDO::getName, name);
    }

    default List<AiPayCoinRechargePackageDO> selectListByStatus(Integer type, Integer packageType) {
        return selectList(new LambdaQueryWrapperX<AiPayCoinRechargePackageDO>()
                .eqIfPresent(AiPayCoinRechargePackageDO::getType, type)
                .eqIfPresent(AiPayCoinRechargePackageDO::getPackageType, packageType)
                .eq(AiPayCoinRechargePackageDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
        );
    }

    default List<AiPayCoinRechargePackageDO> getActivityCoinRechargePackageList(Integer type, Integer packageType) {
        return selectList(new LambdaQueryWrapperX<AiPayCoinRechargePackageDO>()
                .eqIfPresent(AiPayCoinRechargePackageDO::getType, type)
                .neIfPresent(AiPayCoinRechargePackageDO::getPackageType, packageType)
                .eq(AiPayCoinRechargePackageDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
        );
    }

}

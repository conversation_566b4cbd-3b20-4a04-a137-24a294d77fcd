package cn.iocoder.yudao.module.pay.convert.activity;

import cn.iocoder.yudao.module.pay.controller.admin.activity.vo.PayCoinActivityRespVO;
import cn.iocoder.yudao.module.pay.dal.dataobject.activity.AiCoinActivityDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AiCoinActivityConvert {

    AiCoinActivityConvert INSTANCE = Mappers.getMapper(AiCoinActivityConvert.class);

    PayCoinActivityRespVO convert(AiCoinActivityDO activityDO);

}

package cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 钱包流水分页 Response VO")
@Data
public class AppPayCoinTransactionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "流水编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String no;

    @Schema(description = "业务分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer bizType;

    @Schema(description = "变动算力", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Long coin;

    @Schema(description = "流水说明", requiredMode = Schema.RequiredMode.REQUIRED, example = "土豆土豆")
    private String title;

    @Schema(description = "交易时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "流水描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "土豆土豆")
    private String description;
}

package cn.iocoder.yudao.module.pay.controller.app.activity;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pay.api.coin.PayCoinApi;
import cn.iocoder.yudao.module.pay.controller.app.activity.vo.AppPayCoinActivityPackageRespVO;
import cn.iocoder.yudao.module.pay.service.activity.PayCoinActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户app - AI 算力活动 DO")
@RestController
@RequestMapping("/pay/coin-activity")
@Validated
public class AppPayCoinActivityController {

    @Resource
    private PayCoinActivityService coinActivityService;

    @Resource
    private PayCoinApi payCoinApi;

    @GetMapping("/get")
    @Operation(summary = "获得AI 算力活动 DO")
    @Parameter(name = "type", description = "设备类型(0安卓 1苹果)", required = true, example = "0")
    public CommonResult<AppPayCoinActivityPackageRespVO> getAppCoinActivity(@RequestParam(value =  "type") Integer type) {
        AppPayCoinActivityPackageRespVO respVO = coinActivityService.getAppCoinActivity(getLoginUserId(),type);
        return success(respVO);
    }



}
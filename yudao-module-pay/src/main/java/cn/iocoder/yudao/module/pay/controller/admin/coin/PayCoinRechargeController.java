package cn.iocoder.yudao.module.pay.controller.admin.coin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayRefundNotifyReqDTO;
import cn.iocoder.yudao.module.pay.service.coin.AiPayCoinRechargeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;

@Tag(name = "管理后台 - 算力充值")
@RestController
@RequestMapping("/pay/coin-recharge")
@Validated
@Slf4j
public class PayCoinRechargeController {

    @Resource
    private AiPayCoinRechargeService payCoinRechargeService;

    @PostMapping("/update-paid")
    @Operation(summary = "更新算力充值为已充值") // 由 pay-module 支付服务，进行回调，可见 PayNotifyJob
    @PermitAll // 无需登录， 内部校验实现
    public CommonResult<Boolean> updateCoinRechargerPaid(@Valid @RequestBody PayOrderNotifyReqDTO notifyReqDTO) {
        payCoinRechargeService.updateCoinRechargerPaid(Long.valueOf(notifyReqDTO.getMerchantOrderId()),
                notifyReqDTO.getPayOrderId());
        return success(true);
    }

    // TODO @jason：发起退款，要 post 操作哈；
    @GetMapping("/refund")
    @Operation(summary = "发起算力充值退款")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> refundCoinRecharge(@RequestParam("id") Long id) {
        payCoinRechargeService.refundCoinRecharge(id, getClientIP());
        return success(true);
    }

    @PostMapping("/update-refunded")
    @Operation(summary = "更新算力充值为已退款") // 由 pay-module 支付服务，进行回调，可见 PayNotifyJob
    @PermitAll // 无需登录， 内部校验实现
    public CommonResult<Boolean> updateCoinRechargeRefunded(@RequestBody PayRefundNotifyReqDTO notifyReqDTO) {
        payCoinRechargeService.updateCoinRechargeRefunded(
                Long.valueOf(notifyReqDTO.getMerchantOrderId()), notifyReqDTO.getPayRefundId());
        return success(true);
    }

}

package cn.iocoder.yudao.module.pay.controller.admin.coin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.coin.PayCoinPageReqVO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.coin.PayCoinRespVO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.coin.PayCoinUpdateBalanceReqVO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.coin.PayCoinUserReqVO;
import cn.iocoder.yudao.module.pay.convert.coin.PayCoinConvert;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinDO;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;
import cn.iocoder.yudao.module.pay.service.coin.AiPayCoinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.pay.enums.ErrorCodeConstants.COIN_NOT_FOUND;

@Tag(name = "管理后台 - 用户算力")
@RestController
@RequestMapping("/pay/coin")
@Validated
@Slf4j
public class PayCoinController {

    @Resource
    private AiPayCoinService payCoinService;

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('pay:coin:query')")
    @Operation(summary = "获得用户算力明细")
    public CommonResult<PayCoinRespVO> getCoin(PayCoinUserReqVO reqVO) {
        AiPayCoinDO Coin = payCoinService.getOrCreateCoin(reqVO.getUserId());
        return success(PayCoinConvert.INSTANCE.convert02(Coin));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员算力分页")
    @PreAuthorize("@ss.hasPermission('pay:coin:query')")
    public CommonResult<PageResult<PayCoinRespVO>> getCoinPage(@Valid PayCoinPageReqVO pageVO) {
        PageResult<AiPayCoinDO> pageResult = payCoinService.getCoinPage(pageVO);
        return success(PayCoinConvert.INSTANCE.convertPage(pageResult));
    }

    @PutMapping("/update-balance")
    @Operation(summary = "更新会员用户算力余额")
    @PreAuthorize("@ss.hasPermission('pay:coin:update-balance')")
    public CommonResult<Boolean> updateCoinBalance(@Valid @RequestBody PayCoinUpdateBalanceReqVO updateReqVO) {
        // 获得用户钱包
        AiPayCoinDO coin = payCoinService.getOrCreateCoin(updateReqVO.getUserId());
        if (coin == null) {
            log.error("[updateCoinBalance]，updateReqVO({}) 用户算力信息不存在.", updateReqVO);
            throw exception(COIN_NOT_FOUND);
        }

        // 更新钱包余额
        payCoinService.addCoinBalance(coin.getId(), String.valueOf(updateReqVO.getUserId()),
                PayCoinBizTypeEnum.UPDATE_BALANCE,0, updateReqVO.getBalance(),PayCoinBizTypeEnum.UPDATE_BALANCE.getDescription());
        return success(true);
    }

}

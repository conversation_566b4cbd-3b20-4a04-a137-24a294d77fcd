package cn.iocoder.yudao.module.pay.controller.admin.coin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.rechargepackage.CoinRechargePackageCreateReqVO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.rechargepackage.CoinRechargePackagePageReqVO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.rechargepackage.CoinRechargePackageRespVO;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.rechargepackage.CoinRechargePackageUpdateReqVO;
import cn.iocoder.yudao.module.pay.convert.coin.PayCoinRechargePackageConvert;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinRechargePackageDO;
import cn.iocoder.yudao.module.pay.service.coin.AiPayCoinRechargePackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 算力充值套餐")
@RestController
@RequestMapping("/pay/coin-recharge-package")
@Validated
public class PayCoinRechargePackageController {

    @Resource
    private AiPayCoinRechargePackageService coinRechargePackageService;

    @PostMapping("/create")
    @Operation(summary = "创建算力充值套餐")
    @PreAuthorize("@ss.hasPermission('pay:coin-recharge-package:create')")
    public CommonResult<Long> createCoinRechargePackage(@Valid @RequestBody CoinRechargePackageCreateReqVO createReqVO) {
        return success(coinRechargePackageService.createCoinRechargePackage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新算力充值套餐")
    @PreAuthorize("@ss.hasPermission('pay:coin-recharge-package:update')")
    public CommonResult<Boolean> updateCoinRechargePackage(@Valid @RequestBody CoinRechargePackageUpdateReqVO updateReqVO) {
        coinRechargePackageService.updateCoinRechargePackage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除算力充值套餐")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pay:coin-recharge-package:delete')")
    public CommonResult<Boolean> deleteCoinRechargePackage(@RequestParam("id") Long id) {
        coinRechargePackageService.deleteCoinRechargePackage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得算力充值套餐")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pay:coin-recharge-package:query')")
    public CommonResult<CoinRechargePackageRespVO> getCoinRechargePackage(@RequestParam("id") Long id) {
        AiPayCoinRechargePackageDO coinRechargePackage = coinRechargePackageService.getCoinRechargePackage(id);
        return success(PayCoinRechargePackageConvert.INSTANCE.convert(coinRechargePackage));
    }

    @GetMapping("/page")
    @Operation(summary = "获得算力充值套餐分页")
    @PreAuthorize("@ss.hasPermission('pay:coin-recharge-package:query')")
    public CommonResult<PageResult<CoinRechargePackageRespVO>> getCoinRechargePackagePage(@Valid CoinRechargePackagePageReqVO pageVO) {
        PageResult<AiPayCoinRechargePackageDO> pageResult = coinRechargePackageService.getCoinRechargePackagePage(pageVO);
        return success(PayCoinRechargePackageConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/activitiy-list")
    @Operation(summary = "获得算力充值套餐列表-活动套餐")
    @PreAuthorize("@ss.hasPermission('pay:coin-recharge-package:query')")
    public CommonResult<List<CoinRechargePackageRespVO>> getCoinRechargePackageList(@RequestParam(value = "type",required = false) Integer type,
                                                                                    @RequestParam("packageType") Integer packageType) {
        List<AiPayCoinRechargePackageDO> list = coinRechargePackageService.getActivityCoinRechargePackageList(type, packageType);
        return success(PayCoinRechargePackageConvert.INSTANCE.convertList(list));
    }
}

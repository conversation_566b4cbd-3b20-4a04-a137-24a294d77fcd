package cn.iocoder.yudao.module.pay.service.coin;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.coin.PayCoinPageReqVO;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinDO;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinTransactionDO;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;

/**
 * 钱包 Service 接口
 *
 * <AUTHOR>
 */
public interface AiPayCoinService {

    /**
     * 获取钱包信息
     * <p>
     * 如果不存在，则创建钱包。由于用户注册时候不会创建钱包
     *
     * @param userId   用户编号
     */
    AiPayCoinDO getOrCreateCoin(Long userId);

    /**
     * 获取钱包信息
     *
     * @param coinId 算力 id
     */
    AiPayCoinDO getCoin(Long coinId);

    /**
     * 获得会员钱包分页
     *
     * @param pageReqVO 分页查询
     * @return 会员钱包分页
     */
    PageResult<AiPayCoinDO> getCoinPage(PayCoinPageReqVO pageReqVO);

    /**
     * 钱包订单支付
     *
     * @param userId     用户 id
     * @param userType   用户类型
     * @param outTradeNo 外部订单号
     * @param price      金额
     */
    AiPayCoinTransactionDO orderPay(Long userId, Integer userType, String outTradeNo, Integer price);

    /**
     * 钱包订单支付退款
     *
     * @param outRefundNo 外部退款号
     * @param refundPrice 退款金额
     * @param reason      退款原因
     */
    AiPayCoinTransactionDO orderRefund(String outRefundNo, Integer refundPrice, String reason);

    /**
     * 扣减算力包余额
     *
     * @param walletId 钱包 id
     * @param bizId    业务关联 id
     * @param bizType  业务关联分类
     * @param coin    扣减金额
     * @return 算力流水
     */
    AiPayCoinTransactionDO reduceCoinBalance(Long walletId, Long bizId,
                                               PayCoinBizTypeEnum bizType, Integer coin);

    /**
     * 增加钱包余额
     *
     * @param walletId 钱包 id
     * @param bizId    业务关联 id
     * @param bizType  业务关联分类
     * @param totalCoin    增加算力
     * @return 钱包流水
     */
    AiPayCoinTransactionDO addCoinBalance(Long walletId, String bizId,
                                          PayCoinBizTypeEnum bizType,Integer price, Integer totalCoin,String packageName);

    /**
     * 冻结钱包部分余额
     *
     * @param id    钱包编号
     * @param price 冻结金额
     */
    void freezePrice(Long id, Integer price);

    /**
     * 解冻钱包余额
     *
     * @param id    钱包编号
     * @param price 解冻金额
     */
    void unfreezePrice(Long id, Integer price);

    /**
     * 获取用户算力记录
     * @param userId  用户ID
     * @param bizTypeEnum 业务类型枚举
     * @Param bizId   业务编号
     * @Param MemberCoinBizTypeEnum 会员算力业务类型枚举
     * @return
     */
    void sendCoinByRegister(Long userId, int coin, PayCoinBizTypeEnum bizTypeEnum, String bizId);
}

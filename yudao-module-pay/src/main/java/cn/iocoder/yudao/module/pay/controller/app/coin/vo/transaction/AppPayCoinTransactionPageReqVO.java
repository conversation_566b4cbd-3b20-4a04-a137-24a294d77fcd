package cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 算力流水分页 Request VO")
@Data
public class AppPayCoinTransactionPageReqVO extends PageParam {

    /**
     * 类型 - 购买
     */
    public static final Integer TYPE_BUY = 1;
    /**
     * 类型 - 消耗
     */
    public static final Integer TYPE_CONSUME = 2;
    /**
     * 类型 - 退回
     */
    public static final Integer TYPE_REFUND = 3;

    @Schema(description = "类型",  example = " 1 购买 2 消耗 3 退回")
    private Integer type;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}

package cn.iocoder.yudao.module.pay.service.coin;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pay.controller.admin.coin.vo.transaction.PayCoinTransactionPageReqVO;
import cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction.AppPayCoinTransactionPageReqVO;
import cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction.AppPayCoinTransactionSummaryRespVO;
import cn.iocoder.yudao.module.pay.convert.coin.PayCoinTransactionConvert;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinDO;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinTransactionDO;
import cn.iocoder.yudao.module.pay.dal.mysql.coin.PayCoinTransactionMapper;
import cn.iocoder.yudao.module.pay.dal.redis.no.PayNoRedisDAO;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;
import cn.iocoder.yudao.module.pay.service.coin.bo.CoinTransactionCreateReqBO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction.AppPayCoinTransactionPageReqVO.TYPE_BUY;
import static cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction.AppPayCoinTransactionPageReqVO.TYPE_CONSUME;

/**
 * 钱包流水 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class AiPayCoinTransactionServiceImpl implements AiPayCoinTransactionService {

    /**
     * 钱包流水的 no 前缀
     */
    private static final String COIN_NO_PREFIX = "C";

    @Resource
    private AiPayCoinService payCoinService;
    @Resource
    private PayCoinTransactionMapper transactionMapper;
    @Resource
    private PayNoRedisDAO noRedisDAO;

    @Override
    public PageResult<AiPayCoinTransactionDO> getCoinTransactionPage(Long userId,
                                                                     AppPayCoinTransactionPageReqVO pageVO) {
        AiPayCoinDO coin = payCoinService.getOrCreateCoin(userId);
        return transactionMapper.selectPage(coin.getId(), pageVO.getType(), pageVO, pageVO.getCreateTime());
    }

    @Override
    public PageResult<AiPayCoinTransactionDO> getCoinTransactionPage(PayCoinTransactionPageReqVO pageVO) {
        // 基于 userId + userType 查询钱包
        if (pageVO.getCoinId() == null
            && ObjectUtil.isAllNotEmpty(pageVO.getUserId(), pageVO.getUserType())) {
            AiPayCoinDO Coin = payCoinService.getOrCreateCoin(pageVO.getUserId());
            if (Coin != null) {
                pageVO.setCoinId(Coin.getId());
            }
        }

        // 查询分页
        return transactionMapper.selectPage(pageVO.getCoinId(), null, pageVO, null);
    }

    @Override
    public AiPayCoinTransactionDO createCoinTransaction(CoinTransactionCreateReqBO bo) {
        AiPayCoinTransactionDO transaction = PayCoinTransactionConvert.INSTANCE.convert(bo)
                .setNo(noRedisDAO.generate(COIN_NO_PREFIX));
        transactionMapper.insert(transaction);
        return transaction;
    }

    @Override
    public AiPayCoinTransactionDO getCoinTransactionByNo(String no) {
        return transactionMapper.selectByNo(no);
    }

    @Override
    public AiPayCoinTransactionDO getCoinTransaction(String bizId, PayCoinBizTypeEnum type) {
        return transactionMapper.selectByBiz(bizId, type.getType());
    }

    @Override
    public AppPayCoinTransactionSummaryRespVO getCoinTransactionSummary(Long userId, Integer userType, LocalDateTime[] createTime) {
        AiPayCoinDO Coin = payCoinService.getOrCreateCoin(userId);
        return new AppPayCoinTransactionSummaryRespVO()
                .setTotalExpense(transactionMapper.selectPriceSum(Coin.getId(), TYPE_BUY, createTime))
                .setTotalIncome(transactionMapper.selectPriceSum(Coin.getId(), TYPE_CONSUME, createTime));
    }

    @Override
    public List<AiPayCoinTransactionDO> getCoinTransactionByUserIdAndBizType(Long userId, PayCoinBizTypeEnum bizTypeEnum) {
        //获取算力
        AiPayCoinDO coinDO = payCoinService.getOrCreateCoin(userId);
        List<AiPayCoinTransactionDO> list = transactionMapper.selectList(new LambdaQueryWrapper<AiPayCoinTransactionDO>()
                .eq(AiPayCoinTransactionDO::getCoinId, coinDO.getId())
                .eq(AiPayCoinTransactionDO::getBizType, bizTypeEnum.getType()));
        return list;
    }

}

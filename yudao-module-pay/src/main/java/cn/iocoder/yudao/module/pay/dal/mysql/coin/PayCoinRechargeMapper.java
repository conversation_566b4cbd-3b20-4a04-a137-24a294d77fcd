package cn.iocoder.yudao.module.pay.dal.mysql.coin;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinRechargeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PayCoinRechargeMapper extends BaseMapperX<AiPayCoinRechargeDO> {

    default int updateByIdAndPaid(Long id, boolean wherePayStatus, AiPayCoinRechargeDO updateObj) {
        return update(updateObj, new LambdaQueryWrapperX<AiPayCoinRechargeDO>()
                .eq(AiPayCoinRechargeDO::getId, id).eq(AiPayCoinRechargeDO::getPayStatus, wherePayStatus));
    }

    default int updateByIdAndRefunded(Long id, Integer whereRefundStatus, AiPayCoinRechargeDO updateObj) {
        return update(updateObj, new LambdaQueryWrapperX<AiPayCoinRechargeDO>()
                .eq(AiPayCoinRechargeDO::getId, id).eq(AiPayCoinRechargeDO::getRefundStatus, whereRefundStatus));
    }

    default PageResult<AiPayCoinRechargeDO> selectPage(PageParam pageReqVO, Long CoinId, Boolean payStatus) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<AiPayCoinRechargeDO>()
                .eq(AiPayCoinRechargeDO::getCoinId, CoinId)
                .eq(AiPayCoinRechargeDO::getPayStatus, payStatus)
                .orderByDesc(AiPayCoinRechargeDO::getId));
    }

    default List<AiPayCoinRechargeDO> getCoinRechargeListByCoinIdPayStatusType(Boolean payStatus,Long coinId, Integer type){
        return selectList(new LambdaQueryWrapperX<AiPayCoinRechargeDO>()
                .eq(AiPayCoinRechargeDO::getCoinId, coinId)
                .eq(AiPayCoinRechargeDO::getPayStatus, payStatus)
                .eq(AiPayCoinRechargeDO::getType, type));
    }
}
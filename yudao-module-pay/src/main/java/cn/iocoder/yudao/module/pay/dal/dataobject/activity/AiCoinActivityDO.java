package cn.iocoder.yudao.module.pay.dal.dataobject.activity;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 算力活动 DO
 *
 */
@Table(name = "ai_coin_activity")
@Comment(value = "AI 算力活动 DO")
@Entity
@TableName("ai_coin_activity")
@KeySequence("ai_coin_activity_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCoinActivityDO extends TenantBaseDO {

    /**
     * 活动编号
     */
    @Id
    @Comment(value = "编号")
    @GeneratedValue(strategy = GenerationType.IDENTITY)//自增主键
    @TableId
    private Long id;

    /**
     * 苹果套餐ID
     */
    @Column(length = 64, columnDefinition = "varchar(64) COMMENT '苹果套餐ID'")
    private String applePackageId;

    /**
     * 活动名称
     */
    @Column(columnDefinition = "varchar(255) NOT NUll COMMENT '活动名称'")
    private String name;

    /**
     * 支付金额
     */
    @Column(columnDefinition = "int NOT NULL COMMENT '支付金额'")
    private Integer payPrice;

    /**
     * 算力值
     */
    @Column(columnDefinition = "int NOT NULL COMMENT '算力值'")
    private Integer coin;

    /**
     * 赠送算力值
     */
    @Column(columnDefinition = "int NOT NULL DEFAULT 0 COMMENT '赠送算力值'")
    private Integer bonusCoin;

    /**
     * 活动状态
     *
     * 枚举 {@link CommonStatusEnum 对应的类}
     */
    @Column(columnDefinition = "int COMMENT '活动状态'")
    private Integer status;

    /**
     * 标签
     */
    @Column(columnDefinition = "varchar(255) COMMENT '标签'")
    private String label;
    /**
     * 备注
     */
    @Column(columnDefinition = "varchar(255) COMMENT '备注'")
    private String remark;

    /**
     * 描述
     */
    @Column(columnDefinition = "varchar(255) COMMENT '描述'")
    private String description;

    /**
     * 活动开始时间
     */
    @Column(columnDefinition = "datetime COMMENT '活动开始时间'")
    private LocalDateTime startTime;
    /**
     * 活动结束时间
     */
    @Column(columnDefinition = "datetime COMMENT '活动结束时间'")
    private LocalDateTime endTime;
    /**
     * 排序 参与顺序升序
     */
    @Column(columnDefinition = "int COMMENT '排序 参与顺序升序'")
    private Integer sort;

    /**
     * 是否限购
     * 枚举 {@link CommonStatusEnum 对应的类}
     */
    @Column(columnDefinition = "int COMMENT '是否限购'")
    private Integer limitBy;

    /**
     * 限购数量
     */
    @Column(columnDefinition = "int COMMENT '限购数量'")
    private Integer limitCount;

    /**
     * 限购时长
     */
    @Column(columnDefinition = "int COMMENT '限购时长'")
    private Integer limitTime;

    /**
     * 设备类型 0安卓 1苹果
     */
    @Column(columnDefinition = "int COMMENT '设备类型 0安卓 1苹果'")
    private Integer deviceType;

    /**
     * 套餐类型
     */
    @Column(columnDefinition = "int COMMENT '套餐类型'")
    private Integer packageType;
}

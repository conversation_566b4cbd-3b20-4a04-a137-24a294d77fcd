package cn.iocoder.yudao.module.pay.dal.mysql.coin;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.pay.dal.dataobject.coin.AiPayCoinTransactionDO;
import cn.iocoder.yudao.module.pay.enums.coin.PayCoinBizTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.iocoder.yudao.module.pay.controller.app.coin.vo.transaction.AppPayCoinTransactionPageReqVO.*;

@Mapper
public interface PayCoinTransactionMapper extends BaseMapperX<AiPayCoinTransactionDO> {

    default PageResult<AiPayCoinTransactionDO> selectPage(Long CoinId, Integer type,
                                                          PageParam pageParam, LocalDateTime[] createTime) {
        LambdaQueryWrapperX<AiPayCoinTransactionDO> query = new LambdaQueryWrapperX<AiPayCoinTransactionDO>()
                .eqIfPresent(AiPayCoinTransactionDO::getCoinId, CoinId);
        if (Objects.equals(type, TYPE_BUY)) {
            query.in(AiPayCoinTransactionDO::getBizType, PayCoinBizTypeEnum.RECHARGE.getType());
        } else if (Objects.equals(type, TYPE_CONSUME)) {
            query.eq(AiPayCoinTransactionDO::getBizType, PayCoinBizTypeEnum.PAYMENT.getType());
        } else if (Objects.equals(type, TYPE_REFUND)) {
            query.in(AiPayCoinTransactionDO::getBizType, PayCoinBizTypeEnum.PAYMENT_REFUND.getType(), PayCoinBizTypeEnum.RECHARGE_REFUND.getType(),PayCoinBizTypeEnum.PAYMENT_REFUND_ITEM.getType());
        }
        query.betweenIfPresent(AiPayCoinTransactionDO::getCreateTime, createTime);
        query.orderByDesc(AiPayCoinTransactionDO::getId);
        return selectPage(pageParam, query);
    }

    default Integer selectPriceSum(Long coinId, Integer type, LocalDateTime[] createTime) {
        // SQL sum 查询
        List<Map<String, Object>> result = selectMaps(new QueryWrapperX<AiPayCoinTransactionDO>()
                .select("SUM(coin) AS coinSum")
                .gt(Objects.equals(type, TYPE_BUY), "coin", 0) // 收入
                .lt(Objects.equals(type, TYPE_CONSUME), "coin", 0) // 支出
                .eq("coin_id", coinId)
                .between("create_time", createTime[0], createTime[1]));
        // 获得 sum 结果
        Map<String, Object> first = CollUtil.getFirst(result);
        return MapUtil.getInt(first, "coinSum", 0);
    }

    default AiPayCoinTransactionDO selectByNo(String no) {
        return selectOne(AiPayCoinTransactionDO::getNo, no);
    }

    default AiPayCoinTransactionDO selectByBiz(String bizId, Integer bizType) {
        return selectOne(AiPayCoinTransactionDO::getBizId, bizId,
                AiPayCoinTransactionDO::getBizType, bizType);
    }

}





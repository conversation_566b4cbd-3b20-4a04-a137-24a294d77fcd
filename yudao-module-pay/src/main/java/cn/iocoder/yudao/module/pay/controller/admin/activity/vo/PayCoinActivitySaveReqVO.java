package cn.iocoder.yudao.module.pay.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 算力活动 DO新增/修改 Request VO")
@Data
public class PayCoinActivitySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11838")
    private Long id;

    @Schema(description = "活动结束时间")
    private LocalDateTime endTime;

    @Schema(description = "限购数量", example = "21713")
    private Integer limitCount;

    @Schema(description = "是否限购")
    private Integer limitBy;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "蛇年特惠")
    @NotEmpty(message = "活动名称不能为空")
    private String name;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "贺新春·大吉大利")
    private String label;

    @Schema(description = "备注", example = "每个用户限购1次")
    private String remark;

    @Schema(description = "排序 参与顺序升序")
    private Integer sort;

    @Schema(description = "活动开始时间")
    private LocalDateTime startTime;

    @Schema(description = "活动状态", example = "2")
    private Integer status;

    @Schema(description = "设备类型 0安卓 1苹果", example = "1")
    private Integer deviceType;

    @Schema(description = "限购时长")
    private Integer limitTime;

    @Schema(description = "苹果套餐ID", example = "1")
    private String applePackageId;

    @Schema(description = "描述", example = "限购1次，超值！")
    private String description;

    @Schema(description = "支付金额", example = "1")
    private Integer payPrice;

    @Schema(description = "算力值", example = "1")
    private Integer coin;

    @Schema(description = "赠送算力值", example = "1")
    private Integer bonusCoin;

    /**
     * 套餐类型
     */
    @Schema(description = "套餐类型")
    private Integer packageType;
}
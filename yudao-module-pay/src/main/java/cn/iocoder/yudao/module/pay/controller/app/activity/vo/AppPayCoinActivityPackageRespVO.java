package cn.iocoder.yudao.module.pay.controller.app.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: TODO
 * @Date: 2025/1/16 21:22
 * @Author: zhangq
 * @Version: 1.0
 */
@Schema(description = "用户 APP - 用户活动充值算力套餐 Response VO")
@Data
public class AppPayCoinActivityPackageRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "苹果支付套餐ID")
    private String applePackageId;
    /**
     * 套餐类型
     */
    @Schema(description = "套餐类型(0安卓 1苹果)")
    private Integer type;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "小套餐")
    private String name;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer payPrice;

    /**
     * 算力
     */
    @Schema(description = "算力", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer coin;

    @Schema(description = "赠送算力值", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    private Integer bonusCoin;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private String label;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 活动剩余时间
     */
    @Schema(description = "活动剩余时间")
    private String endTime;
}
